syncTask
```json
{
    "app": "ee",
    "bizCode": "884a08fae11e4b458caae99cd9722018",
    "extend": [
        {
            "content": "3",
            "tips": "条",
            "type": "text"
        }
    ],
    "content": "http://jd.com",
    "mobileContent": "jdme://web/?url=null",
    "openUserId": "8c48b6a3d6574af7c13e5f56eeee0bc3",
    "ownerList": [
        {
            "openUserId": "8c48b6a3d6574af7c13e5f56eeee0bc3",
            "taskUserRole": "OWNER"
        }
    ],
    "sourceId": "ces_count_111",
    "title": "ces_count_111",
    "remark": "这是任务背景",
    "startTime": 1641892247344,
    "endTime": 1641892247344,
    "sysProjectId": "xxxxx",
    "sysGroupId": "xxxxx",
    "sourceDescZh": "xxxxx",
    "sourceDescEn": "xxxxx",
    "sourceTrusteeship": 0
}
```

updateTask
```json
{
    "taskId": "string",
    "title": "string",
    "remark": "string",
    "startTime": 0,
    "endTime": 0,
    "priorityType": 0,
    "content": "string",
    "mobileContent": "string",
    "extend": "string",
    "sysProjectId": "xxxxx",
    "sysGroupId": "xxxxx"
}
```

updateTaskStatus
```json
{
    "openUserId": "string",
    "taskId": "string",
    "taskStatus": 0
}
```