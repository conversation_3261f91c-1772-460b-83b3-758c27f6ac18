package com.jdl.mcp.server.openme.timline;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.jdl.mcp.server.McpPorterApplication;
import com.jdl.mcp.server.openme.timline.param.SendRobotMsgParam;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = McpPorterApplication.class)
@TestPropertySource(properties = {
    // 这些属性会覆盖配置文件中的属性，用于测试
    "openme.app.host=https://openme.jd.com",
    "openme.app.key=JIAOYI-FDM",
    "openme.app.secret=FE253BA8F75A634F",
    "openme.app.teamId=00046419",
    "openme.app.openTeamId=15441406156a21eddea382ea3c2c2fbe",
    "openme.timline.appId=JIAOYI-FDM",
    "openme.timline.tenantId=CN.JD.GROUP",
    "openme.timline.robotId=00_55e779bf635b4f14",
    "openme.joyspace.scene=JDLTradeAi",
    "openme.joyspace.userId=org.wljy1",
    "openme.joyspace.teamId=JuJpAXsTgawHjpow9r75Z"
})
public class TimlineServiceTest {

    private static final Logger logger= LoggerFactory.getLogger(TimlineServiceTest.class);
    private static final ObjectMapper objectMapper = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT);

    // mvn test -Dtest=com.jdl.mcp.server.openme.timline.TimlineServiceTest
    @Autowired
    private TimlineService timlineService;

    private SendRobotMsgParam param;

    private static final String ERP = "bjliandahu"; // 替换为实际测试用户的ERP
    private static final String Content = "这是一条来自mcp 平台的测试消息"; //
    private static final String App = "ee"; //
    private static final String GroupId = "10216688676"; // 替换为实际测试用户的PIN


    @BeforeEach
    public void setUp() {
        // 初始化发送机器人消息的参数
        param = new SendRobotMsgParam();

        // 设置接收消息的用户ERP
        param.setErp(ERP); // 替换为实际测试用户的ERP

        // 创建消息参数
        SendRobotMsgParam.RobotMsgParams msgParams = new SendRobotMsgParam.RobotMsgParams();

        // 创建文本消息体
        SendRobotMsgParam.TextMsgBody textBody = new SendRobotMsgParam.TextMsgBody();
        textBody.setContent(Content);

        msgParams.setBody(textBody);
        param.setParams(msgParams);
    }

    @Test
    public void testSendTextMessage() {
        // 发送文本消息
        JSONObject response = timlineService.sendRobotMsg(param);

        // 验证响应
        assertNotNull(response);
        System.out.println("Response: " + response.toString());

        // 检查响应码，0表示成功
        // 注意：实际测试中，如果配置不正确或网络问题，可能会失败
        // 这里我们只验证响应不为空
    }

    @Test
    public void testSendAtMessage() {
        // 创建@用户消息
        SendRobotMsgParam.TextMsgBody textBody = (SendRobotMsgParam.TextMsgBody) param.getParams().getBody();

        // 设置@用户列表
        List<SendRobotMsgParam.AtUser> atUsers = new ArrayList<>();
        SendRobotMsgParam.AtUser atUser = new SendRobotMsgParam.AtUser();
        atUser.setApp(App);
        atUser.setPin(ERP); // 替换为实际测试用户的PIN
        atUser.setNickname("连大湖");
        atUsers.add(atUser);

        textBody.setAtUsers(atUsers);
        textBody.setContent("@连大湖 这是一条@消息测试");

        try {
            logger.info("创建@用户消息:" + " ====  " + objectMapper.writeValueAsString(param));
        } catch (JsonProcessingException e) {
        }
        // 发送@消息
        JSONObject response = timlineService.sendRobotMsg(param);

        // 验证响应
        assertNotNull(response);
        System.out.println("At Message Response: " + response.toString());
    }

    @Test
    public void testSendGroupMessage() {
        // 创建一个新的参数对象，避免影响其他测试
        SendRobotMsgParam groupParam = new SendRobotMsgParam();

        // 设置群组ID
        groupParam.setGroupId(GroupId); // 替换为实际的群组ID

        // 创建消息参数
        SendRobotMsgParam.RobotMsgParams msgParams = new SendRobotMsgParam.RobotMsgParams();

        // 创建文本消息体
        SendRobotMsgParam.TextMsgBody textBody = new SendRobotMsgParam.TextMsgBody();
        textBody.setContent("这是一条群组测试消息");

        msgParams.setBody(textBody);
        groupParam.setParams(msgParams);

        try {
            logger.info("群组:" + " ====  " + objectMapper.writeValueAsString(groupParam));
        } catch (JsonProcessingException e) {
        }
        // 发送群组消息
        JSONObject response = timlineService.sendRobotMsg(groupParam);

        // 验证响应
        assertNotNull(response);
        System.out.println("Group Message Response: " + response.toString());
    }

    @Test
    public void testSendAtAllMessage() {
        // 创建一个新的参数对象，避免影响其他测试
        SendRobotMsgParam atAllParam = new SendRobotMsgParam();

        // 设置群组ID
        atAllParam.setGroupId(GroupId); // 替换为实际的群组ID

        // 创建消息参数
        SendRobotMsgParam.RobotMsgParams msgParams = new SendRobotMsgParam.RobotMsgParams();

        // 创建文本消息体
        SendRobotMsgParam.TextMsgBody textBody = new SendRobotMsgParam.TextMsgBody();

        // 设置@全体成员
        List<SendRobotMsgParam.AtUser> atUsers = new ArrayList<>();
        SendRobotMsgParam.AtUser atUser = new SendRobotMsgParam.AtUser();
        atUser.setApp("ee");
        atUser.setPin("all");
        atUser.setNickname("全体成员");
        atUsers.add(atUser);

        textBody.setAtUsers(atUsers);
        textBody.setContent("@全体成员 这是一条全体成员通知");

        msgParams.setBody(textBody);
        atAllParam.setParams(msgParams);

        try {
            logger.info("发送@全体成员消息:" + " ====  " + objectMapper.writeValueAsString(atAllParam));
        } catch (JsonProcessingException e) {
        }
        // 发送@全体成员消息
        JSONObject response = timlineService.sendRobotMsg(atAllParam);

        // 验证响应
        assertNotNull(response);
        System.out.println("At All Message Response: " + response.toString());
    }
}
