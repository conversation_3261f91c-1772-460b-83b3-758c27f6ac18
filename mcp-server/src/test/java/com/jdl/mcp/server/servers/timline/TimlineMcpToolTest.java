package com.jdl.mcp.server.servers.timline;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.jdl.mcp.server.McpPorterApplication;
import com.jdl.mcp.server.servers.timline.TimlineMcpTool.SendMessageParams;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

//mvn test -Dtest=TimlineMcpToolTest
@SpringBootTest(classes = McpPorterApplication.class)
@ActiveProfiles("test")
class TimlineMcpToolTest {
    
    @Autowired
    private TimlineMcpTool timline;

    @Test
    void testSendMessage() {
        // 准备测试数据
        SendMessageParams params = new SendMessageParams();
        params.setReceiver("bjliandahu");
        params.setMessageContent("这是来自mcp 平台单元测试的消息内容");

        // 执行测试
        Map<String, Object> result = timline.sendMessage(params);

        // 验证结果
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertNotNull(result.get("jsonData"));
    }

} 