package com.jdl.mcp.server.openme.timline;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.jdl.mcp.server.McpPorterApplication;
import com.jdl.mcp.server.openme.timline.param.SendJUEMsgParam;

import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = McpPorterApplication.class)
@TestPropertySource(properties = {
    // 这些属性会覆盖配置文件中的属性，用于测试
    "openme.app.host=https://openme.jd.com",
    "openme.app.key=JIAOYI-FDM",
    "openme.app.secret=FE253BA8F75A634F",
    "openme.app.teamId=00046419",
    "openme.app.openTeamId=15441406156a21eddea382ea3c2c2fbe",
    "openme.timline.appId=JIAOYI-FDM",
    "openme.timline.tenantId=CN.JD.GROUP",
    "openme.timline.robotId=00_55e779bf635b4f14",
    "openme.joyspace.scene=JDLTradeAi",
    "openme.joyspace.userId=org.wljy1",
    "openme.joyspace.teamId=JuJpAXsTgawHjpow9r75Z"
})
public class TimlineJUEMsgServiceTest {

    private static final Logger log = LoggerFactory.getLogger(TimlineJUEMsgServiceTest.class);
    private static final ObjectMapper objectMapper = new ObjectMapper().enable(SerializationFeature.INDENT_OUTPUT);

    private static final String ERP = "bjliandahu"; // 替换为实际测试用户的ERP
    private static final String App = "ee"; //
    private static final String GroupId = "10216688676"; // 替换为实际测试用户的PIN码

    // mvn test -Dtest=com.jdl.mcp.server.openme.timline.TimlineJUEMsgServiceTest
    @Autowired
    private TimlineService timlineService;

    private SendJUEMsgParam param;

    /**
     * 将对象转换为格式化的JSON字符串
     */
    private String toJsonString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert object to JSON string", e);
            return obj.toString();
        }
    }

    @BeforeEach
    public void setUp() {
        // 初始化发送互动卡片消息的参数
        param = new SendJUEMsgParam();

        // 设置接收消息的用户ERP
        param.setErp(ERP); // 替换为实际测试用户的ERP

        // 创建消息参数
        SendJUEMsgParam.JUEMsgParams msgParams = new SendJUEMsgParam.JUEMsgParams();
        SendJUEMsgParam.JUEMsgData msgData = new SendJUEMsgParam.JUEMsgData();

        // 设置模板ID和会话类型
        msgData.setTemplateId("templateMsgCard"); // 使用标准模板
        msgData.setSessionType(1); // 设置会话类型

        // 设置卡片数据
        Map<String, Object> cardData = new HashMap<>();
        cardData.put("title", "测试卡片标题");
        cardData.put("content", "这是一条测试卡片内容这是一条测试卡片内容这是一条测试卡片内容这是一条测试卡片内容这是一条测试卡片内容");
        msgData.setCardData(cardData);

        // 设置摘要信息
        msgData.setSummary("测试卡片摘要");

        // 设置转发配置
        SendJUEMsgParam.Forward forward = new SendJUEMsgParam.Forward();
        forward.setReload(false);
        Map<String, Object> forwardCardData = new HashMap<>();
        forwardCardData.put("title", "转发卡片标题");
        forwardCardData.put("content", "这是转发卡片内容这是一条测试卡片内容这是一条测试卡片内容这是一条测试卡片内容这是一条测试卡片内容这是一条测试卡片内容");
        forward.setCardData(forwardCardData);
        msgData.setForward(forward);

        // 设置回调数据
        Map<String, Object> callbackData = new HashMap<>();
        callbackData.put("key", "value");
        msgData.setCallbackData(callbackData);

        msgParams.setData(msgData);
        param.setParams(msgParams);
    }

    //@Test
    public void testSendJUEMsg() {
        // 打印入参
        log.info("请求参数: \n{}", toJsonString(param));

        // 发送互动卡片消息
        JSONObject response = timlineService.sendJUEMsg(param);

        // 验证响应
        assertNotNull(response);

        // 打印出参
        try {
            log.info("响应结果: \n{}", response.toString(4));
        } catch (JSONException e) {
            log.info("响应结果: \n{}", response.toString());
        }

        // 检查响应码，0表示成功
        // 注意：实际测试中，如果配置不正确或网络问题，可能会失败
        // 这里我们只验证响应不为空
    }

    //@Test
    public void testSendJUEMsgWithAt() {
        // 创建@用户消息
        SendJUEMsgParam.JUEMsgData msgData = param.getParams().getData();

        // 设置@用户
        SendJUEMsgParam.At at = new SendJUEMsgParam.At();
        at.setAtAll(false);

        List<SendJUEMsgParam.AtUser> users = new ArrayList<>();
        SendJUEMsgParam.AtUser atUser = new SendJUEMsgParam.AtUser();
        atUser.setApp(App);
        atUser.setPin(ERP); // 替换为实际测试用户的PIN
        users.add(atUser);

        at.setUsers(users);
        msgData.setAt(at);

        // 打印入参
        log.info("@用户请求参数: \n{}", toJsonString(param));

        // 发送@用户互动卡片消息
        JSONObject response = timlineService.sendJUEMsg(param);

        // 验证响应
        assertNotNull(response);

        // 打印出参
        try {
            log.info("@用户响应结果: \n{}", response.toString(4));
        } catch (JSONException e) {
            log.info("@用户响应结果: \n{}", response.toString());
        }
    }

    //@Test
    public void testSendJUEMsgToGroup() {
        // 创建一个新的参数对象，避免影响其他测试
        SendJUEMsgParam groupParam = new SendJUEMsgParam();

        // 设置群组ID
        groupParam.setGroupId(GroupId); // 替换为实际的群组ID

        // 创建消息参数
        SendJUEMsgParam.JUEMsgParams msgParams = new SendJUEMsgParam.JUEMsgParams();
        SendJUEMsgParam.JUEMsgData msgData = new SendJUEMsgParam.JUEMsgData();

        // 设置模板ID和会话类型
        msgData.setTemplateId("templateMsgCard"); // 使用标准模板
        msgData.setSessionType(1); // 设置会话类型

        // 设置卡片数据
        Map<String, Object> cardData = new HashMap<>();
        cardData.put("title", "群组测试卡片标题");
        cardData.put("content", "这是一条发送到群组的测试卡片内容");
        msgData.setCardData(cardData);

        // 设置摘要信息
        msgData.setSummary("群组测试卡片摘要");

        msgParams.setData(msgData);
        groupParam.setParams(msgParams);

        // 打印入参
        log.info("群组请求参数: \n{}", toJsonString(groupParam));

        // 发送群组互动卡片消息
        JSONObject response = timlineService.sendJUEMsg(groupParam);

        // 验证响应
        assertNotNull(response);

        // 打印出参
        try {
            log.info("群组响应结果: \n{}", response.toString(4));
        } catch (JSONException e) {
            log.info("群组响应结果: \n{}", response.toString());
        }
    }

    //@Test
    public void testSendJUEMsgWithAtAll() {
        // 创建一个新的参数对象，避免影响其他测试
        SendJUEMsgParam atAllParam = new SendJUEMsgParam();

        // 设置群组ID
        atAllParam.setGroupId(GroupId); // 替换为实际的群组ID

        // 创建消息参数
        SendJUEMsgParam.JUEMsgParams msgParams = new SendJUEMsgParam.JUEMsgParams();
        SendJUEMsgParam.JUEMsgData msgData = new SendJUEMsgParam.JUEMsgData();

        // 设置模板ID和会话类型
        msgData.setTemplateId("templateMsgCard"); // 使用标准模板
        msgData.setSessionType(1); // 设置会话类型

        // 设置卡片数据
        Map<String, Object> cardData = new HashMap<>();
        cardData.put("title", "@全体成员测试卡片标题");
        cardData.put("content", "这是一条@全体成员的测试卡片内容");
        msgData.setCardData(cardData);

        // 设置摘要信息
        msgData.setSummary("@全体成员测试卡片摘要");

        // 设置@全体成员
        SendJUEMsgParam.At at = new SendJUEMsgParam.At();
        at.setAtAll(true);
        msgData.setAt(at);

        msgParams.setData(msgData);
        atAllParam.setParams(msgParams);

        // 打印入参
        log.info("@全体成员请求参数: \n{}", toJsonString(atAllParam));

        // 发送@全体成员互动卡片消息
        JSONObject response = timlineService.sendJUEMsg(atAllParam);

        // 验证响应
        assertNotNull(response);

        // 打印出参
        try {
            log.info("@全体成员响应结果: \n{}", response.toString(4));
        } catch (JSONException e) {
            log.info("@全体成员响应结果: \n{}", response.toString());
        }
    }
    @Test
    public void testSendJUEMsgWithExampleData() {
        // 创建一个新的参数对象
        SendJUEMsgParam exampleParam = new SendJUEMsgParam();

        // 设置接收消息的用户ERP
        exampleParam.setErp(ERP); // 替换为实际的用户ERP

        // 创建消息参数
        SendJUEMsgParam.JUEMsgParams msgParams = new SendJUEMsgParam.JUEMsgParams();
        SendJUEMsgParam.JUEMsgData msgData = new SendJUEMsgParam.JUEMsgData();

        // 设置模板ID和会话类型
        msgData.setTemplateId("templateMsgCard");
        msgData.setSessionType(1);
        msgData.setTemplateType(1);
        msgData.setReload(false);
        msgData.setSummary("");

        // 设置卡片数据，完全按照示例结构
        Map<String, Object> cardData = new HashMap<>();

        // 创建 header
        Map<String, Object> header = new HashMap<>();
        header.put("theme", "red");

        Map<String, Object> title = new HashMap<>();
        title.put("content", "京东价值观");
        title.put("tag", "plain_text");

        header.put("title", title);
        cardData.put("header", header);

        // 创建 elements
        List<Map<String, Object>> elements = new ArrayList<>();

        // 第一个元素：图片
        Map<String, Object> imgElement = new HashMap<>();
        imgElement.put("preview", true);
        imgElement.put("img_url", "https://apijoyspace.jd.com/v1/files/IrFPuO2K5qBbUjGA0LiP/link");
        imgElement.put("scale", 2.3);
        imgElement.put("tag", "img");
        elements.add(imgElement);

        // 第二个元素：分隔线
        Map<String, Object> hrElement = new HashMap<>();
        hrElement.put("tag", "hr");
        elements.add(hrElement);

        // 第三个元素：页脚
        Map<String, Object> footerElement = new HashMap<>();
        footerElement.put("tag", "footer");
        footerElement.put("content", "来自MCP 平台");
        elements.add(footerElement);

        cardData.put("elements", elements);
        msgData.setCardData(cardData);

        // 设置转发配置
        SendJUEMsgParam.Forward forward = new SendJUEMsgParam.Forward();
        forward.setReload(false);
        Map<String, Object> forwardCardData = new HashMap<>();
        forwardCardData.put("key", "value");
        forward.setCardData(forwardCardData);
        msgData.setForward(forward);

        // 设置回调数据
        Map<String, Object> callbackData = new HashMap<>();
        callbackData.put("key", "value");
        msgData.setCallbackData(callbackData);

        msgParams.setData(msgData);
        exampleParam.setParams(msgParams);

        // 设置请求ID和时间戳
        exampleParam.setRequestId(UUID.randomUUID().toString());
        exampleParam.setDateTime(1688558656000L);

        // 打印入参
        log.info("示例数据请求参数: \n{}", toJsonString(exampleParam));

        // 发送互动卡片消息
        JSONObject response = timlineService.sendJUEMsg(exampleParam);

        // 验证响应
        assertNotNull(response);

        // 打印出参
        try {
            log.info("示例数据响应结果: \n{}", response.toString(4));
        } catch (JSONException e) {
            log.info("示例数据响应结果: \n{}", response.toString());
        }
    }
}