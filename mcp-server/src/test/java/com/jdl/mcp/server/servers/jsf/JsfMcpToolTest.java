package com.jdl.mcp.server.servers.jsf;

import com.jd.jsf.open.api.vo.InterfaceInfo;
import com.jdl.mcp.server.jsf.model.JsfResult;
import com.jdl.mcp.server.jsf.service.JsfInterfaceService;
import com.jdl.mcp.server.jsf.service.JsfProviderAliasService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * JSF MCP工具测试类
 */
@ExtendWith(MockitoExtension.class)
class JsfMcpToolTest {

    @Mock
    private JsfInterfaceService jsfInterfaceService;

    @Mock
    private JsfProviderAliasService jsfProviderAliasService;

    @InjectMocks
    private JsfMcpTool jsfMcpTool;

    private static final String TEST_INTERFACE = "com.jd.test.TestService";
    private static final String TEST_OPERATOR = "testOperator";

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    void testGetInterfaceInfoSuccess() {
        // 准备测试数据
        InterfaceInfo interfaceInfo = new InterfaceInfo();
        interfaceInfo.setInterfaceName(TEST_INTERFACE);
        
        JsfResult<InterfaceInfo> mockResult = new JsfResult<>();
        mockResult.setSuccess(true);
        mockResult.setCode(1);
        mockResult.setMsg("success");
        mockResult.setData(interfaceInfo);

        // Mock服务调用
        when(jsfInterfaceService.getByInterfaceName(TEST_INTERFACE, TEST_OPERATOR))
            .thenReturn(mockResult);

        // 创建参数
        JsfMcpTool.GetInterfaceInfoParams params = new JsfMcpTool.GetInterfaceInfoParams();
        params.setInterfaceName(TEST_INTERFACE);
        params.setOperator(TEST_OPERATOR);

        // 执行测试
        Map<String, Object> result = jsfMcpTool.getInterfaceInfo(params);

        // 验证结果
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertEquals(1, result.get("code"));
        assertEquals("success", result.get("message"));
        assertNotNull(result.get("data"));

        // 验证服务调用
        verify(jsfInterfaceService).getByInterfaceName(TEST_INTERFACE, TEST_OPERATOR);
    }

    @Test
    void testGetInterfaceInfoWithNullParams() {
        // 执行测试
        Map<String, Object> result = jsfMcpTool.getInterfaceInfo(null);

        // 验证结果
        assertNotNull(result);
        assertFalse((Boolean) result.get("success"));
        assertTrue(result.get("error").toString().contains("参数不能为空"));
    }

    @Test
    void testGetMethodListSuccess() {
        // 准备测试数据
        List<String> methodList = Arrays.asList("method1", "method2", "method3");
        
        JsfResult<List<String>> mockResult = new JsfResult<>();
        mockResult.setSuccess(true);
        mockResult.setCode(1);
        mockResult.setMsg("success");
        mockResult.setData(methodList);

        // Mock服务调用
        when(jsfInterfaceService.getMethodList(eq(TEST_INTERFACE), isNull(), isNull(), isNull(), isNull(), eq(TEST_OPERATOR)))
            .thenReturn(mockResult);

        // 创建参数
        JsfMcpTool.GetMethodListParams params = new JsfMcpTool.GetMethodListParams();
        params.setInterfaceName(TEST_INTERFACE);
        params.setOperator(TEST_OPERATOR);

        // 执行测试
        Map<String, Object> result = jsfMcpTool.getMethodList(params);

        // 验证结果
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertEquals(1, result.get("code"));
        assertEquals("success", result.get("message"));
        assertEquals(methodList, result.get("data"));
        assertEquals(3, result.get("total"));

        // 验证服务调用
        verify(jsfInterfaceService).getMethodList(eq(TEST_INTERFACE), isNull(), isNull(), isNull(), isNull(), eq(TEST_OPERATOR));
    }

    @Test
    void testGetMethodInfoSuccess() {
        // 准备测试数据
        String methodInfo = "{\"methods\":[{\"name\":\"testMethod\",\"params\":[]}]}";
        
        JsfResult<String> mockResult = new JsfResult<>();
        mockResult.setSuccess(true);
        mockResult.setCode(1);
        mockResult.setMsg("success");
        mockResult.setData(methodInfo);

        // Mock服务调用
        when(jsfInterfaceService.getMethodInfo(eq(TEST_INTERFACE), isNull(), isNull(), isNull(), eq("testMethod"), eq(TEST_OPERATOR)))
            .thenReturn(mockResult);

        // 创建参数
        JsfMcpTool.GetMethodInfoParams params = new JsfMcpTool.GetMethodInfoParams();
        params.setInterfaceName(TEST_INTERFACE);
        params.setMethodName("testMethod");
        params.setOperator(TEST_OPERATOR);

        // 执行测试
        Map<String, Object> result = jsfMcpTool.getMethodInfo(params);

        // 验证结果
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertEquals(1, result.get("code"));
        assertEquals("success", result.get("message"));
        assertEquals(methodInfo, result.get("data"));

        // 验证服务调用
        verify(jsfInterfaceService).getMethodInfo(eq(TEST_INTERFACE), isNull(), isNull(), isNull(), eq("testMethod"), eq(TEST_OPERATOR));
    }

    @Test
    void testGetAliasByInterfaceNameSuccess() {
        // 准备测试数据
        List<String> aliases = Arrays.asList("alias1", "alias2");
        
        JsfResult<List<String>> mockResult = new JsfResult<>();
        mockResult.setSuccess(true);
        mockResult.setCode(1);
        mockResult.setMsg("success");
        mockResult.setData(aliases);

        // Mock服务调用
        when(jsfProviderAliasService.getAliasByInterfaceName(TEST_INTERFACE, TEST_OPERATOR))
            .thenReturn(mockResult);

        // 创建参数
        JsfMcpTool.GetAliasByInterfaceNameParams params = new JsfMcpTool.GetAliasByInterfaceNameParams();
        params.setInterfaceName(TEST_INTERFACE);
        params.setOperator(TEST_OPERATOR);

        // 执行测试
        Map<String, Object> result = jsfMcpTool.getAliasByInterfaceName(params);

        // 验证结果
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertEquals(1, result.get("code"));
        assertEquals("success", result.get("message"));
        assertEquals(aliases, result.get("data"));
        assertEquals(2, result.get("total"));

        // 验证服务调用
        verify(jsfProviderAliasService).getAliasByInterfaceName(TEST_INTERFACE, TEST_OPERATOR);
    }

    @Test
    void testParameterValidation() {
        // 测试接口名称为空的情况
        JsfMcpTool.GetInterfaceInfoParams params = new JsfMcpTool.GetInterfaceInfoParams();
        params.setOperator(TEST_OPERATOR);
        
        assertThrows(IllegalArgumentException.class, params::validateSelf);

        // 测试操作人为空的情况
        params.setInterfaceName(TEST_INTERFACE);
        params.setOperator(null);
        
        assertThrows(IllegalArgumentException.class, params::validateSelf);

        // 测试正常情况
        params.setOperator(TEST_OPERATOR);
        assertDoesNotThrow(params::validateSelf);
    }
}
