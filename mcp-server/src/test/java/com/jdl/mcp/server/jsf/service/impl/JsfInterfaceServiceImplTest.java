package com.jdl.mcp.server.jsf.service.impl;

import com.jd.jsf.open.api.InterfaceService;
import com.jd.jsf.open.api.vo.request.QueryInterfaceRequest;
import com.jd.jsf.open.api.vo.request.QueryMethodInfoRequest;
import com.jd.jsf.open.api.vo.Result;
import com.jd.jsf.open.api.vo.InterfaceInfo;
import com.jdl.mcp.server.jsf.auth.JsfAuthService;
import com.jdl.mcp.server.jsf.exception.JsfException;
import com.jdl.mcp.server.jsf.model.JsfResult;
import com.jdl.mcp.server.jsf.param.BaseJsfRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * JSF接口服务测试类
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
class JsfInterfaceServiceImplTest {
    
    @Mock
    private JsfAuthService jsfAuthService;
    
    @Mock
    private InterfaceService interfaceService;
    
    @InjectMocks
    private JsfInterfaceServiceImpl jsfInterfaceService;
    
    private BaseJsfRequest mockCommonParams;
    
    @BeforeEach
    void setUp() {
        mockCommonParams = new BaseJsfRequest();
        mockCommonParams.setAppKey("testAppKey");
        mockCommonParams.setOperator("testOperator");
        mockCommonParams.setClientIp("127.0.0.1");
        mockCommonParams.setTimeStamp(System.currentTimeMillis());
        mockCommonParams.setSign("testSign");
    }
    
    @Test
    void testGetByInterfaceNameSuccess() throws Exception {
        // 准备测试数据
        String interfaceName = "com.test.TestService";
        String operator = "testOperator";
        
        // Mock认证服务
        when(jsfAuthService.buildCommonParams(operator)).thenReturn(mockCommonParams);
        
        // Mock JSF API响应
        Result<InterfaceInfo> mockResult = new Result<>();
        mockResult.setCode(1);
        mockResult.setMsg("success");

        InterfaceInfo interfaceInfo = new InterfaceInfo();
        interfaceInfo.setInterfaceName(interfaceName);
        mockResult.setData(interfaceInfo);

        when(interfaceService.getByInterfaceName(any(QueryInterfaceRequest.class))).thenReturn(mockResult);
        
        // 执行测试
        JsfResult<InterfaceInfo> result = jsfInterfaceService.getByInterfaceName(interfaceName, operator);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getCode());
        assertEquals("success", result.getMsg());
        
        InterfaceInfo resultInterfaceInfo = result.getData();
        assertNotNull(resultInterfaceInfo);
        assertEquals(interfaceName, resultInterfaceInfo.getInterfaceName());
        
        // 验证调用
        verify(jsfAuthService).buildCommonParams(operator);
        verify(interfaceService).getByInterfaceName(any(QueryInterfaceRequest.class));
    }
    
    @Test
    void testGetByInterfaceNameWithNullInterfaceName() {
        // 测试空接口名
        assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName(null, "testOperator");
        });
        
        assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName("", "testOperator");
        });
        
        assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName("   ", "testOperator");
        });
    }
    
    @Test
    void testGetByInterfaceNameWithNullOperator() {
        // 测试空操作人
        assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName("com.test.TestService", null);
        });
        
        assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName("com.test.TestService", "");
        });
    }
    
    @Test
    void testGetMethodInfoSuccess() throws Exception {
        // 准备测试数据
        String interfaceName = "com.test.TestService";
        String operator = "testOperator";
        String methodInfo = "{\"methods\":[{\"name\":\"testMethod\",\"params\":[]}]}";
        
        // Mock认证服务
        when(jsfAuthService.buildCommonParams(operator)).thenReturn(mockCommonParams);
        
        // Mock JSF API响应
        Result<String> mockResult = new Result<>();
        mockResult.setCode(1);
        mockResult.setMsg("success");
        mockResult.setData(methodInfo);
        
        when(interfaceService.getMethodInfo(any(QueryMethodInfoRequest.class))).thenReturn(mockResult);
        
        // 执行测试
        JsfResult<String> result = jsfInterfaceService.getMethodInfo(interfaceName, null, null, null, null, operator);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getCode());
        assertEquals("success", result.getMsg());
        assertEquals(methodInfo, result.getData());
        
        // 验证调用
        verify(jsfAuthService).buildCommonParams(operator);
        verify(interfaceService).getMethodInfo(any(QueryMethodInfoRequest.class));
    }
    
    @Test
    void testGetMethodListSuccess() throws Exception {
        // 准备测试数据
        String interfaceName = "com.test.TestService";
        String operator = "testOperator";
        List<String> methodList = Arrays.asList("method1", "method2", "method3");
        
        // Mock认证服务
        when(jsfAuthService.buildCommonParams(operator)).thenReturn(mockCommonParams);
        
        // Mock JSF API响应
        Result<List<String>> mockResult = new Result<>();
        mockResult.setCode(1);
        mockResult.setMsg("success");
        mockResult.setData(methodList);
        
        when(interfaceService.getMethodList(any(QueryMethodInfoRequest.class))).thenReturn(mockResult);
        
        // 执行测试
        JsfResult<List<String>> result = jsfInterfaceService.getMethodList(interfaceName, null, null, null, null, operator);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getCode());
        assertEquals("success", result.getMsg());
        assertEquals(methodList, result.getData());
        assertEquals(3, result.getData().size());
        
        // 验证调用
        verify(jsfAuthService).buildCommonParams(operator);
        verify(interfaceService).getMethodList(any(QueryMethodInfoRequest.class));
    }
    
    @Test
    void testGetByInterfaceNameWithJsfFailure() throws Exception {
        // 准备测试数据
        String interfaceName = "com.test.TestService";
        String operator = "testOperator";
        
        // Mock认证服务
        when(jsfAuthService.buildCommonParams(operator)).thenReturn(mockCommonParams);
        
        // Mock JSF API失败响应
        Result<InterfaceInfo> mockResult = new Result<>();
        mockResult.setCode(0);
        mockResult.setMsg("Interface not found");

        when(interfaceService.getByInterfaceName(any(QueryInterfaceRequest.class))).thenReturn(mockResult);
        
        // 执行测试
        JsfResult<InterfaceInfo> result = jsfInterfaceService.getByInterfaceName(interfaceName, operator);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(0, result.getCode());
        assertEquals("Interface not found", result.getMsg());
        assertNull(result.getData());
    }
    
    @Test
    void testGetByInterfaceNameWithException() throws Exception {
        // 准备测试数据
        String interfaceName = "com.test.TestService";
        String operator = "testOperator";
        
        // Mock认证服务
        when(jsfAuthService.buildCommonParams(operator)).thenReturn(mockCommonParams);
        
        // Mock JSF API抛出异常
        when(interfaceService.getByInterfaceName(any(QueryInterfaceRequest.class)))
                .thenThrow(new RuntimeException("Network error"));
        
        // 执行测试并验证异常
        JsfException exception = assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName(interfaceName, operator);
        });
        
        assertEquals("Failed to get interface by name", exception.getMessage());
    }
}
