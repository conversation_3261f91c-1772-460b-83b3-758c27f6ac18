package com.jdl.mcp.server.openme.auth.impl;

import com.jdl.mcp.server.openme.auth.OpenUserInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;
import static org.junit.jupiter.api.Assertions.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

class OpenMeAuthServiceImplTest {

    //mvn test -Dtest=OpenMeAuthServiceImplTest
    private static final Logger logger = LoggerFactory.getLogger(OpenMeAuthServiceImplTest.class);
    private OpenMeAuthServiceImpl openMeAuthService;

    @BeforeEach
    void setUp() {
        openMeAuthService = new OpenMeAuthServiceImpl(new RestTemplate());
        
        // 设置配置属性
        ReflectionTestUtils.setField(openMeAuthService, "appKey", "RUjHcGB2uMvqbvfXxbip4");
        ReflectionTestUtils.setField(openMeAuthService, "appSecret", "qJOwjyOJBoIwHMHLn8w5");
        ReflectionTestUtils.setField(openMeAuthService, "openTeamId", "88904676cbddd3ab14f037bd335d9423");
        ReflectionTestUtils.setField(openMeAuthService, "host", "http://openme-test.jd.com");
        ReflectionTestUtils.setField(openMeAuthService, "teamId", "00046419");
    }

    @Test
    void getAppAccessToken_Success() {
        String result = openMeAuthService.getAppAccessToken();
        assertNotNull(result);
        logger.info("AppAccessToken: {}", result);
    }

    @Test
    void getTeamAccessToken_Success() {
        String appAccessToken = openMeAuthService.getAppAccessToken();
        assertNotNull(appAccessToken);
        logger.info("AppAccessToken: {}", appAccessToken);
        
        String result = openMeAuthService.getTeamAccessToken(appAccessToken);
        if (result == null) {
            logger.error("Failed to get TeamAccessToken. Please check if the application is authorized for the team.");
        }
        assertNotNull(result);
        logger.info("TeamAccessToken: {}", result);
    }

    @Test
    void getOpenUserInfo_Success() {
        String appAccessToken = openMeAuthService.getAppAccessToken();
        assertNotNull(appAccessToken);
        logger.info("AppAccessToken: {}", appAccessToken);
        
        String teamAccessToken = openMeAuthService.getTeamAccessToken(appAccessToken);
        if (teamAccessToken == null) {
            logger.error("Failed to get TeamAccessToken. Skipping OpenUserInfo test.");
            return;
        }
        assertNotNull(teamAccessToken);
        logger.info("TeamAccessToken: {}", teamAccessToken);
        
        // 使用一个有效的用户ID
        String userId = "bjliandahu";
        OpenUserInfo result = openMeAuthService.getOpenUserInfo(teamAccessToken, userId);
        if (result == null) {
            logger.error("Failed to get OpenUserInfo. Please check if the TeamAccessToken and userId are valid.");
        }
        assertNotNull(result);
        logger.info("OpenUserInfo: {}", result);
    }

    @Test
    void getAppAccessToken_Failure() {
        // 设置错误的配置
        ReflectionTestUtils.setField(openMeAuthService, "appKey", "wrong_key");
        ReflectionTestUtils.setField(openMeAuthService, "appSecret", "wrong_secret");
        
        String result = openMeAuthService.getAppAccessToken();
        assertNull(result);
        logger.error("AppAccessToken retrieval failed as expected with wrong credentials");
    }

    @Test
    void getTeamAccessToken_Failure() {
        String result = openMeAuthService.getTeamAccessToken("wrong_token");
        assertNull(result);
        logger.error("TeamAccessToken retrieval failed as expected with wrong token");
    }

    @Test
    void getOpenUserInfo_Failure() {
        OpenUserInfo result = openMeAuthService.getOpenUserInfo("wrong_token", "wrong_user_id");
        assertNull(result);
        logger.error("OpenUserInfo retrieval failed as expected with wrong token and user ID");
    }
}
