package com.jdl.mcp.server.jsf;

import com.jd.jsf.open.api.vo.InterfaceInfo;
import com.jdl.mcp.server.jsf.auth.JsfAuthService;
import com.jdl.mcp.server.jsf.exception.JsfException;
import com.jdl.mcp.server.jsf.model.JsfResult;
import com.jdl.mcp.server.jsf.param.BaseJsfRequest;
import com.jdl.mcp.server.jsf.service.JsfInterfaceService;
import com.jdl.mcp.server.jsf.service.JsfProviderAliasService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JSF客户端集成测试
 * 
 * <AUTHOR> Assistant
 */
@SpringBootTest(classes = {com.jdl.mcp.server.McpPorterApplication.class})
@ActiveProfiles("test")
class JsfClientIntegrationTest {
    
    @Autowired(required = false)
    private JsfAuthService jsfAuthService;
    
    @Autowired(required = false)
    private JsfInterfaceService jsfInterfaceService;
    
    @Autowired(required = false)
    private JsfProviderAliasService jsfProviderAliasService;
    
    @Test
    void testJsfAuthServiceConfiguration() {
        // 验证认证服务是否正确配置
        assertNotNull(jsfAuthService, "JsfAuthService should be configured");
    }
    
    @Test
    void testJsfAuthServiceFunctionality() {
        if (jsfAuthService == null) {
            // 如果服务未配置，跳过测试
            return;
        }
        
        // 测试认证服务基本功能
        String operator = "testOperator";
        
        // 测试构建通用参数
        BaseJsfRequest commonParams = jsfAuthService.buildCommonParams(operator);
        assertNotNull(commonParams);
        assertNotNull(commonParams.getAppKey());
        assertEquals(operator, commonParams.getOperator());
        assertNotNull(commonParams.getClientIp());
        assertNotNull(commonParams.getTimeStamp());
        assertNotNull(commonParams.getSign());
        assertEquals(32, commonParams.getSign().length()); // MD5长度
        
        // 测试签名生成
        String sign = jsfAuthService.generateSign("testApp", 1234567890L, "testToken");
        assertNotNull(sign);
        assertEquals(32, sign.length());
        
        // 测试获取本机IP
        String localHost = jsfAuthService.getLocalHost();
        assertNotNull(localHost);
        assertFalse(localHost.isEmpty());
    }
    
    @Test
    void testJsfInterfaceServiceConfiguration() {
        // 验证接口服务是否正确配置
        // 注意：在测试环境中，JSF服务可能未配置，这是正常的
        if (jsfInterfaceService != null) {
            System.out.println("JsfInterfaceService is configured");
        } else {
            System.out.println("JsfInterfaceService is not configured (expected in test environment)");
        }
    }
    
    @Test
    void testJsfProviderAliasServiceConfiguration() {
        // 验证别名服务是否正确配置
        // 注意：在测试环境中，JSF服务可能未配置，这是正常的
        if (jsfProviderAliasService != null) {
            System.out.println("JsfProviderAliasService is configured");
        } else {
            System.out.println("JsfProviderAliasService is not configured (expected in test environment)");
        }
    }
    
    @Test
    void testJsfInterfaceServiceParameterValidation() {
        if (jsfInterfaceService == null) {
            // 如果服务未配置，跳过测试
            return;
        }
        
        // 测试参数验证
        assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName(null, "testOperator");
        });
        
        assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName("", "testOperator");
        });
        
        assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName("com.test.Service", null);
        });
        
        assertThrows(JsfException.class, () -> {
            jsfInterfaceService.getByInterfaceName("com.test.Service", "");
        });
    }
    
    @Test
    void testJsfProviderAliasServiceParameterValidation() {
        if (jsfProviderAliasService == null) {
            // 如果服务未配置，跳过测试
            return;
        }
        
        // 测试参数验证
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName(null, "testOperator");
        });
        
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName("", "testOperator");
        });
        
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName("com.test.Service", null);
        });
        
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName("com.test.Service", "");
        });
    }
    
    /**
     * 这个测试方法演示了如何在真实环境中使用JSF客户端
     * 注意：这个测试需要真实的JSF环境才能运行成功
     */
    @Test
    void testRealJsfApiCall() {
        if (jsfInterfaceService == null) {
            System.out.println("Skipping real JSF API call test - service not configured");
            return;
        }
        
        try {
            // 尝试查询一个真实的接口
            String testInterface = "com.jd.eclp.btp.taskfunnel.handler.Handler";
            String operator = "testOperator";
            
            JsfResult<InterfaceInfo> result = jsfInterfaceService.getByInterfaceName(testInterface, operator);
            
            // 验证结果结构
            assertNotNull(result);
            assertNotNull(result.getCode());
            assertNotNull(result.getMsg());
            
            if (result.isSuccess()) {
                System.out.println("JSF API call successful: " + result);
                InterfaceInfo interfaceInfo = result.getData();
                if (interfaceInfo != null) {
                    System.out.println("Interface info: " + interfaceInfo);
                }
            } else {
                System.out.println("JSF API call failed: " + result.getMsg());
            }
            
        } catch (JsfException e) {
            System.out.println("JSF API call failed with exception: " + e.getMessage());
            // 在测试环境中，这是预期的行为
        }
    }
    
    /**
     * 这个测试方法演示了如何查询接口别名
     */
    @Test
    void testRealJsfAliasApiCall() {
        if (jsfProviderAliasService == null) {
            System.out.println("Skipping real JSF alias API call test - service not configured");
            return;
        }
        
        try {
            // 尝试查询一个真实接口的别名
            String testInterface = "com.jd.eclp.btp.taskfunnel.handler.Handler";
            String operator = "testOperator";
            
            JsfResult<List<String>> result = jsfProviderAliasService.getAliasByInterfaceName(testInterface, operator);
            
            // 验证结果结构
            assertNotNull(result);
            assertNotNull(result.getCode());
            assertNotNull(result.getMsg());
            
            if (result.isSuccess()) {
                System.out.println("JSF alias API call successful: " + result);
                List<String> aliases = result.getData();
                if (aliases != null && !aliases.isEmpty()) {
                    System.out.println("Found aliases: " + aliases);
                }
            } else {
                System.out.println("JSF alias API call failed: " + result.getMsg());
            }
            
        } catch (JsfException e) {
            System.out.println("JSF alias API call failed with exception: " + e.getMessage());
            // 在测试环境中，这是预期的行为
        }
    }
}
