package com.jdl.mcp.server.openme.joyspace.impl;

import com.jdl.mcp.server.McpPorterApplication;
import com.jdl.mcp.server.openme.joyspace.JoyspaceService;
import com.jdl.mcp.server.openme.joyspace.param.GetPageContentParam;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.json.JSONException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;


import static org.junit.jupiter.api.Assertions.*;

//mvn test -Dtest=JoyspaceServiceImplOnlineTest
@SpringBootTest(classes = McpPorterApplication.class)
@ActiveProfiles("test")
class JoyspaceServiceImplOnlineTest {

    private static final Logger logger = LoggerFactory.getLogger(JoyspaceServiceImplOnlineTest.class);

    @Autowired
    private JoyspaceService joyspaceService;

    @Value("${openme.app.host}")
    private String host;

    @Value("${openme.joyspace.scene}")
    private String defaultScene;

    @Value("${openme.app.openTeamId}")
    private String openTeamId;

   
    @Test
    void getPageContent_Success() throws JSONException {
        // Arrange
        GetPageContentParam param = new GetPageContentParam();
        param.setPageId("waD9CSsBwkr2Upg5MNf5");

        // Act
        JSONObject result = joyspaceService.getPageContent(param);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getString("msg"));
        assertEquals(0, result.getInt("code"));
        assertNotNull(result.getJSONArray("data"));
        assertTrue(result.getJSONArray("data").length() > 0);
        JSONObject firstBlock = result.getJSONArray("data").getJSONObject(0);
        assertNotNull(firstBlock.getString("id"));
        assertNotNull(firstBlock.getString("type"));
        assertNotNull(firstBlock.getJSONArray("children"));
    }
   
}
