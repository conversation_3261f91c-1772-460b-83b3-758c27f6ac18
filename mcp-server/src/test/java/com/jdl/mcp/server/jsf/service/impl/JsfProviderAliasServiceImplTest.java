package com.jdl.mcp.server.jsf.service.impl;

import com.jd.jsf.open.api.ProviderAliaService;
import com.jd.jsf.open.api.vo.request.QueryInterfaceRequest;
import com.jd.jsf.open.api.vo.Result;
import com.jdl.mcp.server.jsf.auth.JsfAuthService;
import com.jdl.mcp.server.jsf.exception.JsfException;
import com.jdl.mcp.server.jsf.model.JsfResult;
import com.jdl.mcp.server.jsf.param.BaseJsfRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * JSF别名服务测试类
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
class JsfProviderAliasServiceImplTest {
    
    @Mock
    private JsfAuthService jsfAuthService;
    
    @Mock
    private ProviderAliaService providerAliaService;
    
    @InjectMocks
    private JsfProviderAliasServiceImpl jsfProviderAliasService;
    
    private BaseJsfRequest mockCommonParams;
    
    @BeforeEach
    void setUp() {
        mockCommonParams = new BaseJsfRequest();
        mockCommonParams.setAppKey("testAppKey");
        mockCommonParams.setOperator("testOperator");
        mockCommonParams.setClientIp("127.0.0.1");
        mockCommonParams.setTimeStamp(System.currentTimeMillis());
        mockCommonParams.setSign("testSign");
    }
    
    @Test
    void testGetAliasByInterfaceNameSuccess() throws Exception {
        // 准备测试数据
        String interfaceName = "com.test.TestService";
        String operator = "testOperator";
        List<String> aliasList = Arrays.asList(
                "jsf-liush-demo",
                "jsftest-gw",
                "jsf-liush-test",
                "CHANGE-IT",
                "zjx-jsf-provider-test"
        );
        
        // Mock认证服务
        when(jsfAuthService.buildCommonParams(operator)).thenReturn(mockCommonParams);
        
        // Mock JSF API响应
        Result<List<String>> mockResult = new Result<>();
        mockResult.setCode(1);
        mockResult.setMsg("success");
        mockResult.setData(aliasList);
        mockResult.setTotal(aliasList.size());
        
        when(providerAliaService.getAliasByInterfaceName(any(QueryInterfaceRequest.class))).thenReturn(mockResult);
        
        // 执行测试
        JsfResult<List<String>> result = jsfProviderAliasService.getAliasByInterfaceName(interfaceName, operator);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getCode());
        assertEquals("success", result.getMsg());
        assertEquals(aliasList, result.getData());
        assertEquals(5, result.getData().size());
        assertEquals(5, result.getTotal());
        
        // 验证别名内容
        assertTrue(result.getData().contains("jsf-liush-demo"));
        assertTrue(result.getData().contains("jsftest-gw"));
        assertTrue(result.getData().contains("jsf-liush-test"));
        assertTrue(result.getData().contains("CHANGE-IT"));
        assertTrue(result.getData().contains("zjx-jsf-provider-test"));
        
        // 验证调用
        verify(jsfAuthService).buildCommonParams(operator);
        verify(providerAliaService).getAliasByInterfaceName(any(QueryInterfaceRequest.class));
    }
    
    @Test
    void testGetAliasByInterfaceNameWithNullInterfaceName() {
        // 测试空接口名
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName(null, "testOperator");
        });
        
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName("", "testOperator");
        });
        
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName("   ", "testOperator");
        });
    }
    
    @Test
    void testGetAliasByInterfaceNameWithNullOperator() {
        // 测试空操作人
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName("com.test.TestService", null);
        });
        
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName("com.test.TestService", "");
        });
        
        assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName("com.test.TestService", "   ");
        });
    }
    
    @Test
    void testGetAliasByInterfaceNameWithEmptyResult() throws Exception {
        // 准备测试数据
        String interfaceName = "com.test.NonExistentService";
        String operator = "testOperator";
        List<String> emptyAliasList = Arrays.asList();
        
        // Mock认证服务
        when(jsfAuthService.buildCommonParams(operator)).thenReturn(mockCommonParams);
        
        // Mock JSF API响应
        Result<List<String>> mockResult = new Result<>();
        mockResult.setCode(1);
        mockResult.setMsg("success");
        mockResult.setData(emptyAliasList);
        mockResult.setTotal(0);
        
        when(providerAliaService.getAliasByInterfaceName(any(QueryInterfaceRequest.class))).thenReturn(mockResult);
        
        // 执行测试
        JsfResult<List<String>> result = jsfProviderAliasService.getAliasByInterfaceName(interfaceName, operator);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getCode());
        assertEquals("success", result.getMsg());
        assertNotNull(result.getData());
        assertEquals(0, result.getData().size());
        assertEquals(0, result.getTotal());
    }
    
    @Test
    void testGetAliasByInterfaceNameWithJsfFailure() throws Exception {
        // 准备测试数据
        String interfaceName = "com.test.TestService";
        String operator = "testOperator";
        
        // Mock认证服务
        when(jsfAuthService.buildCommonParams(operator)).thenReturn(mockCommonParams);
        
        // Mock JSF API失败响应
        Result<List<String>> mockResult = new Result<>();
        mockResult.setCode(-6);
        mockResult.setMsg("查询不到对应的记录");
        
        when(providerAliaService.getAliasByInterfaceName(any(QueryInterfaceRequest.class))).thenReturn(mockResult);
        
        // 执行测试
        JsfResult<List<String>> result = jsfProviderAliasService.getAliasByInterfaceName(interfaceName, operator);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(-6, result.getCode());
        assertEquals("查询不到对应的记录", result.getMsg());
        assertNull(result.getData());
    }
    
    @Test
    void testGetAliasByInterfaceNameWithException() throws Exception {
        // 准备测试数据
        String interfaceName = "com.test.TestService";
        String operator = "testOperator";
        
        // Mock认证服务
        when(jsfAuthService.buildCommonParams(operator)).thenReturn(mockCommonParams);
        
        // Mock JSF API抛出异常
        when(providerAliaService.getAliasByInterfaceName(any(QueryInterfaceRequest.class)))
                .thenThrow(new RuntimeException("Network timeout"));
        
        // 执行测试并验证异常
        JsfException exception = assertThrows(JsfException.class, () -> {
            jsfProviderAliasService.getAliasByInterfaceName(interfaceName, operator);
        });
        
        assertEquals("Failed to get alias by interface name", exception.getMessage());
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Network timeout", exception.getCause().getMessage());
    }
    

}
