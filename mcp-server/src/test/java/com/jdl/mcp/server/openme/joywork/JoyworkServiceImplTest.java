package com.jdl.mcp.server.openme.joywork;

import com.jdl.mcp.server.McpPorterApplication;
import com.jdl.mcp.server.openme.auth.OpenMeAuthService;
import com.jdl.mcp.server.openme.joywork.param.JoyworkCreateTaskParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkCreateTaskParam.Owner.TaskUserRole;

import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

//mvn test -Dtest=JoyworkServiceImplTest
@SpringBootTest(classes = McpPorterApplication.class)
@ActiveProfiles("test")
public class JoyworkServiceImplTest {
    @Autowired
    private JoyworkService joyworkService;

    @Test
    public void testCreateAndUpdateAndStatusTask() throws Exception {
        // 构造创建任务参数
        long now = System.currentTimeMillis();
        long after24h = now + 24 * 60 * 60 * 1000L;
        JoyworkCreateTaskParam createParam = new JoyworkCreateTaskParam();
        createParam.setContent("http://jd.com");
        createParam.setMobileContent("jdme://web/?url=null");
        createParam.setSourceId("JDL_LOGISTICS_TASK_618_2");
        createParam.setTitle("物流交易待办");
        createParam.setRemark("这是任务背景");
        createParam.setStartTime(now);
        createParam.setEndTime(after24h);
        createParam.setSysProjectId("xxxxx");
        createParam.setSysGroupId("xxxxx");
        createParam.setSourceDescZh("xxxxx");
        createParam.setSourceDescEn("xxxxx");
        createParam.setSourceTrusteeship(0);
        // extend
        // JoyworkCreateTaskParam.Extend ext = new JoyworkCreateTaskParam.Extend();
        // ext.setContent("3");
        // ext.setTips("条");
        // ext.setType("text");
        // List<JoyworkCreateTaskParam.Extend> extList = new ArrayList<>();
        // extList.add(ext);
        // createParam.setExtend(extList);
        // ownerList
        JoyworkCreateTaskParam.Owner owner = new JoyworkCreateTaskParam.Owner();
        // openUserId由实现自动填充
        owner.setOpenUserId("bjliandahu");
        owner.setTaskUserRole(TaskUserRole.EXECUTOR.getName());
        List<JoyworkCreateTaskParam.Owner> ownerList = new ArrayList<>();
        ownerList.add(owner);
        createParam.setOwnerList(ownerList);

        JSONObject createResult = joyworkService.createTask(createParam);
        System.out.println("createTask result: " + createResult);
        assertEquals(createResult.getString("code"), "0", "创建任务失败");
        String taskId = createResult.optJSONObject("data") != null ? createResult.getJSONObject("data").optString("taskId") : null;
        assertNotNull(taskId, "返回的taskId为空");

        // // 构造更新任务参数
        // com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskParam updateParam = new com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskParam();
        // updateParam.setTaskId(taskId);
        // updateParam.setTitle("update_title");
        // updateParam.setRemark("update_remark");
        // updateParam.setStartTime(now);
        // updateParam.setEndTime(after24h);
        // updateParam.setPriorityType(1);
        // updateParam.setContent("update_content");
        // updateParam.setMobileContent("update_mobile_content");
        // updateParam.setExtend("update_extend");
        // updateParam.setSysProjectId("xxxxx");
        // updateParam.setSysGroupId("xxxxx");
        // JSONObject updateResult = joyworkService.updateTask(updateParam);
        // System.out.println("updateTask result: " + updateResult);
        // assertTrue(updateResult.getBoolean("success"), "更新任务失败");

        // // 构造更新任务状态参数
        // com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskStatusParam statusParam = new com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskStatusParam();
        // statusParam.setTaskId(taskId);
        // statusParam.setTaskStatus(1); // 1=完成，具体状态码需根据业务调整
        // JSONObject statusResult = joyworkService.updateTaskStatus(statusParam);
        // System.out.println("updateTaskStatus result: " + statusResult);
        // assertTrue(statusResult.getBoolean("success"), "更新任务状态失败");
    }
}
