package com.jdl.mcp.server.openme.joyspace.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JavaOcrProcessor类的单元测试
 */
class JavaOcrProcessorTest {
    private static final Logger logger = LoggerFactory.getLogger(JavaOcrProcessorTest.class);
    
    private JavaOcrProcessor javaOcrProcessor;
    
    @BeforeEach
    void setUp() {
        javaOcrProcessor = new JavaOcrProcessor();
    }
    
    /**
     * 测试创建包含文本的测试图片
     */
    private BufferedImage createTextImage(String text) {
        // 创建一个300x100的图片
        BufferedImage image = new BufferedImage(300, 100, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置白色背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 300, 100);
        
        // 设置黑色文本
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("Arial", Font.BOLD, 24));
        g2d.drawString(text, 50, 50);
        
        g2d.dispose();
        return image;
    }
    
    /**
     * 测试对图片进行OCR识别
     */
    @Test
    void testPerformOcr() throws IOException {
        logger.info("测试对图片进行OCR识别");
        
        // 创建一个包含文本的测试图片
        String testText = "Hello World";
        BufferedImage textImage = createTextImage(testText);
        
        // 保存图片到临时文件
        File tempFile = File.createTempFile("ocr_test_", ".png");
        tempFile.deleteOnExit();
        ImageIO.write(textImage, "png", tempFile);
        
        // 测试对文件进行OCR
        String ocrTextFromFile = javaOcrProcessor.performOcr(tempFile);
        assertNotNull(ocrTextFromFile, "OCR结果不应为null");
        logger.info("文件OCR结果: {}", ocrTextFromFile);
        
        // 测试对BufferedImage进行OCR
        String ocrTextFromImage = javaOcrProcessor.performOcr(textImage);
        assertNotNull(ocrTextFromImage, "OCR结果不应为null");
        logger.info("图片OCR结果: {}", ocrTextFromImage);
    }
    
    /**
     * 测试判断OCR结果是否适合转换为Markdown
     */
    @Test
    void testIsTextSuitableForMarkdown() {
        // 测试null文本
        assertFalse(javaOcrProcessor.isTextSuitableForMarkdown(null));
        
        // 测试短文本
        assertFalse(javaOcrProcessor.isTextSuitableForMarkdown("Short"));
        
        // 测试多行文本
        assertTrue(javaOcrProcessor.isTextSuitableForMarkdown("This is a\nmulti-line text\nthat should be suitable for markdown"));
        
        // 测试表格文本
        assertTrue(javaOcrProcessor.isTextSuitableForMarkdown("| Column1 | Column2 |\n| ------- | ------- |\n| Data1   | Data2   |"));
    }
    
    /**
     * 测试将OCR文本转换为Markdown格式
     */
    @Test
    void testConvertOcrTextToMarkdown() {
        // 测试null文本
        assertEquals("", javaOcrProcessor.convertOcrTextToMarkdown(null));
        
        // 测试普通文本
        String normalText = "This is a normal paragraph.";
        String expectedNormalMarkdown = "This is a normal paragraph.\n\n";
        assertEquals(expectedNormalMarkdown, javaOcrProcessor.convertOcrTextToMarkdown(normalText));
        
        // 测试表格文本
        String tableText = "| Column1 | Column2 |\n| ------- | ------- |\n| Data1   | Data2   |";
        String expectedTableMarkdown = "| Column1 | Column2 |\n| ------- | ------- |\n| Data1   | Data2   |\n";
        assertEquals(expectedTableMarkdown, javaOcrProcessor.convertOcrTextToMarkdown(tableText));
        
        // 测试多段落文本
        String multiParagraphText = "Paragraph 1.\n\nParagraph 2.\n\nParagraph 3.";
        String expectedMultiParagraphMarkdown = "Paragraph 1.\n\nParagraph 2.\n\nParagraph 3.\n\n";
        assertEquals(expectedMultiParagraphMarkdown, javaOcrProcessor.convertOcrTextToMarkdown(multiParagraphText));
    }
}
