package com.jdl.mcp.server.openme.joyspace.impl;

import com.jdl.mcp.server.McpPorterApplication;
import com.jdl.mcp.server.openme.joyspace.JoyspaceService;
import com.jdl.mcp.server.openme.joyspace.param.GetFolderListParam;
import com.jdl.mcp.server.openme.joyspace.param.GetFileListParam;
import com.jdl.mcp.server.openme.joyspace.param.GetPageInfosParam;
import com.jdl.mcp.server.openme.joyspace.param.GetPageContentParam;
import com.jdl.mcp.server.openme.joyspace.param.GetLocationParam;
import com.jdl.mcp.server.openme.joyspace.param.GetAttachmentPresignUrlParam;
import org.json.JSONObject;
import org.json.JSONException;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.*;

//mvn test -Dtest=JoyspaceServiceImplTest
@SpringBootTest(classes = McpPorterApplication.class)
@ActiveProfiles("test")
class JoyspaceServiceImplTest {

    @Autowired
    private JoyspaceService joyspaceService;

    @Value("${openme.app.host}")
    private String host;

    @Value("${openme.joyspace.scene}")
    private String defaultScene;

    @Value("${openme.app.openTeamId}")
    private String openTeamId;


   // @Test
    void getFolderList_Success() throws JSONException {
        // Arrange
        GetFolderListParam param = new GetFolderListParam();
        param.setFolderId("root");

        // Act
        JSONObject result = joyspaceService.getFolderList(param);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getString("msg"));
        assertEquals(0, result.getInt("code"));
        assertNotNull(result.getJSONArray("data"));
        assertTrue(result.getJSONArray("data").length() > 0);
        assertNotNull(result.getJSONArray("data").getJSONObject(0).getString("id"));
        assertNotNull(result.getJSONArray("data").getJSONObject(0).getString("name"));
    }

    //@Test
    void getFileList_Success() throws JSONException {
        // Arrange
        GetFileListParam param = new GetFileListParam();
        param.setFolderId("5Tx3IbXgzSGhwEpnG7kK");
        param.setStart(0);
        param.setLength(10);

        // Act
        JSONObject result = joyspaceService.getFileList(param);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getString("msg"));
        assertEquals(0, result.getInt("code"));
        assertNotNull(result.getJSONObject("data"));
        assertNotNull(result.getJSONObject("data").getInt("total"));
        assertNotNull(result.getJSONObject("data").getJSONArray("pages"));
        assertTrue(result.getJSONObject("data").getJSONArray("pages").length() > 0);
        JSONObject firstPage = result.getJSONObject("data").getJSONArray("pages").getJSONObject(0);
        assertNotNull(firstPage.getString("id"));
        assertNotNull(firstPage.getString("title"));
        assertNotNull(firstPage.getInt("pageType"));
    }

    @Test
    void getPageInfos_Success() throws JSONException, ExecutionException, InterruptedException {
        // Arrange
        GetPageInfosParam param = new GetPageInfosParam();
        param.setPageIds(Arrays.asList("mabzQBOJoTpWbiQy3L3O", "0suFsDfjSeJGlxYIPMIH"));


        // Act
        JSONObject result = joyspaceService.getPageInfos(param);// 等待异步操作完成

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getString("msg"));
        assertEquals(0, result.getInt("code"));
        assertNotNull(result.getJSONArray("data"));
        assertTrue(result.getJSONArray("data").length() > 0);
        JSONObject firstPage = result.getJSONArray("data").getJSONObject(0);
        assertNotNull(firstPage.getString("pageId"));
        assertNotNull(firstPage.getString("title"));
        assertNotNull(firstPage.getString("createdBy"));
        assertNotNull(firstPage.getLong("createdAt"));
        assertNotNull(firstPage.getInt("pageType"));
    }

    //@Test
    void getPageContent_Success() throws JSONException {
        // Arrange
        GetPageContentParam param = new GetPageContentParam();
        param.setPageId("mabzQBOJoTpWbiQy3L3O");

        // Act
        JSONObject result = joyspaceService.getPageContent(param);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getString("msg"));
        assertEquals(0, result.getInt("code"));
        assertNotNull(result.getJSONArray("data"));
        assertTrue(result.getJSONArray("data").length() > 0);
        JSONObject firstBlock = result.getJSONArray("data").getJSONObject(0);
        assertNotNull(firstBlock.getString("id"));
        assertNotNull(firstBlock.getString("type"));
        assertNotNull(firstBlock.getJSONArray("children"));
    }

   // @Test
    void getLocation_Success() throws JSONException {
        // Arrange
        GetLocationParam param = new GetLocationParam();
        param.setPageId("mabzQBOJoTpWbiQy3L3O");

        // Act
        JSONObject result = joyspaceService.getLocation(param);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getString("msg"));
        assertEquals(0, result.getInt("code"));
        assertNotNull(result.getJSONObject("data"));
        assertNotNull(result.getJSONObject("data").getJSONArray("locations"));
        assertTrue(result.getJSONObject("data").getJSONArray("locations").length() > 0);
        JSONObject firstLocation = result.getJSONObject("data").getJSONArray("locations").getJSONObject(0);
        assertNotNull(firstLocation.getString("id"));
        assertNotNull(firstLocation.getString("name"));
        assertNotNull(firstLocation.getInt("bizType"));
    }

   // @Test
    void getAttachmentPresignUrl_Success() throws JSONException {
        // Arrange
        GetAttachmentPresignUrlParam param = new GetAttachmentPresignUrlParam();
        param.setPageId("mabzQBOJoTpWbiQy3L3O");
        param.setAttachmentId("test_attachment_id");

        // Act
        JSONObject result = joyspaceService.getAttachmentPresignUrl(param);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.getString("msg"));
        assertEquals(0, result.getInt("code"));
        assertNotNull(result.getJSONObject("data"));
        assertNotNull(result.getJSONObject("data").getString("link"));
    }
}
