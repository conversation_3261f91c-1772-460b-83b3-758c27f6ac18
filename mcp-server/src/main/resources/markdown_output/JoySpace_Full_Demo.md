# **JoySpace 完整演示文档**

这是一个包含所有JoySpace文档结构的演示文件。

- 无序列表项1
- 无序列表项2

1. 有序列表项1
2. 有序列表项2

- [ ] 任务列表项1
- [ ] 任务列表项2

| **表头1** | **表头2** | **表头3** |
| --- | --- | --- |
| 数据1 | 数据2 | 数据3 |

![](https://example.com/image.jpg)

这是一个[链接](https://example.com)示例。

```javascript
function helloWorld() {
  console.log('Hello, World!');
}
```

> 这是一个引用示例。

---

<div style="background-color: #f0f0f0; padding: 10px;">

这是一个高亮块示例。

</div>

<div style="display: flex;">
<div style="flex: 1; padding: 10px;">

这是第一列的内容。

</div>
<div style="flex: 1; padding: 10px;">

这是第二列的内容。

</div>
</div>

这是一个文本格式*示例，包含***粗体**<u>、</u>~~删除线~~`和`行内代码。

