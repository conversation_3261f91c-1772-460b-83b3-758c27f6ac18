【TRD】616全-汪冉冉-2023_06_17

# **一、研发设计规范概述**

## 1.1  目标

   规范项目、需求开发前，系统的架构设计；在开发前指导编码开发，开发后，作为代码Review的重要依据。

## **1.2 撰写人**

（参与设计的具体架构、研发名单，未直接参与请不要署名）

| 撰写人 | 姓名 | 时间 | 版本 | 变更内容 |
| --- | --- | --- | --- | --- |
| 测试开发工程师岗 | 汪冉冉 |  |  |  |

# **二、设计规范****主体内容**

## **2.1 需求与背景**

简要概述需求与背景，并列出相关联的PRD等附件链接



| **需求名称** | **需求描述** | **需求附件** |
| --- | --- | --- |
| ytest_需求002 |  |  |
| testbug |  |  |
| 添加子需求 |  |  |
| 主站-OCS订单优惠券分摊-扩展点实现 |  |  |
| 测试需求10 |  |  |



## **2.2 整体解决方案**

### 2.2.1 打算如何解决这些需求（包括整体架构图，看全局）

随着需求和技改不断建设，我们的系统在不断的变化和演进，为了能够实时把控系统的整体架构，我们需要用架构图来表达系统最新的架构情况，主要包括功能结构图和分层体系架构图

**图链接（上传OSS）**

### 2.2.2 产品化设计（根据引导项来说明）

设计方案是否产品化，体现对系统能力的抽象，抽象的能力是什么，除了可以满足当前项目需求外，还可以复用到将来类似的需求。（1：复用已有的系统能力、2：新抽象了系统能力、3：临时方案支持、4：不需要产品化设计）

如果是产品化设计，需要体现沉淀的能力是什么，未来那些场景可以复用；

例如：

1、仪表盘数据统计配置新增精度配置功能，后续的价格、数量等可以按照自己想要展示的精度进行配置

2、代销商出库待办项展示复用了之前的待办配置化设计

3、站内信查询新增补数扩展点，并提供水平实现，同时后续也可以通过该扩展点自定义需要返回的数据以及取数逻辑



如果否，需说明为什么没有去做产品化设计

例如：倒排期时间紧张、技改（中间件升级）、扩展点的垂直实现等



## **2.3 改动项总览（必需—基于PRD明确改动范围）**

示例：

| 改动项(涉及应用名称) | 改造内容 | 备注 |
| --- | --- | --- |
| 批次服务 | 具体改造项<br><br>1、创建批次时，旧发券服务和新隔离环境同步初始化库存，已支持<br><br>2、大奖隔离环境初始化库存支持 |  |
| 批次worker | 具体改造项 |  |

| **改动项(涉及应用名称)** | **改造内容** | **备注** |
| --- | --- | --- |
| 藏经阁listener<br><br>J-dos-cjg-listener | 更新扩展点-能力地图-原始节点落库后置处理11<br><br>修改节点名称 |  |
| 藏经阁listener<br><br>J-dos-cjg-listener | 删除扩展点-能力地图-根节点前置校验 |  |
| 藏经阁listener<br><br>J-dos-cjg-listener | 删除扩展点实现-com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightJSFProviderHandler |  |
| 藏经阁listener<br><br>J-dos-cjg-listener | 删除扩展点实现-com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightTaskHandler |  |
| 藏经阁listener<br><br>J-dos-cjg-listener | 删除扩展点实现-com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightMQHandler |  |
| 藏经阁listener<br><br>J-dos-cjg-listener | 删除扩展点实现-com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightHTTPHandler |  |
| 藏经阁listener<br><br>J-dos-cjg-listener | 新增领域服务-新增领域服务<br><br>领域服务 |  |
| 藏经阁listener<br><br>J-dos-cjg-listener | 新增领域能力-新增领域能力<br><br>新增 |  |
| 藏经阁listener<br><br>J-dos-cjg-listener | 新增JSF消费者-新增JSF消费者<br><br>新增 |  |



# **三、详细设计**

## **3.1 API层（体现是否进行了通用化设计，将来类似需求不会导致架构发生颠覆性的改造）**

用户接口层负责向用户显示信息和解释用户操作，这里的用户可能是：真实的用户、程序、自动化测试和批处理脚本等等。（暴露给外部调用，S层、其它系统）

1、列出新增API（入参，出参）；

2、列出更新API；

3、描述MQ主题BODY体；

示例：

| API接口 | **数据模型** | **属性(新增)** | **类型** | **描述** | **说明** |
| --- | --- | --- | --- | --- | --- |
| om.jd.trade2.assets.giftcard | com.jd.trade2.assets.giftcard.export.param.GiftCardCollectionParam | setSupportBizTags | （CardSupportTagEnum, String） | 设置支持的业务标识<br><br>第一个参数: CardSupportTagEnum.WELFARE_UNSELECTED_BY_DEFAULT<br><br>第二个参数: "1" 福粒不默认选中<br><br><br><br>枚举类:com.jd.trade2.assets.giftcard.export.enums.CardSupportTagEnum | 新增字段 |
|  | com.jd.trade2.assets.giftcard.export.param.GiftCardCollectionParam | getSupportBizTags | Map<String, String> | 支持的业务标识 | 新增字段 |







## **3.2 业务域层（体现是否进行了通用化设计，将来类似需求不会导致架构发生颠覆性的改造）**

### 3.2.1 业务域(业务模块变更)

- 模块之间边界和交互的变更

- 模块内部功能的变更

### 3.2.2 业务流程变更(业务流程图)

**图链接（上传OSS）**

### 3.2.3 依赖外部服务的变更

- 依赖接口的参数变更

- 内部适配层模型和参数的变更



### 3.2.4** PaaS化设计**

#### 3.2.4.1 **领域能力与扩展点**

| **修改类型** | **领域能力**<br><br>**(中文)** | **领域能力**<br><br>**(英文)** | **扩展点方法**<br><br>**(中文)** | **扩展点方法**<br><br>**(英文)** | **扩展点释义** | **Merge规则名称** | **典型垂直业务的差异逻辑** | **备注** |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |  |  |



#### 3.2.4.2 扩展点设计

| **扩展点方法(类名.方法名)** | **业务身份名称(见**[藏经阁](http://cjg.jd.com/admin/integration/b-list?type=2)**)** | **本需求的差异逻辑** | **备注** |
| --- | --- | --- | --- |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightJSFProviderHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightTaskHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightDomainServiceOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightDomainAbilityOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightJSFConsumerOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightFlowNodeOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightDBNodeOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightChildTraceOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightConditionNodeOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightMQHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightHTTPHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightBizConfigOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightRemoteCacheNodeOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightRPCNodeOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightExtensionOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightExtensionImplOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightJSFProviderHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightTaskHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightMQHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightHTTPHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightJSFProviderHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightTaskHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightMQHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.root.LightWeightHTTPHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |
| com.jd.viewworld.service.lightweight.handle.wrapper.basic.LightWeightChildTraceOriginHandler | 藏经阁内部业务身份(cn_retail_cjg_ability_map) |  |  |



#### 3.2.4.3 核心业务模型的变更

| **业务模型名称** | **模型字段** | **字段类型** | **本需求的差异逻辑** | **备注** |
| --- | --- | --- | --- | --- |
|  |  |  |  |  |



### 3.2.5** 配置化设计**

使用ducc还是cms进行了xxx的配置化，实现了xxx。

描述本次需求涉及不到，但以后可能会在此处扩展的功能

注：开发人员可根据实际情况进行补充。



## **3.3 基础设施层设计（体现是否进行了通用化设计，将来类似需求不会导致架构发生颠覆性的改造）**

1、列出MySQL数据结构的变化，DDL语句；

2、列出JIMDB数据结构变化；

3、列出ES数据结构变化；

4、列出HBASE数据结构变化；



## **3.4 安全性设计**

### 3.4.1 权限设计

可在此节统一描述相关接口的权限类校验，如必须登录/必须登录且为商家身份等。

### 3.4.2 加解密/敏感字段设计

数据库存储/接口暴露的敏感信息，需要xx方式保证安全性。

### 3.4.3 签名机制

xx接口需要使用xx签名机制

### 3.4.4 风控规则

暂无/接入了什么风控接口

注：开发人员可根据实际情况进行补充。

### 3.4.5 报警规则

需要配置UMP可用率及性能报警



# **四、上线方案**

## **4.1 上线步骤与注意事项**

上线需要有上线步骤文档，并约架构师评审

*各涉及系统上线方案，多系统时需要统筹规划*

抢券系统上线方案参考：[https://joyspace.jd.com/page/PpP6PqVkf36Cyw37ry9B](https://joyspace.jd.com/page/PpP6PqVkf36Cyw37ry9B)

批次系统上线方案参考：[https://joyspace.jd.com/page/gJLQlyCsZYzagDiNC68L](https://joyspace.jd.com/page/gJLQlyCsZYzagDiNC68L)

## **4.2 回滚方案**

*各涉及系统回滚的方案*

## **4.3 切量方案**

