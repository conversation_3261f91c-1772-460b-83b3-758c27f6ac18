[{"type": "p", "id": "header1", "children": [{"text": "JoySpace 完整演示文档", "fontSize": 32, "bold": true}], "header": 1, "align": "center"}, {"type": "p", "id": "intro", "children": [{"text": "这是一个包含所有JoySpace文档结构的演示文件。"}]}, {"type": "list", "id": "unordered-list", "value": "bullet", "children": [{"type": "p", "id": "ul-item1", "children": [{"text": "无序列表项1"}]}, {"type": "p", "id": "ul-item2", "children": [{"text": "无序列表项2"}]}]}, {"type": "list", "id": "ordered-list", "value": "ordered", "start": 1, "orderedType": "Normal", "children": [{"type": "p", "id": "ol-item1", "children": [{"text": "有序列表项1"}]}, {"type": "p", "id": "ol-item2", "children": [{"text": "有序列表项2"}]}]}, {"type": "list", "id": "task-list", "value": "checkbox", "children": [{"type": "p", "id": "task-item1", "children": [{"text": "任务列表项1"}], "checked": true}, {"type": "p", "id": "task-item2", "children": [{"text": "任务列表项2"}], "checked": false}]}, {"type": "table", "id": "demo-table", "width": [200, 200, 200], "align": "center", "children": [{"type": "table-row", "id": "table-row1", "children": [{"type": "table-cell", "id": "cell1-1", "children": [{"type": "p", "id": "cell1-1-content", "children": [{"text": "表头1", "bold": true}]}]}, {"type": "table-cell", "id": "cell1-2", "children": [{"type": "p", "id": "cell1-2-content", "children": [{"text": "表头2", "bold": true}]}]}, {"type": "table-cell", "id": "cell1-3", "children": [{"type": "p", "id": "cell1-3-content", "children": [{"text": "表头3", "bold": true}]}]}]}, {"type": "table-row", "id": "table-row2", "children": [{"type": "table-cell", "id": "cell2-1", "children": [{"type": "p", "id": "cell2-1-content", "children": [{"text": "数据1"}]}]}, {"type": "table-cell", "id": "cell2-2", "children": [{"type": "p", "id": "cell2-2-content", "children": [{"text": "数据2"}]}]}, {"type": "table-cell", "id": "cell2-3", "children": [{"type": "p", "id": "cell2-3-content", "children": [{"text": "数据3"}]}]}]}]}, {"type": "img", "id": "demo-image", "url": "https://example.com/image.jpg", "width": 300, "height": 200, "align": "center", "children": [{"text": ""}]}, {"type": "p", "id": "link-demo", "children": [{"text": "这是一个"}, {"type": "link", "id": "demo-link", "url": "https://example.com", "children": [{"text": "链接"}]}, {"text": "示例。"}]}, {"type": "block-code", "id": "demo-code-block", "lang": "javascript", "children": [{"type": "block-code-line", "id": "code-line1", "children": [{"text": "function helloWorld() {"}]}, {"type": "block-code-line", "id": "code-line2", "children": [{"text": "  console.log('Hello, <PERSON>!');"}]}, {"type": "block-code-line", "id": "code-line3", "children": [{"text": "}"}]}]}, {"type": "block-quote", "id": "demo-quote", "children": [{"text": "这是一个引用示例。"}]}, {"type": "divider", "id": "demo-divider"}, {"type": "highlight-block", "id": "demo-highlight", "children": [{"type": "p", "id": "highlight-content", "children": [{"text": "这是一个高亮块示例。"}]}]}, {"type": "multi-column", "id": "demo-multi-column", "children": [{"type": "multi-column-item", "id": "column1", "children": [{"type": "p", "id": "column1-content", "children": [{"text": "这是第一列的内容。"}]}]}, {"type": "multi-column-item", "id": "column2", "children": [{"type": "p", "id": "column2-content", "children": [{"text": "这是第二列的内容。"}]}]}]}, {"type": "p", "id": "text-formatting", "children": [{"text": "这是一个", "bgColor": "#f0f0f0"}, {"text": "文本格式", "fontColor": "#ff0000", "fontSize": 20}, {"text": "示例，包含", "italic": true}, {"text": "粗体", "bold": true}, {"text": "、", "underLine": true}, {"text": "删除线", "strike": true}, {"text": "和", "code": true}, {"text": "行内代码。"}]}]