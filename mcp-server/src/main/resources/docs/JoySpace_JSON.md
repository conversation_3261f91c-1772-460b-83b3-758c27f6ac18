# JoySpace 新文档类型数据结构

## 1 数据格式

多级嵌套的数据结构
*  数据结构伪代码
```
1    // 内容元素
2    interface TextElement {
3    text: string;
4    }
5    // 容器元素(块级/行内)
6    interface ContainerElement {
7    id: string;
8    type: string;
9    children: Array<ContainerElement | ContainerElement>
10   }
11 
12   //文档主体
13   ContainerElement[]
```
示例代码

▼ 内容代码
```json
[
    {
        "type": "p",
        "id": "cebwlM",
        "children": [
            {
                "text": "JoySpace 新文档类型数据结构"
            }
        ]
    },
{
        "type": "p",
        "id": "cebwlM1",
        "children": [
            {
                "text": "JoySpace 新文档类型数据结构"
            }
        ]
    },
    {
        "type": "p",
        "id": "hLMRgy",
        "children": [
            {
                "text": "1 数据格式",
                "fontSize": 26
            }
        ],
        "header": 1
    },
    {
        "type": "p",
        "id": "cFh2rj",
        "children": [
            {
                "text": "多级嵌套的数据结构"
            }
        ]
    },
    {
        "type": "block-code",
        "id": "VAjdPt",
        "children": [
            {
                "type": "block-code-line",
                "id": "jlR9cq",
                "children": [
                    {
                        "text": "// 内容元素"
                    }
                ]
            },
            {
                "type": "block-code-line",
                "id": "qC_bR9",
                "children": [
                    {
                        "text": "interface TextElement {"
                    }
                ]
            },
            {
                "type": "block-code-line",
                "id": "KJgsUe",
                "children": [
                    {
                        "text": "    text: string;"
                    }
                ]
            },
            {
                "type": "block-code-line",
                "id": "CvxQ6I",
                "children": [
                    {
                        "text": "}"
                    }
                ]
            },
            {
                "type": "block-code-line",
                "id": "j73MzN",
                "children": [
                    {
                        "text": "// 容器元素（块级/行内）"
                    }
                ]
            },
            {
                "type": "block-code-line",
                "id": "z36Cw9",
                "children": [
                    {
                        "text": "interface ContainerElement {"
                    }
                ]
            },
            {
                "type": "block-code-line",
                "id": "YoY694",
                "children": [
                    {
                        "text": "    id: string;"
                    }
                ]
            },
            {
                "type": "block-code-line",
                "id": "xgr55q",
                "children": [
                    {
                        "text": "    type: string;"
                    }
                ]
            },
            {
                "type": "block-code-line",
                "id": "GhL-De",
                "children": [
                    {
                        "text": "    children: Array<ContainerElement|ContainerElement>"
                    }
                ]
            },
            {
                "type": "block-code-line",
                "id": "okBze_",
                "children": [
                    {
                        "text": "}"
                    }
                ]
            }
        ],
        "lang": "javascript",
        "codeName": "数据结构伪代码"
    },
    {
        "type": "block-code",
        "id": "oPS5Py",
        "children": [
            {
                "type": "block-code-line",
                "id": "Pwe7UU",
                "children": [
                    {
                        "text": ""
                    }
                ]
            }
        ],
        "lang": "javascript"
    }
]
```

# 2 元素

## 2.1 元素类型

| 元素名称 | 类型(块级/行内) | type      | 可作为顶级元素 | 包含子元素 | 说明                                  |
| -------- | ------------ | --------- | -------- | -------- | ------------------------------------- |
| 段落     | 块级          | p         | 是        | 是        |                                       |
| 图片     | 块级          | img       | 是        | 是        |                                       |
| 列表     | 块级          | list      | 是        | 是        |                                       |
| 表格     | 块级          | table     | 是        | 是        | 直接子元素仅可以包含table-row            |
| 表格-行   | 块级          | table-row | 否        | 是        | 直接子元素仅可以包含table-cell          |
| 表格-元素  | 块级          | table-cell| 否        | 是        | 可以包含其他可以作为顶级的块级元素                |
| 分栏     | 块级          | multi-column| 是        | 是        | 直接子元素仅可以包含multi-column-item     |
| 分栏-列   | 块级          | multi- column-item | 否        | 是        | 直接子元素可以包含除分栏外的其他顶级的块级元素        |
| 代码块    | 块级          | block-code| 是        | 是        | 直接子元素仅可以包含block-code-line     |
| 代码块-行  | 块级          | block-code-line | 否        | 是        | 直接子元素仅可以包含文本                      |
| 引用     | 块级          | block- quote| 是        | 是        | 仅可以包含行内元素                             |
| 分割线    | 块级          | divider   | 是        | 否        |                                       |
| 高亮块    | 块级          | highlight-block | 是        | 是        | 直接子元素可以包含除高亮块外的其他顶级的块级元素      |
| 链接     | 行内          | link      | 否        | 是        | 子集仅可以包含文字元素                           |
| joyspace文档链接 | 行内          | docfile   | 否        | 否        |                               |
| 文本     | 行内          |           | 否        | 否        | 所有文字区域,无类型,无 children, text字段表示内容 |

## 2.2 通用属性

| 属性  | 类型     | 是否必选 | 说明                      |
| --- | ------ | ----- | ------------------------- |
| id  | 字符串   | 是    | 文档内全局唯一,不能重复             |
| type | 字符串   | 是    | 元素类型,不支持的元素类型会渲染为空 |

## 2.3 元素介绍

### 2.2.1 段落 (p)

| 属性    | 类型     | 是否必选 | 说明                     |
| ----- | ------ | ----- | ------------------------ |
| id    | 字符串   | 是    |                        |
| header | 数字     | 否    | 标题级别: 1-6分别表示不同级别的标题 |
| align | 字符串   | 否    | 对齐方式，可选值：            |
|       |        |       | - left 左对齐              |
|       |        |       | - right 右对齐             |
|       |        |       | - center 居中对齐          |
|       |        |       | - justify 两端对齐         |

| 属性    | 类型      | 是否必 选 | 说明                                              |
| :------ | :-------- | :-------- | :------------------------------------------------ |
| type    | 字符串    | 是        | p                                                 |
| header  | 数字      | 否        | 标题级别: 1-6分别表示不同级别的标题                  |
| align   | 字符串    | 否        | 对齐方式, 可选值:                                 |
|         |           |           | `left` 左对齐                                     |
|         |           |           | `right` 右对齐                                    |
|         |           |           | `center` 居中对齐                                  |
|         |           |           | `justify` 两端对齐                                |

### 2.2.2 列表

无序

| 属性      | 类型     | 是否必 选 | 说明                |
| :-------- | :------- | :-------- | :------------------ |
| type      | string   | 是        | 类型: `list`        |
| value     | string   | 是        | 固定值: `bullet`  |
| indent    | number   | 否        | 缩进                |
| align     | string   | 否        | 对齐方式, 可选值:   |
|         |        |         | `left` 左对齐        |
|         |        |         | `right` 右对齐       |
|         |        |         | `center` 居中对齐     |
| children  | array    | 是        | 行内元素             |

有序

| 属性        | 类型     | 是否必选 | 说明                        |
| ----------- | -------- | -------- | --------------------------- |
| type        | string   | 是       | 类型: list                  |
| value       | string   | 是       | 固定值: ordered            |
| indent      | number   | 否       | 缩进                        |
| start       | number   | 否       | 新的编号数字,默认1          |
| orderedType | string   | 否       | 列表类型: "Normal"        |
| align       | string   | 否       | 对齐方式,可选值:           |
|             |          |          | - left 左对齐              |
|             |          |          | - right 右对齐             |
|             |          |          | - center 居中对齐         |
| children    | array    | 是       | 行内元素                    |

任务

| 属性     | 类型     | 是否必选 | 说明                  |
| -------- | -------- | -------- | --------------------- |
| type     | string   | 是       | 类型: list            |
| value    | string   | 是       | 固定值: checkbox     |
| indent   | number   | 否       | 缩进                  |
| checked  | boolean  | 否       | 是否选中              |
| align    | string   | 否       | 对齐方式,可选值:     |
|          |          |          | - left  左对齐        |
|          |          |          | - right 右对齐        |
|          |          |          | - center 居中对齐    |
| children | array    | 是       | 行内元素              |

2.2.3 表格

| 属性     | 类型     | 是否必选 | 说明                                                                         |
| -------- | -------- | -------- | ---------------------------------------------------------------------------- |
| type     | string   | 是       | 类型: table                                                                  |
| width    | number[] | 是       |  固定值: 宽度数组,指定每个cell宽度及cell数量                                 |
| align    | string   | 否       | 对齐方式,可选值:                                                             |
|          |          |          | - left 左对齐                                                               |
|          |          |          | - right 右对齐                                                              |
|          |          |          | - center 居中对齐                                                           |
| children | array    | 是       | 行内元素                                                                     |

## 2.2.4 图片

| 属性     | 类型   | 是否必选 | 说明           |
| -------- | ------ | -------- | -------------- |
| type     | string | 是       | 类型,img       |
| url      | string | 是       | url地址,图片地址 |
| width    | number | 否       |                |
| height   | number | 否       |                |
| align    | string | 否       | 对齐方式,可选值: |
|          |        |          | - left 左对齐   |
|          |        |          | - right 右对齐  |
|          |        |          | - center 居中对齐 |
| children | array  | 是       | 空串:[{text:""}] |

## 2.2.5 链接

| 属性      | 类型     | 是否必选 | 说明                            |
| --------- | -------- | -------- | ------------------------------- |
| type      | string   | 是       | 类型:link                       |
| url       | string   | 是       | url地址                         |
| childre   | array    | 是       | 仅可以使用文字子类型              |
| n         |          |          |                                 |

## 2.2.6 文字

| 属性      | 类型      | 是否必选 | 说明                               |
| --------- | --------- | -------- | ---------------------------------- |
| text      | string    | 是       | 文字内容                             |
| bgColor   | string    | 否       | 背景颜色: rgb #ffffff,默认无           |
| or        |           |          |                                    |
| fontColor | string    | 否       | 字体颜色: 默认 #22222               |
| fontSize  | number    | 否       | 文字大小,默认16,                     |
| e         | r         |          |                                    |
| italic    | boolean   | 否       | 倾斜,默认false                       |
| n         |           |          |                                    |
| bold      | boolean   | 否       | 加粗,默认false                       |
| n         |           |          |                                    |
| underLine | boolean   | 否       | 下划线,默认false                     |
| ne        | n         |          |                                    |
| strike    | boolean   | 否       | 中划线,默认false                     |
| n         |           |          |                                    |
| code      | boolean   | 否       | 行内高亮,默认false                   |
| n         |           |          |                                    |