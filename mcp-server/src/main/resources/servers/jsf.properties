# JSF开放平台MCP服务配置文件

# JSF开放API配置
jsf.openapi.appKey=jdos_ofw-outbound
jsf.openapi.token=Jk7J2Lp9XmN4QwR8vT3sB6yH1gF5dE0z
jsf.openapi.index=test.i.jsf.jd.local

# 服务描述
jsf.service.name=JSF开放平台服务
jsf.service.description=提供JSF开放平台的接口查询、方法信息获取、别名查询等功能
jsf.service.version=1.0.0

# 默认配置
jsf.default.operator=system
jsf.default.timeout=5000
jsf.default.retries=3

# 测试接口配置
jsf.test.interface=erp.ql.station.api.service.gangao.CustomsClearanceApi
jsf.test.operator=testOperator
