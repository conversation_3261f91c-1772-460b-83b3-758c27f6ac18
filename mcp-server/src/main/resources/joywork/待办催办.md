# 待办催办

| 基本        | 说明                                                                              |
| ----------- | --------------------------------------------------------------------------------- |
| HTTP URL    | {{host}}/open-api/suite/v1/joywork/urgeTask                                      |
| HTTP Method | POST                                                                              |

# 请求头

| 名称          | 类型     | 必填 | 描述                          |
| ------------- | -------- | ---- | ----------------------------- |
| authorization | string | 是   | Bearer ${team_access_token} |

# 参数

| 名称          | 类型     | 必填 | 描述       |
| ------------- | -------- | ---- | ---------- |
| taskld        | String | Y    | 任务id     |
| urgeContent   | String | Y    | 催办内容   |
| taskUsers     | array    |      | 抄送人     |
| └erp         | string |      | erp        |
| └teamld      | string |      | 国内00046419 |
| └app         | string |      | 国内ee     |

# 参数示例
```json
{
  "taskId":"string",
  "urgeContent":"xxxx",// 催办内容
  "taskUsers":[          //  抄送人
    {
        "erp":"ceshi",
        "teamId":"xxxx",   // 国内00046419
        "app":"xxxx"       // 国内ee
    }
  ]  
}
```
响应

响应体

| 名称 | 类型 | 描述 |
| --- | --- | --- |
| `code` | `int` | 错误码,非0取值表示失败 |
| `msg` | `string` | 错误信息描述 |
| `data` | `object` | 业务数据信息 |

响应示例

```json
{"code":0,"msg":"string","data":{}}
```
