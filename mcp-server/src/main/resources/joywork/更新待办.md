# 更新待办

| 基本     | 说明                                               |
| -------- | -------------------------------------------------- |
| HTTP URL | {{host}}/open-api/suite/v1/joywork/updateTask |
| HTTP Method | POST                                               |

# 请求头

| 名称            | 类型   | 必填 | 描述 |
| --------------- | ------ | ---- | ---- |
| authorization | string | 是   | Bearer `${team_access_to_ken}`|

# 参数

| 名称               | 类型   | 必填 | 描述                               |
| ------------------ | ------ | ---- | ---------------------------------- |
| taskld             | String | Y    | 任务id                               |
| title              | String |      | 任务标题                             |
| remark             | String |      | 任务背景                             |
| startTime          | Number |      | 开始时间,毫秒时间戳                   |
| endTime            | Number |      | 结束时间,毫秒时间戳                   |
| sysProjectid       | String |      | 京东人事清单id                       |
| sysGroupld         | String |      | 京东人事清单分组id                    |
| sourceDescZh       | String |      | 详情页去处理中文文案                    |
| sourceDescEn       | String |      | 详情页去处理英文文案                    |
| sourceTrusteeship | Number |      | 是否能从joywork完成该任务,0否,1是 |

参数示例

```json
{
    "taskId":"string",
    "title": "string",
    "remark":"string",
    "startTime":0,
    "endTime":0,
    "priorityType":0,
    "content":"string",
    "mobileContent": "string",
    "extend":"string",
    "sysProjectId":"xxxxx",
    "sysGroupId":"xxxxx"
}
```

响应

响应体

| 名称   | 类型   | 描述                 |
| ---- | ---- | ------------------ |
| code | int  | 错误码,非0取值表示失败      |
| msg  | string | 错误信息描述             |
| data | object | 业务数据信息             |

响应示例

```json
{
    "code":0,
    "msg":"string",
    "data":{}
}
```