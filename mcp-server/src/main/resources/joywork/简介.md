# 简介

## 业务介绍

JoyWork（京ME任务）是京东统一的任务管理平台，目的是辅助管理者、员工更加轻松
的委派、跟踪每一项工作任务，让所有的“事”都可以高效的落地。现在，JoyWork和
咚咚群、JoySpace都做了深度集成，你可以在项目群中、会议纪要文档中直接创建待办
任务，与团队成员快速发起协作，解决群中沟通凌乱、会议待办分散在多处带来的难以跟
进等痛点。

## 开放API

任务 API 基于joywork任务功能开放了对任务管理能力，你可以实现多种功能，例如:

*   任务创建
*   任务更新
*   更新任务状态

## 接入流程

|     | 步骤     | 介绍                          |
| --- | -------- | ----------------------------- |
| 1   | 创建应用 | [京东me开发者后台](https://me.jd.com/center/v2/home.action)创建应用 |
| 2   | 申请权限 | 给业务应用申请需要的API权限      |
| 3   | 业务接入 | 业务调试API接口                |

## 方法列表

| 方法(API) | 链接 | 权限要求 | 访问凭证 |
|---|---|---|---|
| 任务创建 | POST<br>{{host}}/open-api/suite/v1/joywork/syncTask | 任务创建 | Team_access token |
| 任务更新 | POST <br>{{host}}/open-api/suite/v1/joywork/updateTask | 任务更新 | Team_access token |
| 更新任务状态 | POST <br>{{host}}/open-api/suite/v2/joywork/updateTaskStatus | 更新任务状态 | Team_access token |
| 任务催办 | POST<br>{{host}}/open-api/suite/v1/joywork/urgeTask | 任务催办 | Team_access token |