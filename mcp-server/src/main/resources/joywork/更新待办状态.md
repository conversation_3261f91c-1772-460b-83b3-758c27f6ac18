# 更新待办状态

| 基本      | 说明                                                     |
| :-------- | :------------------------------------------------------- |
| HTTP URL  | {{host}}/open-api/suite/v2/joywork/updateTaskStatus    |
| HTTP Method | POST                                                  |

# 请求头

| 名称            | 类型     | 必填 | 描述                           |
| :-------------- | :------- | :--- | :----------------------------- |
| authorization | string | 是   | Bearer ${team_access_token} |

# 参数

| 名称        | 类型     | 必填 | 描述                                                          |
| :---------- | :------- | :--- | :------------------------------------------------------------ |
| taskld      | String | Y    | 任务id                                                       |
| openUserld   | String | Y    | 操作人openUserld,一般为系统的虚拟账号                             |
| taskStatus  | String | Y    | 任务状态。 taskStatus = 1 未完成; taskStatus = 2 完成        |

# 参数示例

```json
{
  "openUserId":"string",
  "taskId":"string",
  "taskStatus":0
}
```

# 响应

# 响应体

| 名称   | 类型     | 描述                     |
| ------ | -------- | ------------------------ |
| code   | int      | 错误码,非0取值表示失败   |
| msg    | string   | 错误信息描述             |
| data   | object   | 业务数据信息             |

# 响应示例

```json
{"code":0,"msg":"string", "data":{}}
```