# 创建待办

| 基本     | 说明                                         |
| -------- | -------------------------------------------- |
| HTTP URL | {{host}}/open-api/suite/v1/joywork/syncTask |
| HTTP Method | POST                                         |

## 请求头

| 名称            | 类型   | 必填 | 描述                               |
| --------------- | ------ | ---- | ---------------------------------- |
| authorization | string | 是   | Bearer\
                                     ${team_access_to_ken} |

## 参数

| 名称          | 类型     | 必填 | 描述                                                             |
| ------------- | -------- | ---- | ---------------------------------------------------------------- |
| app           | String   | Y    | 应用标识                                                           |
| bizCode       | String   |      | joywork分配第三方code,联系@张玉放 @章万康分配                  |
| extend        | Array    |      | 扩展信息                                                           |
| content       | Object   |      |                                                                  |
| \_tips        | String   | N    |                                                                  |
| \_type        | String   | N    |                                                                  |
| \_content     | String   | N    | pc端跳转链接                                                     |
| mobileContent | String   |      | 手机端跳转链接                                                   |
| openUserld    | String   |      | 创建人openUserld,一般为系统的虚拟账号                             |
| ownerList     | Array    | Y    | 执行人                                                           |
| \_openUserld  | String   | Y    | getOpenUserInfoInner接口获取                                     |

|               |        |   |                                                                                                          |
| :------------ | :----- | :-: | :------------------------------------------------------------------------------------------------------- |
| _taskUserRole | String |  Y  | CREATOR(0, "创建人"),OWNER(1,"负责人"),EXECUTOR(2,“执行人"),LOOK_UP(3,"关注人"),JOIN_ROLE(4,"参与人") |
| sourceld      | String |     | 业务唯一主键                                                                                           |
| title         | String |     | 任务标题                                                                                                 |
| remark        | String |     | 任务背景                                                                                                 |
| startTime     | Number |     | 开始时间，毫秒时间戳                                                                                             |
| endTime       | Number |     | 结束时间，毫秒时间戳                                                                                             |
| sysProjectid  | String |     | 京东人事清单id                                                                                           |
| sysGroupld    | String |     | 京东人事清单分组id                                                                                         |
| sourceDescZh  | String |     | 详情页去处理中文文案                                                                                             |
| sourceDescEn  | String |     | 详情页去处理英文文案                                                                                             |
| sourceTrusteeship | Number |     | 是否能从joywork完成该任务，0否，1是                                                                                  |

# 参数示例

```json
{
    "app":"ee",
    "bizCode":"884a08fae11e4b458caae99cd9722018",// joywork分配三方系统code
    "extend":[{
        "content":"3",
        "tips":"条",
        "type":"text"
    }],// 扩展信息
    "content":"http://jd.com",// pc端跳转链接
    "mobileContent":"jdme://jm/biz/appcenter/${appId}",// 移动端跳转链接, appId为应用的SN码
    "openUserId":"8c48b6a3d6574af7c13e5f56eeee0bc3",// 创建人openUserId,一般为系统的虚拟账号
    "ownerList":[{
        "openUserId":"8c48b6a3d6574af7c13e5f56eeee0bc3",
        "taskUserRole":"OWNER"
    }],// 执行人
    "sourceId":"ces_count_111",// 业务唯一主键
    "title":"ces_count_111",// 任务标题
    "remark":"这是任务背景",// 任务背景
    "startTime":1641892247344,// 开始时间，毫秒时间戳
    "endTime":  1641892247344,// 结束时间，毫秒时间戳
    "sysProjectId":"xxxxx",// 京东人事清单id
    "sysGroupId":"xxxxx",// 京东人事清单分组id
    "sourceDescZh":"xxxxx",// 详情页去处理中文文案
    "sourceDescEn":"xxxxx",// 详情页去处理英文文案
    "sourceTrusteeship":0// 是否能从joywork完成该任务，0否，1是
}
```

# 响应

响应体

| 名称   | 类型   | 描述                    |
| ------ | ------ | ----------------------- |
| code   | int    | 错误码,非0取值表示失败 |
| msg    | string | 错误信息描述            |
| data   | object | 业务数据信息            |
| taskld | string | 生成的任务id            |

响应示例

```json
{
    "code":0,
    "msg":"string",
    "data":{
        "taskId":"string"
    }
}
```