# App configuration
# openme.app.key=RUjHcGB2uMvqbvfXxbip4
# openme.app.secret=qJOwjyOJBoIwHMHLn8w5

#这个是区分国内，泰国、印尼的参数
# openme.app.teamId=00046419
# openme.app.openTeamId=88904676cbddd3ab14f037bd335d9423
openme.app.host=http://openme-test.jd.com


openme.joyspace.scene=TripitakaLibrary
openme.joyspace.userId=zhibeibei3
#这个是js空间的id
openme.joyspace.teamId=JuJpAXsTgawHjpow9r75Z
openme.joyspace.x_stage=

openme.joywork.app=ee
openme.joywork.bizCode=wxlH2M7IMWlJzJZw9BKBaf3aLow6vq
openme.joywork.openUserId=a58bdaca8b2115f29891531288dfabc4
openme.joywork.teamId=00046419




#cjg 测试账号
openme.app.key=aReooQTi8UOCHNOGgoIj1
openme.app.secret=9PZc9IGyglK4j18C8YWd
openme.app.openTeamId=a58bdaca8b2115f29891531288dfabc4
openme.app.teamId=00046419
# joywork:
#   appkey: aReooQTi8UOCHNOGgoIj1
#   appsecret: 9PZc9IGyglK4j18C8YWd
#   openteamid: a58bdaca8b2115f29891531288dfabc4
#   teamid: "00046419"
#   stage: PROD
#   bizcode: gCAoYb6qXAjia26bgBwUuCf871CP2L
#   enname: rule_platform
#   content: #京me任务跳转的url
#     deploy: http://biz-rule-config-test1.local-pf.jd.com/myTask/taskPublish/%s #执行发布任务
#     collect: http://biz-rule-config-test1.local-pf.jd.com/myTask/TaskDistributionRule/%s #业务域配置收集
#     config: http://biz-rule-config-test1.local-pf.jd.com/myTask/TaskSubmissionSolution/%s #业务域规则编排任务