package com.jdl.mcp.server.jsf.service;

import com.jd.jsf.open.api.vo.InterfaceInfo;
import com.jdl.mcp.server.jsf.model.JsfResult;

import java.util.List;

/**
 * JSF接口信息服务
 * 
 * <AUTHOR> Assistant
 */
public interface JsfInterfaceService {
    
    /**
     * 根据接口名称查询接口信息
     * 
     * @param interfaceName 接口名称
     * @param operator 操作人erp
     * @return 接口信息
     */
    JsfResult<InterfaceInfo> getByInterfaceName(String interfaceName, String operator);
    
    /**
     * 获取接口对应的方法入参与出参信息
     * 此方法最终通过telnet到存活的Provider，获取方法信息进行返回
     * 
     * @param interfaceName 接口名称
     * @param alias 指定别名，可选
     * @param ip 指定IP，可选
     * @param port 指定端口，可选
     * @param methodName 方法名，可选
     * @param operator 操作人erp
     * @return 方法详细信息的JSON字符串
     */
    JsfResult<String> getMethodInfo(String interfaceName, String alias, String ip, Integer port, String methodName, String operator);
    
    /**
     * 获取方法列表
     * 
     * @param interfaceName 接口名称
     * @param alias 指定别名，可选
     * @param ip 指定IP，可选
     * @param port 指定端口，可选
     * @param methodName 方法名，可选
     * @param operator 操作人erp
     * @return 方法列表
     */
    JsfResult<List<String>> getMethodList(String interfaceName, String alias, String ip, Integer port, String methodName, String operator);
}
