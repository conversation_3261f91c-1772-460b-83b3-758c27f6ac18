package com.jdl.mcp.server.openme.joyspace.converter;


import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class JsonToMarkdownConverterRunner {

    public static void main(String[] args) {
        JoySpaceJsonToMarkdownConverter converter = new JoySpaceJsonToMarkdownConverter();
        List<String> jsonFilePaths = Arrays.asList(
            "./src/main/resources/docs/JoySpace_Full_Demo.json",
            "./src/main/resources/docs/getPageContent.json"
        );

        try {
            List<String> markdownFilePaths = converter.convertJsonFilesToMarkdownFiles(jsonFilePaths);
            System.out.println("Conversion completed. Markdown files created at:");
            for (String path : markdownFilePaths) {
                System.out.println(path);
            }
        } catch (IOException e) {
            System.err.println("Error occurred during conversion: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

// cd mcp-server && mvn compile exec:java -Dexec.mainClass="com.jdl.mcp.server.openme.joyspace.converter.JsonToMarkdownConverterRunner"