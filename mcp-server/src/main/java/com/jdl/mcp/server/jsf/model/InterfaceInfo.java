package com.jdl.mcp.server.jsf.model;

/**
 * 接口信息实体类
 * 
 * <AUTHOR> Assistant
 */
public class InterfaceInfo {
    
    /**
     * 接口ID
     */
    private Long id;
    
    /**
     * 接口名称
     */
    private String interfaceName;
    
    /**
     * 服务类型, 1: JSF; 2: containerMesh; 默认: 1
     */
    private Integer serviceType;
    
    /**
     * 服务支持类型
     */
    private Integer supportType;
    
    /**
     * 消费者总数
     */
    private Integer consumerTotal;
    
    /**
     * 提供者总数
     */
    private Integer providerTotal;
    
    /**
     * 存活的提供者数量
     */
    private Integer providerLive;
    
    /**
     * 存活的消费者数量
     */
    private Integer consumerLive;
    
    /**
     * 来源, 1-saf1.0, 2-saf2.1
     */
    private Integer src;
    
    /**
     * 部门编码
     */
    private String departmentCode;
    
    /**
     * 部门名称
     */
    private String department;
    
    /**
     * 跨语言支持
     */
    private Integer crossLang;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 重要性
     */
    private Integer important;
    
    /**
     * 接口信息状态, 是否有效【0: 无效; 1: 已审核; 2: 新建(待提交); 3: 其他】
     */
    private Integer valid;
    
    /**
     * 负责人
     */
    private String ownerUser;
    
    /**
     * 创建时间
     */
    private Long createdTime;
    
    /**
     * 应用调用
     */
    private Integer appInvoke;
    
    /**
     * 是否有jsf客户端
     */
    private Integer hasJsfClient;
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getInterfaceName() {
        return interfaceName;
    }
    
    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
    }
    
    public Integer getServiceType() {
        return serviceType;
    }
    
    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }
    
    public Integer getSupportType() {
        return supportType;
    }
    
    public void setSupportType(Integer supportType) {
        this.supportType = supportType;
    }
    
    public Integer getConsumerTotal() {
        return consumerTotal;
    }
    
    public void setConsumerTotal(Integer consumerTotal) {
        this.consumerTotal = consumerTotal;
    }
    
    public Integer getProviderTotal() {
        return providerTotal;
    }
    
    public void setProviderTotal(Integer providerTotal) {
        this.providerTotal = providerTotal;
    }
    
    public Integer getProviderLive() {
        return providerLive;
    }
    
    public void setProviderLive(Integer providerLive) {
        this.providerLive = providerLive;
    }
    
    public Integer getConsumerLive() {
        return consumerLive;
    }
    
    public void setConsumerLive(Integer consumerLive) {
        this.consumerLive = consumerLive;
    }
    
    public Integer getSrc() {
        return src;
    }
    
    public void setSrc(Integer src) {
        this.src = src;
    }
    
    public String getDepartmentCode() {
        return departmentCode;
    }
    
    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public Integer getCrossLang() {
        return crossLang;
    }
    
    public void setCrossLang(Integer crossLang) {
        this.crossLang = crossLang;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public Integer getImportant() {
        return important;
    }
    
    public void setImportant(Integer important) {
        this.important = important;
    }
    
    public Integer getValid() {
        return valid;
    }
    
    public void setValid(Integer valid) {
        this.valid = valid;
    }
    
    public String getOwnerUser() {
        return ownerUser;
    }
    
    public void setOwnerUser(String ownerUser) {
        this.ownerUser = ownerUser;
    }
    
    public Long getCreatedTime() {
        return createdTime;
    }
    
    public void setCreatedTime(Long createdTime) {
        this.createdTime = createdTime;
    }
    
    public Integer getAppInvoke() {
        return appInvoke;
    }
    
    public void setAppInvoke(Integer appInvoke) {
        this.appInvoke = appInvoke;
    }
    
    public Integer getHasJsfClient() {
        return hasJsfClient;
    }
    
    public void setHasJsfClient(Integer hasJsfClient) {
        this.hasJsfClient = hasJsfClient;
    }
    
    @Override
    public String toString() {
        return "InterfaceInfo{" +
                "id=" + id +
                ", interfaceName='" + interfaceName + '\'' +
                ", serviceType=" + serviceType +
                ", supportType=" + supportType +
                ", consumerTotal=" + consumerTotal +
                ", providerTotal=" + providerTotal +
                ", providerLive=" + providerLive +
                ", consumerLive=" + consumerLive +
                ", src=" + src +
                ", departmentCode='" + departmentCode + '\'' +
                ", department='" + department + '\'' +
                ", crossLang=" + crossLang +
                ", remark='" + remark + '\'' +
                ", important=" + important +
                ", valid=" + valid +
                ", ownerUser='" + ownerUser + '\'' +
                ", createdTime=" + createdTime +
                ", appInvoke=" + appInvoke +
                ", hasJsfClient=" + hasJsfClient +
                '}';
    }
}
