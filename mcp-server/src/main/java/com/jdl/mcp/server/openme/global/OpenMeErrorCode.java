package com.jdl.mcp.server.openme.global;

public enum OpenMeErrorCode {
    // 网关平台错误
    INVALID_API(999900, "无效的api"),
    BACKEND_SERVICE_ERROR(999900, "路由后端服务异常"),
    MISSING_API(999900, "缺少api"),
    UNSUPPORTED_CONTENT_TYPE(999900, "请求的content-type不支持"),
    MISSING_AUTHENTICATION(999900, "参数错误,缺少认证信息"),
    PARSE_REQUEST_ERROR(999900, "解析请求参数内容异常"),
    INVALID_API_STATUS(999900, "api状态无效,请发布api"),
    NO_BACKEND_ROUTE(999900, "API没有配置对应的后端路由"),
    WRITE_RESPONSE_ERROR(999900, "写入响应异常"),
    SERVICE_BAD(999900, "Service Bad"),
    GATEWAY_BAD(999900, "GateWay Bad"),

    // 开放平台错误
    AUTH_SERVICE_ERROR(9999010, "授权服务异常"),
    INVALID_APP_INFO(9999010, "应用信息不合法"),
    AUTH_CHECK_ERROR(9999010, "授权校验异常"),
    INVALID_ACCOUNT_INFO(9999010, "账号信息不合法"),
    ACCESS_FREQUENCY_LIMIT(9999010, "访问频次限制"),
    TOKEN_GENERATION_FAILED(9999011, "token生成失败"),
    INVALID_APP_TOKEN(9999000, "Open APP Token 无效"),
    INVALID_TEAM_TOKEN(9999000, "Open Team Token 无效"),
    INVALID_USER_TOKEN(9999000, "Open User Access Token 无效"),
    INVALID_REFRESH_TOKEN(9999000, "Open User Refresh Token无效"),
    INVALID_CODE(9999011, "无效的code"),

    // 其他错误
    INVALID_HTTP_HEADER(********, "HTTP Header不合法"),
    INVALID_HTTP_PARAM(********, "HTTP 参数不合法"),
    INVALID_PARAM_CHECK(********, "参数校验不合法"),
    UNKNOWN_ERROR(********, "未知错误"),
    IP_NO_PERMISSION(********, "IP无权限访问"),
    APP_UNAUTHORIZED(********, "应用未授权"),
    AES_PARSE_ERROR(********, "AES解析异常"),
    RESPONSE_BODY_ERROR(********, "响应体异常"),
    NO_PERMISSION(********, "无权限操作"),
    MISSING_PARAM(********, "参数缺失"),
    FIELD_GET_ERROR(99990125, "字段获取异常"),
    USER_NOT_FOUND(99990126, "没有此用户"),
    DEPARTMENT_NOT_FOUND(99990127, "没有此部门"),
    APP_NOT_IN_WHITELIST(99990129, "白名单内无此应用"),
    DUPLICATE_DEPARTMENT_NAME(99990130, "父部门下已有此名称部门"),
    PUSH_FAILED(99990131, "推送失败"),
    API_ERROR(99990132, "API调用错误");

    private final int code;
    private final String message;

    OpenMeErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
