package com.jdl.mcp.server.openme.joyspace.impl;

import com.jdl.mcp.server.openme.joyspace.JoyspaceService;
import com.jdl.mcp.server.openme.joyspace.param.GetFolderListParam;
import com.jdl.mcp.server.openme.joyspace.param.GetFileListParam;
import com.jdl.mcp.server.openme.joyspace.param.GetPageInfosParam;
import com.jdl.mcp.server.openme.joyspace.param.GetPageContentParam;
import com.jdl.mcp.server.openme.joyspace.param.GetLocationParam;
import com.jdl.mcp.server.openme.joyspace.param.GetAttachmentPresignUrlParam;
import com.jdl.mcp.core.utils.Utils;
import com.jdl.mcp.server.openme.auth.OpenMeAuthService;
import com.jdl.mcp.server.openme.auth.OpenUserInfo;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class JoyspaceServiceImpl implements JoyspaceService {

    private static final Logger logger = LoggerFactory.getLogger(JoyspaceServiceImpl.class);

    @Autowired
    private OpenMeAuthService openMeAuthService;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${openme.app.host}")
    private String host;

    @Value("${openme.joyspace.scene}")
    private String scene;

    @Value("${openme.joyspace.userId}")
    private String userId;

    @Value("${openme.joyspace.teamId}")
    private String teamId;

    @Value("${openme.joyspace.x_stage}")
    private String x_stage;

    @Override
    public JSONObject getFolderList(GetFolderListParam param) {
        try {
            String url = host + "/open-api/suite/v1/joyspace/getFolderList";

            HttpHeaders headers = new HttpHeaders();
            Map<String, Object> requestBody = new HashMap<>();
            buildHttpParam(headers,requestBody);
            requestBody.put("teamId", teamId);
            requestBody.put("folderId", param.getFolderId());
            requestBody.put("creators", param.getCreators());
            requestBody.put("sort", param.getSort());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            logger.info("getFolderList request: URL = {}, Headers = {}, Body = {}", url, headers, requestBody);

            String rawResponse = restTemplate.postForObject(url, request, String.class);
            logger.info("getFolderList raw response: {}", rawResponse);

            JSONObject jsonResponse = new JSONObject(rawResponse);
            String msg = jsonResponse.getString("msg");
            int code = jsonResponse.getInt("code");

            if ("success".equals(msg) && code == 0) {
                logger.info("getFolderList successful. Response: {}", jsonResponse);
                return jsonResponse;
            } else {
                logger.error("Failed to get folder list. Raw response: {}", rawResponse);
                throw new RuntimeException("Failed to get folder list. Raw response: " + rawResponse);
            }
        } catch (Exception e) {
            logger.error("Error occurred while getting folder list", e);
            throw new RuntimeException("Error occurred while getting folder list: " + e.getMessage(), e);
        }
    }

    @Override
    public JSONObject getFileList(GetFileListParam param) {
        try {
            String url = host + "/open-api/suite/v1/joyspace/getFileListOfFolder";

            HttpHeaders headers = new HttpHeaders();
            Map<String, Object> requestBody = new HashMap<>();
            buildHttpParam(headers,requestBody);
            requestBody.put("teamId", teamId);
            requestBody.put("folderId", param.getFolderId());
            requestBody.put("creators", param.getCreators());
            requestBody.put("pageTypes", param.getPageTypes());
            requestBody.put("sort", param.getSort());
            requestBody.put("start", param.getStart());
            requestBody.put("length", param.getLength());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            logger.info("getFileList request: URL = {}, Headers = {}, Body = {}", url, headers, requestBody);

            String rawResponse = restTemplate.postForObject(url, request, String.class);
            logger.info("getFileList raw response: {}", rawResponse);

            JSONObject jsonResponse = new JSONObject(rawResponse);
            String msg = jsonResponse.getString("msg");
            int code = jsonResponse.getInt("code");

            if ("success".equals(msg) && code == 0) {
                logger.info("getFileList successful. Response: {}", jsonResponse);
                return jsonResponse;
            } else {
                logger.error("Failed to get file list. Raw response: {}", rawResponse);
                throw new RuntimeException("Failed to get file list. Raw response: " + rawResponse);
            }
        } catch (Exception e) {
            logger.error("Error occurred while getting file list", e);
            throw new RuntimeException("Error occurred while getting file list: " + e.getMessage(), e);
        }
    }

    @Override
    public JSONObject getPageInfos(GetPageInfosParam param) {
        try {
            String url = host + "/open-api/suite/v1/joyspace/getPageInfos";

            HttpHeaders headers = new HttpHeaders();
            Map<String, Object> requestBody = new HashMap<>();
            buildHttpParam(headers,requestBody);
            requestBody.put("pageIds", param.getPageIds());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            logger.info("getPageInfos request: URL = {}, Headers = {}, Body = {}", url, headers, requestBody);

            String rawResponse = restTemplate.postForObject(url, request, String.class);
            logger.info("getPageInfos raw response: {}", rawResponse);

            JSONObject jsonResponse = new JSONObject(rawResponse);
            String msg = jsonResponse.getString("msg");
            int code = jsonResponse.getInt("code");

            if ("success".equals(msg) && code == 0) {
                logger.info("getPageInfos successful. Response: {}", jsonResponse);
                return jsonResponse;
            } else {
                logger.error("Failed to get page infos. Raw response: {}", rawResponse);
                throw new RuntimeException("Failed to get page infos. Raw response: " + rawResponse);
            }
        } catch (Exception e) {
            logger.error("Error occurred while getting page infos", e);
            throw new RuntimeException("Error occurred while getting page infos: " + e.getMessage(), e);
        }
    }

    @Override
    public JSONObject getPageContent(GetPageContentParam param) {
        try {
            String url = host + "/open-api/suite/v1/joyspace/getPageContent";

            HttpHeaders headers = new HttpHeaders();
            Map<String, Object> requestBody = new HashMap<>();
            buildHttpParam(headers,requestBody);
            requestBody.put("pageId", param.getPageId());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            //logger.info("getPageContent request: URL = {}, Headers = {}, Body = {}", url, headers, requestBody);

            String rawResponse = restTemplate.postForObject(url, request, String.class);
            logger.info("getPageContent raw response: {}", rawResponse);

            JSONObject jsonResponse = new JSONObject(rawResponse);
            String msg = jsonResponse.getString("msg");
            int code = jsonResponse.getInt("code");

            if ("success".equals(msg) && code == 0) {
                //logger.info("getPageContent successful. Response: {}", jsonResponse);
                return jsonResponse;
            } else {
                //logger.error("Failed to get page content. Raw response: {}", rawResponse);
                throw new RuntimeException("Failed to get page content. Raw response: " + rawResponse);
            }
        } catch (Exception e) {
            logger.error("Error occurred while getting page content", e);
            throw new RuntimeException("Error occurred while getting page content: " + e.getMessage(), e);
        }
    }

    @Override
    public JSONObject getLocation(GetLocationParam param) {
        try {
            String url = host + "/open-api/suite/v1/joyspace/getLocation";

            HttpHeaders headers = new HttpHeaders();
            Map<String, Object> requestBody = new HashMap<>();
            buildHttpParam(headers,requestBody);
            requestBody.put("pageId", param.getPageId());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            logger.info("getLocation request: URL = {}, Headers = {}, Body = {}", url, headers, requestBody);

            String rawResponse = restTemplate.postForObject(url, request, String.class);
            logger.info("getLocation raw response: {}", rawResponse);

            JSONObject jsonResponse = new JSONObject(rawResponse);
            String msg = jsonResponse.getString("msg");
            int code = jsonResponse.getInt("code");

            if ("success".equals(msg) && code == 0) {
                logger.info("getLocation successful. Response: {}", jsonResponse);
                return jsonResponse;
            } else {
                logger.error("Failed to get location. Raw response: {}", rawResponse);
                throw new RuntimeException("Failed to get location. Raw response: " + rawResponse);
            }
        } catch (Exception e) {
            logger.error("Error occurred while getting location", e);
            throw new RuntimeException("Error occurred while getting location: " + e.getMessage(), e);
        }
    }

    @Override
    public JSONObject getAttachmentPresignUrl(GetAttachmentPresignUrlParam param) {
        try {
            String url = host + "/open-api/suite/v1/joyspace/getAttachmentPresignUrl";

            HttpHeaders headers = new HttpHeaders();
            Map<String, Object> requestBody = new HashMap<>();
            buildHttpParam(headers,requestBody);
            requestBody.put("pageId", param.getPageId());
            requestBody.put("attachmentId", param.getAttachmentId());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            logger.info("getAttachmentPresignUrl request: URL = {}, Headers = {}, Body = {}", url, headers, requestBody);

            String rawResponse = restTemplate.postForObject(url, request, String.class);
            logger.info("getAttachmentPresignUrl raw response: {}", rawResponse);

            JSONObject jsonResponse = new JSONObject(rawResponse);
            String msg = jsonResponse.getString("msg");
            int code = jsonResponse.getInt("code");

            if ("success".equals(msg) && code == 0) {
                logger.info("getAttachmentPresignUrl successful. Response: {}", jsonResponse);
                return jsonResponse;
            } else {
                logger.error("Failed to get attachment presign URL. Raw response: {}", rawResponse);
                throw new RuntimeException("Failed to get attachment presign URL. Raw response: " + rawResponse);
            }
        } catch (Exception e) {
            logger.error("Error occurred while getting attachment presign URL", e);
            throw new RuntimeException("Error occurred while getting attachment presign URL: " + e.getMessage(), e);
        }
    }

    private void buildHttpParam(HttpHeaders headers,Map<String, Object> requestBody) {
        String appAccessToken = openMeAuthService.getAppAccessToken();
        String teamAccessToken = openMeAuthService.getTeamAccessToken(appAccessToken);
        OpenUserInfo userInfo = openMeAuthService.getOpenUserInfo(teamAccessToken, userId);

        headers.setContentType(MediaType.APPLICATION_JSON);
        if(!Utils.isEmpty(x_stage)){
            headers.set("x-stage",  x_stage);
        }
        headers.set("authorization", "Bearer " + teamAccessToken);

        requestBody.put("openUserId", userInfo.getOpenUserId());
        requestBody.put("openTeamId", userInfo.getOpenTeamId());
        requestBody.put("scene", scene);
    }
}
