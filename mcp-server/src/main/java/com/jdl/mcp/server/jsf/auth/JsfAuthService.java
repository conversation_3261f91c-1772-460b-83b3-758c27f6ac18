package com.jdl.mcp.server.jsf.auth;

import com.jdl.mcp.server.jsf.param.BaseJsfRequest;

/**
 * JSF认证服务接口
 * 
 * <AUTHOR> Assistant
 */
public interface JsfAuthService {
    
    /**
     * 构建通用认证参数
     * 
     * @param operator 操作人erp
     * @return 包含认证信息的基础请求对象
     */
    BaseJsfRequest buildCommonParams(String operator);
    
    /**
     * 生成签名
     * 
     * @param appKey 应用Key
     * @param timeStamp 时间戳
     * @param token 应用token
     * @return MD5签名字符串
     */
    String generateSign(String appKey, Long timeStamp, String token);
    
    /**
     * 获取本机IP地址
     * 
     * @return 本机IP地址
     */
    String getLocalHost();
}
