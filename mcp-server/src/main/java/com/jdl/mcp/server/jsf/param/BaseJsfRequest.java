package com.jdl.mcp.server.jsf.param;

import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;

/**
 * JSF基础请求参数类
 * 包含通用的认证参数
 * 
 * <AUTHOR> Assistant
 */
public class BaseJsfRequest {
    
    /**
     * 应用Key
     */
    private String appKey;
    
    /**
     * 操作人erp
     */
    private String operator;
    
    /**
     * 调用者ip地址
     */
    private String clientIp;
    
    /**
     * 当前时间的时间戳
     */
    private Long timeStamp;
    
    /**
     * 签名信息 sign=md5(utf8(appKey+timeStamp+token))
     */
    private String sign;
    
    public BaseJsfRequest() {
    }
    
    public BaseJsfRequest(String appKey, String operator, String clientIp, Long timeStamp, String sign) {
        this.appKey = appKey;
        this.operator = operator;
        this.clientIp = clientIp;
        this.timeStamp = timeStamp;
        this.sign = sign;
    }
    
    /**
     * 生成签名
     * @param token 应用token
     * @return 签名字符串
     */
    public String sign(String token) {
        if (appKey == null || timeStamp == null || token == null) {
            throw new IllegalArgumentException("appKey, timeStamp and token cannot be null");
        }
        
        try {
            String content = appKey + timeStamp + token;
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(content.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate sign", e);
        }
    }
    
    // Getters and Setters
    public String getAppKey() {
        return appKey;
    }
    
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }
    
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
    
    public String getClientIp() {
        return clientIp;
    }
    
    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
    
    public Long getTimeStamp() {
        return timeStamp;
    }
    
    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }
    
    public String getSign() {
        return sign;
    }
    
    public void setSign(String sign) {
        this.sign = sign;
    }
    
    @Override
    public String toString() {
        return "BaseJsfRequest{" +
                "appKey='" + appKey + '\'' +
                ", operator='" + operator + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", timeStamp=" + timeStamp +
                ", sign='" + sign + '\'' +
                '}';
    }
}
