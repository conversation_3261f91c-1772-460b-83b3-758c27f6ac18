package com.jdl.mcp.server.servers.timline;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jdl.mcp.core.service.annotation.McpEndpoint;
import com.jdl.mcp.core.service.annotation.Tool;
import com.jdl.mcp.core.service.annotation.ToolParam;
import com.jdl.mcp.core.service.annotation.ToolParamBean;
import com.jdl.mcp.server.openme.timline.TimlineService;
import com.jdl.mcp.server.openme.timline.param.SendJUEMsgParam;
import com.jdl.mcp.server.openme.timline.param.SendRobotMsgParam;
import com.jdl.mcp.server.openme.timline.param.SendRobotMsgParam.RobotMsgParams;

import lombok.Data;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;

/**
 * 机器人发消息服务（timline）提供了通过机器人向用户或群组发送消息的能力。服务支持发送普通文本消息和交互式卡片消息，可用于系统通知、业务提醒、信息推送等场景。
 * <p>
 */
@Component
@McpEndpoint(name = "timline", description = "机器人发消息服务（timline）提供了通过机器人向用户或群组发送消息的能力。服务支持发送普通文本消息和交互式卡片消息，可用于系统通知、业务提醒、信息推送等场景。")
public class TimlineMcpTool {
    private static final Logger logger = LoggerFactory.getLogger(TimlineMcpTool.class);
    private static final String CONFIG_PATH = "servers/timline.properties";

    private static final String DEFAULT_RECEIVER_TYPE = "1";
    private static final String DEFAULT_MESSAGE_TYPE = "1";
    private static final String DEFAULT_ROBOT_ID = "00_55e779bf635b4f14";

    private String timline_default_message_robot;
    private String timline_default_message_type;
    private String timline_default_receiver_type;

    @Autowired
    private TimlineService timlineService;


    public TimlineMcpTool() {
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        Properties props = new Properties();
        try (InputStream inputStream = new ClassPathResource(CONFIG_PATH).getInputStream()) {
            props.load(inputStream);
            timline_default_message_robot = props.getProperty("timline_default_message_robot", DEFAULT_ROBOT_ID);
            timline_default_message_type = props.getProperty("timline_default_message_type", DEFAULT_MESSAGE_TYPE);
            timline_default_receiver_type = props.getProperty("timline_default_receiver_type", DEFAULT_RECEIVER_TYPE);
        } catch (IOException e) {
            logger.error("无法加载配置文件: {}, 使用默认配置", CONFIG_PATH, e);
            timline_default_message_robot = DEFAULT_ROBOT_ID;
            timline_default_message_type = DEFAULT_MESSAGE_TYPE;
            timline_default_receiver_type = DEFAULT_RECEIVER_TYPE;
        }
    }

    @Tool(name = "sendMessage", description = "发送机器人消息，支持普通消息和卡片消息")
    public Map<String, Object> sendMessage(@ToolParamBean SendMessageParams params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();
            // 填充默认参数
            fillSendMessageParams(params);
            // 根据消息类型选择不同的发送方式
            if(DEFAULT_MESSAGE_TYPE.equals(params.getMessageType())) {
                return sendRobotMsg(params);
            } else {
                return sendJUEMsg(params);
            }
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return error;
        }
    }

    private void fillSendMessageParams(SendMessageParams params) {
        if (params.getReceiverType() == null || params.getReceiverType().trim().isEmpty()) {
            params.setReceiverType(timline_default_receiver_type);
        }
        if (params.getMessageType() == null || params.getMessageType().trim().isEmpty()) {
            params.setMessageType(timline_default_message_type);
        }
        if (params.getRobotId() == null || params.getRobotId().trim().isEmpty()) {
            params.setRobotId(timline_default_message_robot);
        }
    }

    private Map<String, Object> sendRobotMsg(SendMessageParams params) {
        Map<String, Object> result = new HashMap<>();
        SendRobotMsgParam sendRobotMsgParam = new SendRobotMsgParam();
        if(DEFAULT_RECEIVER_TYPE.equals(params.getReceiverType())){
            sendRobotMsgParam.setErp(params.getReceiver());
        }else{
            sendRobotMsgParam.setGroupId(params.getReceiver());
        }
        
        sendRobotMsgParam.setRequestId(UUID.randomUUID().toString());
        sendRobotMsgParam.setDateTime(System.currentTimeMillis());


        RobotMsgParams robotMsgParams = null;
        ObjectMapper mapper = new ObjectMapper();
        // 配置Jackson处理多态
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            robotMsgParams = mapper.readValue(params.getMessageContent(), RobotMsgParams.class);
        } catch (Exception e) {
            logger.error("解析消息内容失败", e);
            result.put("success", false);
            result.put("error", "解析消息内容[RobotMsgParams]失败: " + e.getMessage());
            return result;
        }
        sendRobotMsgParam.setParams(robotMsgParams);
        try {
            logger.info("发送消息参数: {}", params.toString());
            JSONObject jsonObject = timlineService.sendRobotMsg(sendRobotMsgParam);
            if(jsonObject != null){
                result.put("success", true);
                result.put("jsonData", jsonObject.toString());
            }else{
                result.put("success", false);
                result.put("error", "调用接口正常，返回结果为空对象");
            }
            logger.info("发送消息结果: {}", jsonObject.toString());
        }catch (Exception e) {
            logger.error("发送消息失败", e);
            result.put("success", false);
            result.put("error", "发送消息失败");
            return result;
        }
        return result;
    }

    private Map<String, Object> sendJUEMsg(SendMessageParams params) {
        Map<String, Object> result = new HashMap<>();
        SendJUEMsgParam sendJUEMsgParam = new SendJUEMsgParam();
        if(DEFAULT_RECEIVER_TYPE.equals(params.getReceiverType())){
            sendJUEMsgParam.setErp(params.getReceiver());
        }else{
            sendJUEMsgParam.setGroupId(params.getReceiver());
        }
        sendJUEMsgParam.setRequestId(UUID.randomUUID().toString());
        sendJUEMsgParam.setDateTime(System.currentTimeMillis());

        SendJUEMsgParam.JUEMsgParams jueMsgParams = null;
        ObjectMapper mapper = new ObjectMapper();
        // 配置Jackson处理多态
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            jueMsgParams = mapper.readValue(params.getMessageContent(), SendJUEMsgParam.JUEMsgParams.class);
        } catch (Exception e) {
            logger.error("解析消息内容失败", e);
            result.put("success", false);
            result.put("error", "解析消息内容[JUEMsgParams]失败: " + e.getMessage());
            return result;
        }
        sendJUEMsgParam.setParams(jueMsgParams);
        try {
            logger.info("发送消息参数: {}", params.toString());
            JSONObject jsonObject = timlineService.sendJUEMsg(sendJUEMsgParam);
            if(jsonObject != null){
                result.put("success", true);
                result.put("jsonData", jsonObject.toString());
            }else{
                result.put("success", false);
                result.put("error", "调用接口正常，返回结果为空对象");
            }
            logger.info("发送消息结果: {}", jsonObject.toString());
        }catch (Exception e) {
            logger.error("发送消息失败", e);
            result.put("success", false);
            result.put("error", "发送消息失败");
            return result;
        }
        return result;
    }

    @Data
    public static class SendMessageParams {
        @ToolParam(name = "receiver", description = "接收人，可以是用户erp，也可以是群组id", required = true)
        private String receiver;
        @ToolParam(name = "receiverType", description = "接收人类型，1-用户 2-群组", required = false)
        private String receiverType;
        @ToolParam(name = "messageType", description = "消息类型，普通消息类型为1，卡片消息类型为2", required = false)
        private String messageType;
        @ToolParam(name = "messageContent", description = "消息内容，JSON格式", required = true)
        private String messageContent;
        @ToolParam(name = "robotId", description = "机器人id，默认为物流交易小蜜", required = false)
        private String robotId;


        public void validateSelf() {
            if (receiver == null || receiver.trim().isEmpty()) {
                throw new IllegalArgumentException("receiver，接收人不能为空");
            }
            if (messageContent == null || messageContent.trim().isEmpty()) {
                throw new IllegalArgumentException("messageContent 消息内容不能为空");
            }
        }
    }

}