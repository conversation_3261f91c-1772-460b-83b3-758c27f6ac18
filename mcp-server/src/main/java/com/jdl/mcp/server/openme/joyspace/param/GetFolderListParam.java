package com.jdl.mcp.server.openme.joyspace.param;

import java.util.List;

public class GetFolderListParam {
    private String folderId;
    private List<Creator> creators;
    private String sort;

    public String getFolderId() {
        return folderId;
    }

    public void setFolderId(String folderId) {
        this.folderId = folderId;
    }

    public List<Creator> getCreators() {
        return creators;
    }

    public void setCreators(List<Creator> creators) {
        this.creators = creators;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }


    public static class Creator {
        private String erp;
        private String tenantCode;
        private String orgCode;
        private int userType;

        // Getters and setters

        public String getErp() {
            return erp;
        }

        public void setErp(String erp) {
            this.erp = erp;
        }

        public String getTenantCode() {
            return tenantCode;
        }

        public void setTenantCode(String tenantCode) {
            this.tenantCode = tenantCode;
        }

        public String getOrgCode() {
            return orgCode;
        }

        public void setOrgCode(String orgCode) {
            this.orgCode = orgCode;
        }

        public int getUserType() {
            return userType;
        }

        public void setUserType(int userType) {
            this.userType = userType;
        }
    }
}

