package com.jdl.mcp.server.openme.timline.param;

import java.util.List;
import java.util.UUID;

/**
 * 发送互动卡片消息的参数
 */
public class SendJUEMsgParam {

    private String erp;
    private String groupId;
    private JUEMsgParams params;
    private String requestId;
    private Long dateTime;

    public SendJUEMsgParam() {
        this.requestId = UUID.randomUUID().toString();
        this.dateTime = System.currentTimeMillis();
    }

    public String getErp() {
        return erp;
    }

    public void setErp(String erp) {
        this.erp = erp;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public JUEMsgParams getParams() {
        return params;
    }

    public void setParams(JUEMsgParams params) {
        this.params = params;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Long getDateTime() {
        return dateTime;
    }

    public void setDateTime(Long dateTime) {
        this.dateTime = dateTime;
    }

    /**
     * 互动卡片消息参数
     */
    public static class JUEMsgParams {
        private JUEMsgData data;

        public JUEMsgData getData() {
            return data;
        }

        public void setData(JUEMsgData data) {
            this.data = data;
        }
    }

    /**
     * 互动卡片消息数据
     */
    public static class JUEMsgData {
        private String templateId;
        private Integer sessionType;
        private Integer templateType;
        private String width_mode;
        private Boolean reload;
        private String summary;
        private Object cardData;
        private Forward forward;
        private Object callbackData;
        private At at;

        public JUEMsgData() {
            this.sessionType = 1; // 默认为1
            this.templateType = 1; // 默认为普通类型
            this.width_mode = "wide"; // 默认为宽模式
            this.reload = false; // 默认不重新加载
        }

        public String getTemplateId() {
            return templateId;
        }

        public void setTemplateId(String templateId) {
            this.templateId = templateId;
        }

        public Integer getSessionType() {
            return sessionType;
        }

        public void setSessionType(Integer sessionType) {
            this.sessionType = sessionType;
        }

        public Integer getTemplateType() {
            return templateType;
        }

        public void setTemplateType(Integer templateType) {
            this.templateType = templateType;
        }

        public String getWidth_mode() {
            return width_mode;
        }

        public void setWidth_mode(String width_mode) {
            this.width_mode = width_mode;
        }

        public Boolean getReload() {
            return reload;
        }

        public void setReload(Boolean reload) {
            this.reload = reload;
        }

        public String getSummary() {
            return summary;
        }

        public void setSummary(String summary) {
            this.summary = summary;
        }

        public Object getCardData() {
            return cardData;
        }

        public void setCardData(Object cardData) {
            this.cardData = cardData;
        }

        public Forward getForward() {
            return forward;
        }

        public void setForward(Forward forward) {
            this.forward = forward;
        }

        public Object getCallbackData() {
            return callbackData;
        }

        public void setCallbackData(Object callbackData) {
            this.callbackData = callbackData;
        }

        public At getAt() {
            return at;
        }

        public void setAt(At at) {
            this.at = at;
        }
    }

    /**
     * 转发配置
     */
    public static class Forward {
        private Boolean reload;
        private Object cardData;

        public Forward() {
            this.reload = false; // 默认不重新加载
        }

        public Boolean getReload() {
            return reload;
        }

        public void setReload(Boolean reload) {
            this.reload = reload;
        }

        public Object getCardData() {
            return cardData;
        }

        public void setCardData(Object cardData) {
            this.cardData = cardData;
        }
    }

    /**
     * @用户配置
     */
    public static class At {
        private Boolean atAll;
        private List<AtUser> users;

        public Boolean getAtAll() {
            return atAll;
        }

        public void setAtAll(Boolean atAll) {
            this.atAll = atAll;
        }

        public List<AtUser> getUsers() {
            return users;
        }

        public void setUsers(List<AtUser> users) {
            this.users = users;
        }
    }

    /**
     * @用户信息
     */
    public static class AtUser {
        private String app;
        private String pin;

        public String getApp() {
            return app;
        }

        public void setApp(String app) {
            this.app = app;
        }

        public String getPin() {
            return pin;
        }

        public void setPin(String pin) {
            this.pin = pin;
        }
    }
}
