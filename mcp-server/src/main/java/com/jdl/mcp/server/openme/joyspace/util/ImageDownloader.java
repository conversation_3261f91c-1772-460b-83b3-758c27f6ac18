package com.jdl.mcp.server.openme.joyspace.util;

import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;

/**
 * 图片下载工具类
 * 用于下载图片并转换为BufferedImage或保存为临时文件
 */
public class ImageDownloader {
    private static final Logger logger = LoggerFactory.getLogger(ImageDownloader.class);
    private static final int DEFAULT_TIMEOUT = 10000; // 10 seconds
    
    private final RestTemplate restTemplate;
    private final int timeout;
    
    /**
     * 默认构造函数，使用默认超时时间
     */
    public ImageDownloader() {
        this(DEFAULT_TIMEOUT);
    }
    
    /**
     * 带超时时间的构造函数
     * @param timeout 超时时间（毫秒）
     */
    public ImageDownloader(int timeout) {
        this.timeout = timeout;
        this.restTemplate = new RestTemplate();
    }
    
    /**
     * 下载图片并返回临时文件
     * @param imageUrl 图片URL
     * @return 下载的图片文件，如果下载失败则返回null
     */
    public File downloadImage(String imageUrl) {
        try {
            // 创建临时文件
            String fileExtension = getFileExtension(imageUrl);
            File tempFile = File.createTempFile("image_", "." + fileExtension);
            tempFile.deleteOnExit();
            
            // 下载图片
            ResponseEntity<byte[]> response = restTemplate.exchange(
                imageUrl,
                HttpMethod.GET,
                null,
                byte[].class
            );
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                FileUtils.writeByteArrayToFile(tempFile, response.getBody());
                return tempFile;
            }
            
            return null;
        } catch (Exception e) {
            logger.error("Failed to download image: " + imageUrl, e);
            return null;
        }
    }
    
    /**
     * 下载图片并返回BufferedImage对象
     * @param imageUrl 图片URL
     * @return 下载的图片对象，如果下载失败则返回null
     */
    public BufferedImage downloadImageAsBufferedImage(String imageUrl) {
        try {
            // 下载图片
            ResponseEntity<byte[]> response = restTemplate.exchange(
                imageUrl,
                HttpMethod.GET,
                null,
                byte[].class
            );
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                ByteArrayInputStream bis = new ByteArrayInputStream(response.getBody());
                return ImageIO.read(bis);
            }
            
            return null;
        } catch (Exception e) {
            logger.error("Failed to download image as BufferedImage: " + imageUrl, e);
            return null;
        }
    }
    
    /**
     * 获取图片文件扩展名
     * @param imageUrl 图片URL
     * @return 文件扩展名，默认为jpg
     */
    private String getFileExtension(String imageUrl) {
        try {
            String path = new URL(imageUrl).getPath();
            int lastDotIndex = path.lastIndexOf('.');
            if (lastDotIndex > 0) {
                return path.substring(lastDotIndex + 1).toLowerCase();
            }
        } catch (Exception e) {
            logger.warn("Failed to get file extension from URL: " + imageUrl, e);
        }
        return "jpg"; // 默认扩展名
    }
}
