package com.jdl.mcp.server.openme.joywork.param;

import java.util.List;

public class JoyworkCreateTaskParam {
    private String app;
    private String bizCode;
    private List<Extend> extend;
    private String content;
    private String mobileContent;
    private String openUserId;
    private List<Owner> ownerList;
    private String sourceId;
    private String title;
    private String remark;
    private Long startTime;
    private Long endTime;
    private String sysProjectId;
    private String sysGroupId;
    private String sourceDescZh;
    private String sourceDescEn;
    private Integer sourceTrusteeship;

    public String getApp() { return app; }
    public void setApp(String app) { this.app = app; }
    public String getBizCode() { return bizCode; }
    public void setBizCode(String bizCode) { this.bizCode = bizCode; }
    public List<Extend> getExtend() { return extend; }
    public void setExtend(List<Extend> extend) { this.extend = extend; }
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    public String getMobileContent() { return mobileContent; }
    public void setMobileContent(String mobileContent) { this.mobileContent = mobileContent; }
    public String getOpenUserId() { return openUserId; }
    public void setOpenUserId(String openUserId) { this.openUserId = openUserId; }
    public List<Owner> getOwnerList() { return ownerList; }
    public void setOwnerList(List<Owner> ownerList) { this.ownerList = ownerList; }
    public String getSourceId() { return sourceId; }
    public void setSourceId(String sourceId) { this.sourceId = sourceId; }
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }
    public Long getStartTime() { return startTime; }
    public void setStartTime(Long startTime) { this.startTime = startTime; }
    public Long getEndTime() { return endTime; }
    public void setEndTime(Long endTime) { this.endTime = endTime; }
    public String getSysProjectId() { return sysProjectId; }
    public void setSysProjectId(String sysProjectId) { this.sysProjectId = sysProjectId; }
    public String getSysGroupId() { return sysGroupId; }
    public void setSysGroupId(String sysGroupId) { this.sysGroupId = sysGroupId; }
    public String getSourceDescZh() { return sourceDescZh; }
    public void setSourceDescZh(String sourceDescZh) { this.sourceDescZh = sourceDescZh; }
    public String getSourceDescEn() { return sourceDescEn; }
    public void setSourceDescEn(String sourceDescEn) { this.sourceDescEn = sourceDescEn; }
    public Integer getSourceTrusteeship() { return sourceTrusteeship; }
    public void setSourceTrusteeship(Integer sourceTrusteeship) { this.sourceTrusteeship = sourceTrusteeship; }

    public static class Extend {
        private String content;
        private String tips;
        private String type;
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public String getTips() { return tips; }
        public void setTips(String tips) { this.tips = tips; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }
    public static class Owner {
        private String openUserId;
        private String taskUserRole;
        public String getOpenUserId() { return openUserId; }
        public void setOpenUserId(String openUserId) { this.openUserId = openUserId; }
        public String getTaskUserRole() { return taskUserRole; }
        public void setTaskUserRole(String taskUserRole) { this.taskUserRole = taskUserRole; }
        public enum TaskUserRole {
            CREATOR(0,"CREATOR" , "创建人"),
            OWNER(1, "OWNER" , "负责人"),
            EXECUTOR(2, "EXECUTOR" , "执行人"),
            LOOK_UP(3, "LOOK_UP" , "关注人"),
            JOIN_ROLE(4, "JOIN_ROLE" , "参与人");
            private final int code;
            private final String name;
            private final String desc;
            TaskUserRole(int code, String name , String desc) {
                this.code = code;
                this.name = name;
                this.desc = desc;
            }
            public int getCode() { return code; }
            public String getDesc() { return desc; }
            public String getName() { return name; }
        }
    }
} 