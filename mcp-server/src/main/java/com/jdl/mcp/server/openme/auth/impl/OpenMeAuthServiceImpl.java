package com.jdl.mcp.server.openme.auth.impl;

import com.jdl.mcp.server.openme.auth.OpenMeAuthService;
import com.jdl.mcp.server.openme.auth.OpenUserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@SuppressWarnings("unchecked")
public class OpenMeAuthServiceImpl implements OpenMeAuthService {
    
    private static final Logger log = LoggerFactory.getLogger(OpenMeAuthServiceImpl.class);
    
    // 缓存结构，用于存储token和过期时间
    private static class TokenCache {
        private final String token;
        private final long expireTime; // 过期时间的时间戳（毫秒）
        
        public TokenCache(String token, long expireInSeconds) {
            this.token = token;
            this.expireTime = System.currentTimeMillis() + (expireInSeconds * 1000);
        }
        
        public String getToken() {
            return token;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() >= expireTime;
        }
    }
    
    // 应用token缓存
    private TokenCache appTokenCache;
    
    // 团队token缓存（key为appAccessToken）
    private final Map<String, TokenCache> teamTokenCache = new ConcurrentHashMap<>();
    
    @Value("${openme.app.key}")
    private String appKey;
    
    @Value("${openme.app.secret}")
    private String appSecret;
    
    @Value("${openme.app.openTeamId}")
    private String openTeamId;

    @Value("${openme.app.teamId}")
    private String teamId;
    
    @Value("${openme.app.host}")
    private String host;
    
    private final RestTemplate restTemplate;

    
    public OpenMeAuthServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }
    
    @Override
    public String getAppAccessToken() {
        // 检查缓存是否有效
        if (appTokenCache != null && !appTokenCache.isExpired()) {
            log.debug("Using cached appAccessToken");
            return appTokenCache.getToken();
        }
        
        try {
            String url = host + "/open-api/auth/v1/app_access_token";
            
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("appKey", appKey);
            requestBody.put("appSecret", appSecret);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);
            
            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.info("getAppAccessToken response: {}", response);
            
            if (response != null && (Integer)response.get("code") == 0) {
                Map<String, Object> data = (Map<String, Object>) response.get("data");
                String appAccessToken = (String) data.get("appAccessToken");
                
                // 获取过期时间（秒）
                Integer expireIn = (Integer) data.get("expireIn");
                if (expireIn == null) {
                    expireIn = 7200; // 默认2小时
                }
                
                // 更新缓存
                appTokenCache = new TokenCache(appAccessToken, expireIn);
                
                return appAccessToken;
            }
            
            return null;
        } catch (Exception e) {
            log.error("getAppAccessToken error", e);
            return null;
        }
    }

    @Override
    public String getTeamAccessToken(String appAccessToken) {
        // 检查缓存是否有效
        TokenCache cache = teamTokenCache.get(appAccessToken);
        if (cache != null && !cache.isExpired()) {
            log.debug("Using cached teamAccessToken for appAccessToken: {}", appAccessToken);
            return cache.getToken();
        }
        
        try {
            String url = host + "/open-api/auth/v1/team_access_token";
            
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("appAccessToken", appAccessToken);
            requestBody.put("openTeamId", openTeamId);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);
            
            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.info("getTeamAccessToken response: {}", response);
            
            if (response != null && (Integer)response.get("code") == 0) {
                Map<String, Object> data = (Map<String, Object>) response.get("data");
                String teamAccessToken = (String) data.get("teamAccessToken");
                
                // 获取过期时间（秒）
                Integer expireIn = (Integer) data.get("expireIn");
                if (expireIn == null) {
                    expireIn = 7200; // 默认2小时
                }
                
                // 更新缓存
                teamTokenCache.put(appAccessToken, new TokenCache(teamAccessToken, expireIn));
                
                return teamAccessToken;
            }
            
            return null;
        } catch (Exception e) {
            log.error("getTeamAccessToken error", e);
            return null;
        }
    }
    
    
    @Override
    public OpenUserInfo getOpenUserInfo(String teamAccessToken, String userId) {
        try {
            String url = host + "/open-api/custom/v1/getOpenUserInfoInner";
            
            Map<String, Object> getOpenUserInfo = new HashMap<>();
            getOpenUserInfo.put("appKey", appKey);
            getOpenUserInfo.put("userId", userId);
            getOpenUserInfo.put("teamId", teamId);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("getOpenUserInfo", getOpenUserInfo);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("authorization", "Bearer " + teamAccessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.info("getOpenUserInfo response: {}", response);
            
            if (response != null && (Integer)response.get("code") == 0) {
                Map<String, Object> data = (Map<String, Object>) response.get("data");
                Map<String, Object> userInfo = (Map<String, Object>) data.get("getOpenUserInfoRespDto");
                
                OpenUserInfo openUserInfo = new OpenUserInfo();
                openUserInfo.setOpenUserId((String) userInfo.get("openUserId"));
                openUserInfo.setOpenTeamId((String) userInfo.get("openTeamId"));
                openUserInfo.setName((String) userInfo.get("name"));
                openUserInfo.setUnionUserId((String) userInfo.get("unionUserId"));
                
                return openUserInfo;
            }
            
            return null;
        } catch (Exception e) {
            log.error("getOpenUserInfo error", e);
            return null;
        }
    }
}
