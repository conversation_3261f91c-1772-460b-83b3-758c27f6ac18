package com.jdl.mcp.server.openme.joywork;

import com.jdl.mcp.server.openme.joywork.param.JoyworkCreateTaskParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskStatusParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkUrgeTaskParam;
import org.json.JSONObject;

/**
 * Joywork服务接口，封装任务创建、更新、状态变更、催办等能力
 */
public interface JoyworkService {
    /**
     * 创建待办任务
     * @param param 创建参数
     * @return 响应结果
     */
    JSONObject createTask(JoyworkCreateTaskParam param);

    /**
     * 更新待办任务
     * @param param 更新参数
     * @return 响应结果
     */
    JSONObject updateTask(JoyworkUpdateTaskParam param);

    /**
     * 更新待办任务状态
     * @param param 状态参数
     * @return 响应结果
     */
    JSONObject updateTaskStatus(JoyworkUpdateTaskStatusParam param);

    /**
     * 催办待办任务
     * @param param 催办参数
     * @return 响应结果
     */
    JSONObject urgeTask(JoyworkUrgeTaskParam param);
} 