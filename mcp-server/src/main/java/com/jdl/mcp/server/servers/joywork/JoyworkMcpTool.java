package com.jdl.mcp.server.servers.joywork;

import com.jdl.mcp.core.service.annotation.McpEndpoint;
import com.jdl.mcp.core.service.annotation.Tool;
import com.jdl.mcp.core.service.annotation.ToolParam;
import com.jdl.mcp.core.service.annotation.ToolParamBean;
import com.jdl.mcp.core.utils.NumberUtils;
import com.jdl.mcp.core.utils.Utils;
import com.jdl.mcp.server.openme.joywork.JoyworkService;
import com.jdl.mcp.server.openme.joywork.param.JoyworkCreateTaskParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskStatusParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkUrgeTaskParam;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;

/**
 * 通过待办服务，管理待办任务
 * <p>
 * 通过待办服务，创建待办任务，更新待办任务，更新待办任务状态，催办待办任务
 * </p>
 */
@Component
@McpEndpoint(name = "joywork", description = "通过待办服务，管理待办任务")
public class JoyworkMcpTool {
    private static final Logger logger = LoggerFactory.getLogger(JoyworkMcpTool.class);
    private static final String CONFIG_PATH = "servers/joywork.properties";

    private String joyworkContent;
    private String joyworkMobileContent;
    private String joyworkSourceDescZh;
    private String joyworkSourceDescEn;
    private String joyworkSourceTrusteeship;

    @Autowired
    private JoyworkService joyworkService;

    public JoyworkMcpTool() {
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        Properties props = new Properties();
        try (InputStream inputStream = new ClassPathResource(CONFIG_PATH).getInputStream()) {
            InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            props.load(reader);
            joyworkContent = props.getProperty("joywork_content", "http://mcp.jdl.com");
            joyworkMobileContent = props.getProperty("joywork_mobile_content", "https://mcp.jdl.com");
            joyworkSourceDescZh = props.getProperty("joywork_sourceDescZh", "请前往MCP平台处理");
            joyworkSourceDescEn = props.getProperty("joywork_sourceDescEn", "Please go to MCP platform to handle");
            joyworkSourceTrusteeship = props.getProperty("joywork_sourceTrusteeship", "1");
        } catch (IOException e) {
            logger.error("无法加载配置文件: {}, 使用默认配置", CONFIG_PATH, e);
            joyworkContent = "http://mcp.jdl.com";
            joyworkMobileContent = "https://mcp.jdl.com";
            joyworkSourceDescZh = "请前往MCP平台处理";
            joyworkSourceDescEn = "Please go to MCP platform to handle";
            joyworkSourceTrusteeship = "1";
        }
    }

    @SuppressWarnings("null")
    @Tool(name = "createTask", description = "创建待办任务")
    public Map<String, Object> createTask(@ToolParamBean CreateTaskParams params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);

            JoyworkCreateTaskParam createTaskParam = new JoyworkCreateTaskParam();
            createTaskParam.setContent(joyworkContent);
            createTaskParam.setMobileContent(joyworkMobileContent);
            createTaskParam.setSourceDescZh(joyworkSourceDescZh);
            createTaskParam.setSourceDescEn(joyworkSourceDescEn);
            createTaskParam.setSourceTrusteeship(NumberUtils.toInteger(joyworkSourceTrusteeship, 1));
            createTaskParam.setSourceId(UUID.randomUUID().toString());
            createTaskParam.setTitle(params.getTitle());
            createTaskParam.setRemark(params.getRemark());

            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                createTaskParam.setStartTime(sdf.parse(params.getStarttime()).getTime());
                createTaskParam.setEndTime(sdf.parse(params.getEndtime()).getTime());
            } catch (Exception e) {
                logger.error("开始结束时间转换失败，默认使用当前时间，结束时间+24小时: " + e.getMessage());
                createTaskParam.setStartTime(System.currentTimeMillis());
                createTaskParam.setEndTime(System.currentTimeMillis() + 24 * 60 * 60 * 1000);
            }
            if (!Utils.isEmpty(params.getExecutor())) {
                List<JoyworkCreateTaskParam.Owner> ownerList = new ArrayList<>();
                if (params.getExecutor().contains(",")) {
                    String[] executors = params.getExecutor().split(",");
                    for (String executor : executors) {
                        JoyworkCreateTaskParam.Owner owner = new JoyworkCreateTaskParam.Owner();
                        owner.setOpenUserId(executor);
                        owner.setTaskUserRole(JoyworkCreateTaskParam.Owner.TaskUserRole.EXECUTOR.getName());
                        ownerList.add(owner);
                    }
                } else {
                    JoyworkCreateTaskParam.Owner owner = new JoyworkCreateTaskParam.Owner();
                    owner.setOpenUserId(params.getExecutor());
                    owner.setTaskUserRole(JoyworkCreateTaskParam.Owner.TaskUserRole.EXECUTOR.getName());
                    ownerList.add(owner);
                }
                createTaskParam.setOwnerList(ownerList);
            }
            logger.info("创建待办任务参数: {}", createTaskParam.toString());
            JSONObject jsonObject = joyworkService.createTask(createTaskParam);
            if (jsonObject != null) {
                result.put("success", true);
                result.put("data", jsonObject.toString());
            } else {
                result.put("success", false);
                result.put("error", "调用接口正常，返回结果为空对象");
            }
            logger.info("创建待办任务结果: {}", jsonObject.toString());

            return result;
        } catch (Exception e) {
            return error("createTask 执行失败: " + e.getMessage());
        }
    }

    public static class CreateTaskParams {
        @ToolParam(name = "title", description = "任务标题", required = true)
        private String title;
        @ToolParam(name = "remark", description = "任务说明", required = true)
        private String remark;
        @ToolParam(name = "starttime", description = "任务开始时间", required = true)
        private String starttime;
        @ToolParam(name = "endtime", description = "任务结束时间", required = true)
        private String endtime;
        @ToolParam(name = "executor", description = "任务执行人ERP，多个用逗号分隔", required = true)
        private String executor;

        public void validateSelf() {
            if (title == null || title.trim().isEmpty()) {
                throw new IllegalArgumentException("title 不能为空");
            }
            if (remark == null || remark.trim().isEmpty()) {
                throw new IllegalArgumentException("remark 不能为空");
            }
            if (starttime == null || starttime.trim().isEmpty()) {
                throw new IllegalArgumentException("starttime 不能为空");
            }
            if (endtime == null || endtime.trim().isEmpty()) {
                throw new IllegalArgumentException("endtime 不能为空");
            }
            if (executor == null || executor.trim().isEmpty()) {
                throw new IllegalArgumentException("executor 不能为空");
            }
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getStarttime() {
            return starttime;
        }

        public void setStarttime(String starttime) {
            this.starttime = starttime;
        }

        public String getEndtime() {
            return endtime;
        }

        public void setEndtime(String endtime) {
            this.endtime = endtime;
        }

        public String getExecutor() {
            return executor;
        }

        public void setExecutor(String executor) {
            this.executor = executor;
        }

    }

    @Tool(name = "updateTask", description = "更新待办任务")
    public Map<String, Object> updateTask(@ToolParamBean UpdateTaskParams params) {
        try {
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            JoyworkUpdateTaskParam updateTaskParam = new JoyworkUpdateTaskParam();
            updateTaskParam.setTaskId(params.getTaskid());
            updateTaskParam.setTitle(params.getTitle());
            updateTaskParam.setRemark(params.getRemark());
            updateTaskParam.setContent(joyworkContent);
            updateTaskParam.setMobileContent(joyworkMobileContent);
            updateTaskParam.setSourceDescZh(joyworkSourceDescZh);
            updateTaskParam.setSourceDescEn(joyworkSourceDescEn);
            updateTaskParam.setSourceTrusteeship(NumberUtils.toInteger(joyworkSourceTrusteeship, 1));

            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                updateTaskParam.setStartTime(sdf.parse(params.getStarttime()).getTime());
                updateTaskParam.setEndTime(sdf.parse(params.getEndtime()).getTime());
            } catch (Exception e) {
                logger.error("开始结束时间转换失败，默认使用当前时间，结束时间+24小时: " + e.getMessage());
                updateTaskParam.setStartTime(System.currentTimeMillis());
                updateTaskParam.setEndTime(System.currentTimeMillis() + 24 * 60 * 60 * 1000);
            }

            // 其他字段如priorityType、extend、sysProjectId、sysGroupId可根据需要赋值

            logger.info("更新待办任务参数: {}", updateTaskParam);
            JSONObject jsonObject = joyworkService.updateTask(updateTaskParam);
            if (jsonObject != null) {
                result.put("success", true);
                result.put("data", jsonObject.toString());
            } else {
                result.put("success", false);
                result.put("error", "调用接口正常，返回结果为空对象");
            }
            logger.info("更新待办任务结果: {}", jsonObject);
            return result;
        } catch (Exception e) {
            return error("updateTask 执行失败: " + e.getMessage());
        }
    }

    public static class UpdateTaskParams {
        @ToolParam(name = "taskid", description = "任务id", required = true)
        private String taskid;
        @ToolParam(name = "title", description = "任务标题", required = true)
        private String title;
        @ToolParam(name = "remark", description = "任务说明", required = true)
        private String remark;
        @ToolParam(name = "starttime", description = "任务开始时间", required = true)
        private String starttime;
        @ToolParam(name = "endtime", description = "任务结束时间", required = true)
        private String endtime;

        public void validateSelf() {
            if (taskid == null || taskid.trim().isEmpty()) {
                throw new IllegalArgumentException("taskid 不能为空");
            }
            if (title == null || title.trim().isEmpty()) {
                throw new IllegalArgumentException("title 不能为空");
            }
            if (remark == null || remark.trim().isEmpty()) {
                throw new IllegalArgumentException("remark 不能为空");
            }
            if (starttime == null || starttime.trim().isEmpty()) {
                throw new IllegalArgumentException("starttime 不能为空");
            }
            if (endtime == null || endtime.trim().isEmpty()) {
                throw new IllegalArgumentException("endtime 不能为空");
            }
        }

        public String getTaskid() {
            return taskid;
        }

        public void setTaskid(String taskid) {
            this.taskid = taskid;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getStarttime() {
            return starttime;
        }

        public void setStarttime(String starttime) {
            this.starttime = starttime;
        }

        public String getEndtime() {
            return endtime;
        }

        public void setEndtime(String endtime) {
            this.endtime = endtime;
        }

    }

    @Tool(name = "updateTaskStatus", description = "更新待办任务状态")
    public Map<String, Object> updateTaskStatus(@ToolParamBean UpdateTaskStatusParams params) {
        try {
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            JoyworkUpdateTaskStatusParam updateTaskStatusParam = new JoyworkUpdateTaskStatusParam();
            updateTaskStatusParam.setTaskId(params.getTaskid());
            updateTaskStatusParam.setTaskStatus(Integer.valueOf(params.getTaskstatus()));

            logger.info("更新待办任务状态参数: {}", updateTaskStatusParam);
            JSONObject jsonObject = joyworkService.updateTaskStatus(updateTaskStatusParam);
            if (jsonObject != null) {
                result.put("success", true);
                result.put("data", jsonObject.toString());
            } else {
                result.put("success", false);
                result.put("error", "调用接口正常，返回结果为空对象");
            }
            logger.info("更新待办任务状态结果: {}", jsonObject);
            return result;
        } catch (Exception e) {
            return error("updateTaskStatus 执行失败: " + e.getMessage());
        }
    }

    public static class UpdateTaskStatusParams {
        @ToolParam(name = "taskid", description = "任务id", required = true)
        private String taskid;
        @ToolParam(name = "taskstatus", description = "任务状态，taskStatus = 1 未完成；taskStatus = 2 完成", required = true)
        private String taskstatus;

        public void validateSelf() {
            if (taskid == null || taskid.trim().isEmpty()) {
                throw new IllegalArgumentException("taskid 不能为空");
            }
            if (taskstatus == null || taskstatus.trim().isEmpty()) {
                throw new IllegalArgumentException("taskstatus 不能为空");
            }
        }

        public String getTaskid() {
            return taskid;
        }

        public void setTaskid(String taskid) {
            this.taskid = taskid;
        }

        public String getTaskstatus() {
            return taskstatus;
        }

        public void setTaskstatus(String taskstatus) {
            this.taskstatus = taskstatus;
        }

    }

    @Tool(name = "urgeTask", description = "催办待办任务")
    public Map<String, Object> urgeTask(@ToolParamBean UrgeTaskParams params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            JoyworkUrgeTaskParam urgeTaskParam = new JoyworkUrgeTaskParam();
            urgeTaskParam.setTaskId(params.getTaskid());
            urgeTaskParam.setUrgeContent(params.getUrgecontent());

            List<JoyworkUrgeTaskParam.TaskUser> userList = new ArrayList<>();
            if (!Utils.isEmpty(params.getTaskusers())) {
                String[] users = params.getTaskusers().split(",");
                if (users.length > 0) {
                    for (String user : users) {
                        JoyworkUrgeTaskParam.TaskUser taskUser = new JoyworkUrgeTaskParam.TaskUser();
                        taskUser.setErp(user.trim());
                        userList.add(taskUser);
                    }
                }
            }
            urgeTaskParam.setTaskUsers(userList);

            logger.info("催办待办任务参数: {}", urgeTaskParam);
            JSONObject jsonObject = joyworkService.urgeTask(urgeTaskParam);
            if (jsonObject != null) {
                result.put("success", true);
                result.put("data", jsonObject.toString());
            } else {
                result.put("success", false);
                result.put("error", "调用接口正常，返回结果为空对象");
            }
            logger.info("催办待办任务结果: {}", jsonObject);
            return result;
        } catch (Exception e) {
            return error("urgeTask 执行失败: " + e.getMessage());
        }
    }

    public static class UrgeTaskParams {
        @ToolParam(name = "taskid", description = "任务id", required = true)
        private String taskid;
        @ToolParam(name = "urgecontent", description = "催办内容", required = true)
        private String urgecontent;
        @ToolParam(name = "taskusers", description = "抄送人，多个用逗号分隔", required = true)
        private String taskusers;

        public void validateSelf() {
            if (taskid == null || taskid.trim().isEmpty()) {
                throw new IllegalArgumentException("taskid 不能为空");
            }
            if (urgecontent == null || urgecontent.trim().isEmpty()) {
                throw new IllegalArgumentException("urgecontent 不能为空");
            }
            if (taskusers == null || taskusers.trim().isEmpty()) {
                throw new IllegalArgumentException("taskusers 不能为空");
            }
        }

        public String getTaskid() {
            return taskid;
        }

        public void setTaskid(String taskid) {
            this.taskid = taskid;
        }

        public String getUrgecontent() {
            return urgecontent;
        }

        public void setUrgecontent(String urgecontent) {
            this.urgecontent = urgecontent;
        }

        public String getTaskusers() {
            return taskusers;
        }

        public void setTaskusers(String taskusers) {
            this.taskusers = taskusers;
        }

    }

    private Map<String, Object> error(String message) {
        logger.error(message);
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("error", message);
        return error;
    }
}
