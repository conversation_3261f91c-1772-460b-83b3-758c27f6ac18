package com.jdl.mcp.server.openme.timline.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jdl.mcp.server.openme.auth.OpenMeAuthService;
import com.jdl.mcp.server.openme.timline.TimlineService;
import com.jdl.mcp.server.openme.timline.param.SendJUEMsgParam;
import com.jdl.mcp.server.openme.timline.param.SendRobotMsgParam;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

@Service
public class TimlineServiceImpl implements TimlineService {

    private static final Logger log = LoggerFactory.getLogger(TimlineServiceImpl.class);

    @Value("${openme.app.host}")
    private String host;

    @Value("${openme.timline.appId}")
    private String appId;

    @Value("${openme.timline.tenantId}")
    private String tenantId;

    @Value("${openme.timline.robotId}")
    private String robotId;

    private final RestTemplate restTemplate;
    private final OpenMeAuthService openMeAuthService;
    private final ObjectMapper objectMapper;

    public TimlineServiceImpl(RestTemplate restTemplate, OpenMeAuthService openMeAuthService, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.openMeAuthService = openMeAuthService;
        this.objectMapper = objectMapper;
    }

    @Override
    public JSONObject sendRobotMsg(SendRobotMsgParam param) {
        try {
            // 获取团队访问令牌
            String appAccessToken = openMeAuthService.getAppAccessToken();
            if (appAccessToken == null) {
                log.error("Failed to get app access token");
                return createErrorResponse(-1, "Failed to get app access token");
            }

            String teamAccessToken = openMeAuthService.getTeamAccessToken(appAccessToken);
            if (teamAccessToken == null) {
                log.error("Failed to get team access token");
                return createErrorResponse(-2, "Failed to get team access token");
            }

            // 构建请求URL
            String url = host + "/open-api/suite/v1/timline/sendRobotMsg";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("authorization", "Bearer " + teamAccessToken);

            // 处理@用户的特殊字符
            processAtUsersContent(param);

            // 构建完整的请求体，包括配置文件中的固定参数
            @SuppressWarnings("unchecked")
            Map<String, Object> requestBody = objectMapper.convertValue(param, Map.class);

            // 添加固定参数
            requestBody.put("appId", appId);
            requestBody.put("tenantId", tenantId);

            // 添加机器人ID
            if (param.getParams() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> paramsMap = (Map<String, Object>) requestBody.get("params");
                paramsMap.put("robotId", robotId);
            }

            // 发送请求
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            log.info("sendRobotMsg request: URL = {}, Headers = {}, Body = {}", url, headers, requestBody);
            @SuppressWarnings("unchecked")
            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);
            log.info("sendRobotMsg response: {}", response);
            return new JSONObject(response);
        } catch (Exception e) {
            log.error("sendRobotMsg error", e);
            return createErrorResponse(-3, "Error sending robot message: " + e.getMessage());
        }
    }

    /**
     * 处理@用户的特殊字符
     */
    private void processAtUsersContent(SendRobotMsgParam param) {
        if (param.getParams() != null && param.getParams().getBody() instanceof SendRobotMsgParam.TextMsgBody) {
            SendRobotMsgParam.TextMsgBody textBody = (SendRobotMsgParam.TextMsgBody) param.getParams().getBody();
            if (textBody.getAtUsers() != null && !textBody.getAtUsers().isEmpty()) {
                // 生成特殊间隔符
                String interval = new String(new byte[]{(byte)0xe2, (byte)0x80, (byte)0x85}, Charset.forName("UTF-8"));

                // 确保内容中的@用户名之间使用特殊间隔符
                String content = textBody.getContent();
                if (content != null && !content.isEmpty()) {
                    // 这里简单处理，实际应用中可能需要更复杂的逻辑
                    for (SendRobotMsgParam.AtUser atUser : textBody.getAtUsers()) {
                        String atMark = "@" + atUser.getNickname();
                        if (content.contains(atMark)) {
                            // 确保@后面有特殊间隔符
                            if (!content.contains(atMark + interval)) {
                                content = content.replace(atMark, atMark + interval);
                            }
                        }
                    }
                    textBody.setContent(content);
                }
            }
        }
    }

    @Override
    public JSONObject sendJUEMsg(SendJUEMsgParam param) {
        try {
            // 参数验证
            if (param == null) {
                log.error("SendJUEMsgParam is null");
                return createErrorResponse(-1, "SendJUEMsgParam is null");
            }

            if (param.getParams() == null) {
                log.error("params is null");
                return createErrorResponse(-1, "params is null");
            }

            if (param.getParams().getData() == null) {
                log.error("params.data is null");
                return createErrorResponse(-1, "params.data is null");
            }

            // 获取团队访问令牌
            String appAccessToken = openMeAuthService.getAppAccessToken();
            if (appAccessToken == null) {
                log.error("Failed to get app access token");
                return createErrorResponse(-1, "Failed to get app access token");
            }

            String teamAccessToken = openMeAuthService.getTeamAccessToken(appAccessToken);
            if (teamAccessToken == null) {
                log.error("Failed to get team access token");
                return createErrorResponse(-2, "Failed to get team access token");
            }

            // 构建请求URL
            String url = host + "/open-api/suite/v1/timline/sendJUEMsg";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("authorization", "Bearer " + teamAccessToken);

            // 构建完整的请求体，包括配置文件中的固定参数
            @SuppressWarnings("unchecked")
            Map<String, Object> requestBody = objectMapper.convertValue(param, Map.class);

            // 添加固定参数
            requestBody.put("appId", appId);
            requestBody.put("tenantId", tenantId);

            // 添加机器人ID
            if (param.getParams() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> paramsMap = (Map<String, Object>) requestBody.get("params");
                paramsMap.put("robotId", robotId);

                // 确保 data 中的 templateId 不为空
                if (param.getParams().getData() != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> dataMap = (Map<String, Object>) paramsMap.get("data");
                    if (dataMap != null && (dataMap.get("templateId") == null || dataMap.get("templateId").toString().isEmpty())) {
                        // 如果模板ID为空，使用默认的模板ID
                        dataMap.put("templateId", "templateMsgCard");
                    }
                }
            }

            // 打印请求体，便于调试
            try {
                log.info("sendJUEMsg request body: {}", objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(requestBody));
            } catch (Exception e) {
                log.warn("Failed to print request body", e);
            }

            // 发送请求
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            @SuppressWarnings("unchecked")
            Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);

            log.info("sendJUEMsg response: {}", response);
            return new JSONObject(response);
        } catch (Exception e) {
            log.error("sendJUEMsg error", e);
            return createErrorResponse(-3, "Error sending JUE message: " + e.getMessage());
        }
    }

    /**
     * 创建错误响应
     */
    private JSONObject createErrorResponse(int code, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", code);
        response.put("msg", message);
        response.put("success", false);
        return new JSONObject(response);
    }
}
