package com.jdl.mcp.server.jsf.exception;

/**
 * JSF异常类
 * 
 * <AUTHOR> Assistant
 */
public class JsfException extends RuntimeException {
    
    private final JsfErrorCode errorCode;
    
    public JsfException(JsfErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }
    
    public JsfException(JsfErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public JsfException(JsfErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public JsfException(JsfErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
    }
    
    public JsfErrorCode getErrorCode() {
        return errorCode;
    }
    
    public int getCode() {
        return errorCode.getCode();
    }
}
