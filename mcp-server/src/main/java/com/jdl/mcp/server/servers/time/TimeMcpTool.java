package com.jdl.mcp.server.servers.time;

import com.jdl.mcp.core.service.annotation.McpEndpoint;
import com.jdl.mcp.core.service.annotation.Tool;
import com.jdl.mcp.core.service.annotation.ToolParam;
import com.jdl.mcp.core.service.annotation.ToolParamBean;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.zone.ZoneRulesException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 提供时间和时区转换服务
 * <p>
 * 该服务允许获取当前时间和在不同时区之间转换时间
 * </p>
 */
@Component
@McpEndpoint(name = "time", description = "提供时间和时区转换服务")
public class TimeMcpTool {
    private static final Logger logger = LoggerFactory.getLogger(TimeMcpTool.class);
    private static final String CONFIG_PATH = "servers/time.properties";

    private String defaultTimezone;
    private boolean use24HourFormat;
    private String dateFormat;
    private String timeFormat;
    private String datetimeFormat;

    public TimeMcpTool() {
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        Properties props = new Properties();
        try (InputStream inputStream = new ClassPathResource(CONFIG_PATH).getInputStream()) {
            props.load(inputStream);
            // 读取配置项
            defaultTimezone = props.getProperty("time.default_timezone", "Asia/Shanghai");
            use24HourFormat = Boolean.parseBoolean(props.getProperty("time.use_24hour_format", "true"));
            dateFormat = props.getProperty("time.date_format", "yyyy-MM-dd");
            timeFormat = props.getProperty("time.time_format", use24HourFormat ? "HH:mm:ss" : "hh:mm:ss a");
            datetimeFormat = props.getProperty("time.datetime_format", "yyyy-MM-dd'T'HH:mm:ssXXX");
        } catch (IOException e) {
            // 在测试环境中，配置文件可能不存在，使用默认值
            logger.error("无法加载配置文件: {}, 使用默认配置", CONFIG_PATH, e);
            defaultTimezone = "Asia/Shanghai";
            use24HourFormat = true;
            dateFormat = "yyyy-MM-dd";
            timeFormat = "HH:mm:ss";
            datetimeFormat = "yyyy-MM-dd'T'HH:mm:ssXXX";
        }
    }


    @Tool(name = "get_current_time", description = "获取当前时间，如果不指定时区则使用配置的默认时区")
    public Map<String, Object> getCurrentTime(@ToolParamBean GetCurrentTimeParams params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }

            String timezone = params.getTimezone(); // 这会返回用户指定的时区或默认时区
            validateTimezone(timezone);

            if (timezone.equals(defaultTimezone)) {
                logger.debug("使用默认时区: {}", defaultTimezone);
            } else {
                logger.debug("使用指定时区: {}", timezone);
            }

            ZoneId zoneId = ZoneId.of(timezone);
            ZonedDateTime now = ZonedDateTime.now(zoneId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("timezone", params.getTimezone());
            result.put("datetime", now.format(DateTimeFormatter.ofPattern(datetimeFormat)));
            result.put("date", now.format(DateTimeFormatter.ofPattern(dateFormat)));
            result.put("time", now.format(DateTimeFormatter.ofPattern(timeFormat)));
            result.put("timestamp", now.toInstant().toEpochMilli());
            result.put("is_dst", now.getZone().getRules().isDaylightSavings(now.toInstant()));
            result.put("zone_offset", now.getOffset().getId());

            return result;
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return error;
        }
    }

    @Tool(name = "convert_time", description = "在不同时区之间转换时间")
    public Map<String, Object> convertTime(@ToolParamBean ConvertTimeParams params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            if (params.getSourceTimezone() == null || params.getSourceTimezone().trim().isEmpty()) {
                throw new IllegalArgumentException("源时区不能为空");
            }
            if (params.getTargetTimezone() == null || params.getTargetTimezone().trim().isEmpty()) {
                throw new IllegalArgumentException("目标时区不能为空");
            }
            if (params.getTime() == null || params.getTime().trim().isEmpty()) {
                throw new IllegalArgumentException("时间不能为空");
            }

            validateTimezone(params.getSourceTimezone());
            validateTimezone(params.getTargetTimezone());
            validateTimeFormat(params.getTime());

            // 解析时间
            LocalDateTime localDateTime = LocalDateTime.now();
            String[] timeParts = params.getTime().split(":");
            if (timeParts.length != 2) {
                throw new IllegalArgumentException("时间格式错误，应为HH:mm");
            }

            try {
                int hour = Integer.parseInt(timeParts[0]);
                int minute = Integer.parseInt(timeParts[1]);

                if (hour < 0 || hour > 23) {
                    throw new IllegalArgumentException("小时必须在0-23之间");
                }
                if (minute < 0 || minute > 59) {
                    throw new IllegalArgumentException("分钟必须在0-59之间");
                }

                localDateTime = localDateTime.withHour(hour).withMinute(minute).withSecond(0).withNano(0);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("时间格式错误，小时和分钟必须为数字");
            }

            // 转换时区
            ZoneId sourceZoneId = ZoneId.of(params.getSourceTimezone());
            ZoneId targetZoneId = ZoneId.of(params.getTargetTimezone());

            ZonedDateTime sourceDateTime = localDateTime.atZone(sourceZoneId);
            ZonedDateTime targetDateTime = sourceDateTime.withZoneSameInstant(targetZoneId);

            // 计算时差
            int hoursDifference = targetDateTime.getOffset().getTotalSeconds() - sourceDateTime.getOffset().getTotalSeconds();
            hoursDifference = hoursDifference / 3600;
            String timeDifferenceStr = (hoursDifference >= 0 ? "+" : "") + hoursDifference + "h";

            // 构建结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);

            Map<String, Object> source = new HashMap<>();
            source.put("timezone", params.getSourceTimezone());
            source.put("datetime", sourceDateTime.format(DateTimeFormatter.ofPattern(datetimeFormat)));
            source.put("time", sourceDateTime.format(DateTimeFormatter.ofPattern(timeFormat)));
            source.put("is_dst", sourceDateTime.getZone().getRules().isDaylightSavings(sourceDateTime.toInstant()));

            Map<String, Object> target = new HashMap<>();
            target.put("timezone", params.getTargetTimezone());
            target.put("datetime", targetDateTime.format(DateTimeFormatter.ofPattern(datetimeFormat)));
            target.put("time", targetDateTime.format(DateTimeFormatter.ofPattern(timeFormat)));
            target.put("is_dst", targetDateTime.getZone().getRules().isDaylightSavings(targetDateTime.toInstant()));

            result.put("source", source);
            result.put("target", target);
            result.put("time_difference", timeDifferenceStr);

            return result;
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return error;
        }
    }

    /**
     * 验证时区是否有效
     *
     * @param timezone 时区ID
     * @throws IllegalArgumentException 如果时区无效
     */
    private void validateTimezone(String timezone) {
        if (timezone != null && !timezone.trim().isEmpty()) {
            try {
                ZoneId.of(timezone);
            } catch (ZoneRulesException e) {
                throw new IllegalArgumentException("无效的时区ID: " + timezone);
            }
        }
    }

    /**
     * 验证时间格式是否有效
     *
     * @param time 时间字符串
     * @throws IllegalArgumentException 如果格式无效
     */
    private void validateTimeFormat(String time) {
        if (time == null || time.trim().isEmpty()) {
            throw new IllegalArgumentException("时间不能为空");
        }
        if (!time.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$")) {
            throw new IllegalArgumentException("时间格式错误，应为HH:mm，如：14:30");
        }
    }


    @Data
    public class GetCurrentTimeParams {
        @ToolParam(name = "timezone", description = "时区ID，如：Asia/Shanghai, America/New_York等。可选，默认使用配置文件中的默认时区", required = false)
        private String timezone;

        public String getTimezone() {
            return timezone != null ? timezone : defaultTimezone;
        }
    }

    @Data
    public static class ConvertTimeParams {
        @ToolParam(name = "source_timezone", description = "源时区ID，如：Asia/Shanghai", required = true)
        private String sourceTimezone;

        @ToolParam(name = "target_timezone", description = "目标时区ID，如：America/New_York", required = true)
        private String targetTimezone;

        @ToolParam(name = "time", description = "要转换的时间，格式为HH:mm，如：14:30", required = true)
        private String time;
    }
}
