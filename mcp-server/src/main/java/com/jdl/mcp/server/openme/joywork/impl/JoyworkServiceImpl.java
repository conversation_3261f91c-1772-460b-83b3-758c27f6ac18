package com.jdl.mcp.server.openme.joywork.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jdl.mcp.server.openme.auth.OpenMeAuthService;
import com.jdl.mcp.server.openme.auth.OpenUserInfo;
import com.jdl.mcp.server.openme.joywork.JoyworkService;
import com.jdl.mcp.server.openme.joywork.param.JoyworkCreateTaskParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkUpdateTaskStatusParam;
import com.jdl.mcp.server.openme.joywork.param.JoyworkUrgeTaskParam;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class JoyworkServiceImpl implements JoyworkService {
    private static final Logger log = LoggerFactory.getLogger(JoyworkServiceImpl.class);

    @Value("${openme.app.host}")
    private String host;
    @Value("${openme.joywork.app}")
    private String joyworkApp;
    @Value("${openme.joywork.bizCode}")
    private String joyworkBizCode;
    @Value("${openme.joywork.openUserId}")
    private String joyworkOpenUserId;
    @Value("${openme.joywork.teamId}")
    private String joyworkTeamId;

    private final RestTemplate restTemplate;
    private final OpenMeAuthService openMeAuthService;
    private final ObjectMapper objectMapper;

    public JoyworkServiceImpl(RestTemplate restTemplate, OpenMeAuthService openMeAuthService,
            ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.openMeAuthService = openMeAuthService;
        this.objectMapper = objectMapper;
    }

    @Override
    public JSONObject createTask(JoyworkCreateTaskParam param) {
        param.setApp(joyworkApp);
        param.setBizCode(joyworkBizCode);

        String appAccessToken = openMeAuthService.getAppAccessToken();
        if (appAccessToken == null) {
            return error(-1, "获取app access token失败");
        }
        String teamAccessToken = openMeAuthService.getTeamAccessToken(appAccessToken);
        if (teamAccessToken == null) {
            return error(-2, "获取team access token失败");
        }
        OpenUserInfo createrUserInfo = openMeAuthService.getOpenUserInfo(teamAccessToken, joyworkOpenUserId);
        if (createrUserInfo != null && createrUserInfo.getOpenUserId() != null) {
            param.setOpenUserId(createrUserInfo.getOpenUserId());
        } else {
            return error(-3, "获取getOpenUserInfo 失败");
        }
        if (param.getOwnerList() != null) {
            for (JoyworkCreateTaskParam.Owner owner : param.getOwnerList()) {
                com.jdl.mcp.server.openme.auth.OpenUserInfo userInfo = openMeAuthService
                        .getOpenUserInfo(teamAccessToken, owner.getOpenUserId());
                if (userInfo != null && userInfo.getOpenUserId() != null) {
                    // 传进来的是erp，需要调用开放平台接口获取openUserId
                    owner.setOpenUserId(userInfo.getOpenUserId());
                }
            }
        }
        return postWithAuth("/open-api/suite/v1/joywork/syncTask", param, teamAccessToken);
    }

    @Override
    public JSONObject updateTask(JoyworkUpdateTaskParam param) {
        String appAccessToken = openMeAuthService.getAppAccessToken();
        if (appAccessToken == null) {
            return error(-1, "获取app access token失败");
        }
        String teamAccessToken = openMeAuthService.getTeamAccessToken(appAccessToken);
        if (teamAccessToken == null) {
            return error(-2, "获取team access token失败");
        }
        OpenUserInfo createrUserInfo = openMeAuthService.getOpenUserInfo(teamAccessToken, joyworkOpenUserId);
        if (createrUserInfo != null && createrUserInfo.getOpenUserId() != null) {
            param.setOpenUserId(createrUserInfo.getOpenUserId());
        } else {
            return error(-3, "获取getOpenUserInfo 失败");
        }
        return postWithAuth("/open-api/suite/v1/joywork/updateTask", param, teamAccessToken);
    }

    @Override
    public JSONObject updateTaskStatus(JoyworkUpdateTaskStatusParam param) {
        String appAccessToken = openMeAuthService.getAppAccessToken();
        if (appAccessToken == null) {
            return error(-1, "获取app access token失败");
        }
        String teamAccessToken = openMeAuthService.getTeamAccessToken(appAccessToken);
        if (teamAccessToken == null) {
            return error(-2, "获取team access token失败");
        }
        OpenUserInfo createrUserInfo = openMeAuthService.getOpenUserInfo(teamAccessToken, joyworkOpenUserId);
        if (createrUserInfo != null && createrUserInfo.getOpenUserId() != null) {
            param.setOpenUserId(createrUserInfo.getOpenUserId());
        } else {
            return error(-3, "获取getOpenUserInfo 失败");
        }
        return postWithAuth("/open-api/suite/v2/joywork/updateTaskStatus", param, teamAccessToken);
    }

    @Override
    public JSONObject urgeTask(JoyworkUrgeTaskParam param) {
        String appAccessToken = openMeAuthService.getAppAccessToken();
        if (appAccessToken == null) {
            return error(-1, "获取app access token失败");
        }
        String teamAccessToken = openMeAuthService.getTeamAccessToken(appAccessToken);
        if (teamAccessToken == null) {
            return error(-2, "获取team access token失败");
        }
        // 自动填充每个TaskUser的teamId和app
        if (param.getTaskUsers() != null) {
            for (JoyworkUrgeTaskParam.TaskUser user : param.getTaskUsers()) {
                user.setTeamId(joyworkTeamId);
                user.setApp(joyworkApp);
            }
        }
        return postWithAuth("/open-api/suite/v1/joywork/urgeTask", param, teamAccessToken);
    }

    @SuppressWarnings("unchecked")
    private JSONObject postWithAuth(String path, Object param, String teamAccessToken) {
        try {
            String url = host + path;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("authorization", "Bearer " + teamAccessToken);
            Map<String, Object> requestBody = objectMapper.convertValue(param, Map.class);
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            HttpEntity<String> request = new HttpEntity<>(jsonBody, headers);
            log.info("joywork请求: url={}, body={}", url, jsonBody);
            String responseStr = restTemplate.postForObject(url, request, String.class);
            log.info("joywork响应: {}", responseStr);
            return new JSONObject(responseStr);
        } catch (Exception e) {
            log.error("joywork接口异常", e);
            return error(-99, e.getMessage());
        }
    }

    private JSONObject error(int code, String msg) {
        Map<String, Object> map = new HashMap<>();
        map.put("code", code);
        map.put("msg", msg);
        map.put("success", false);
        return new JSONObject(map);
    }
}