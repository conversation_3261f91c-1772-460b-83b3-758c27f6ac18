package com.jdl.mcp.server.servers.jsf;

import com.jd.jsf.open.api.vo.InterfaceInfo;
import com.jdl.mcp.core.service.annotation.McpEndpoint;
import com.jdl.mcp.core.service.annotation.Tool;
import com.jdl.mcp.core.service.annotation.ToolParam;
import com.jdl.mcp.core.service.annotation.ToolParamBean;
import com.jdl.mcp.server.jsf.model.JsfResult;
import com.jdl.mcp.server.jsf.service.JsfInterfaceService;
import com.jdl.mcp.server.jsf.service.JsfProviderAliasService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JSF开放平台服务工具
 * <p>
 * 提供JSF开放平台的接口查询、方法信息获取、别名查询等功能
 * </p>
 */
@Component
@McpEndpoint(name = "jsf", description = "JSF开放平台服务工具，提供接口查询、方法信息获取、别名查询等功能")
public class JsfMcpTool {
    private static final Logger logger = LoggerFactory.getLogger(JsfMcpTool.class);

    @Autowired(required = false)
    private JsfInterfaceService jsfInterfaceService;

    @Autowired(required = false)
    private JsfProviderAliasService jsfProviderAliasService;

    /**
     * 根据接口名称查询接口信息
     */
    @Tool(name = "getInterfaceInfo", description = "根据接口名称查询接口信息")
    public Map<String, Object> getInterfaceInfo(@ToolParamBean GetInterfaceInfoParams params) {
        try {
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            
            logger.info("查询接口信息，接口名称: {}, 操作人: {}", params.getInterfaceName(), params.getOperator());

            if (jsfInterfaceService == null) {
                return error("JSF接口服务未配置或不可用");
            }

            JsfResult<InterfaceInfo> jsfResult = jsfInterfaceService.getByInterfaceName(
                params.getInterfaceName(), 
                params.getOperator()
            );
            
            result.put("success", jsfResult.isSuccess());
            result.put("code", jsfResult.getCode());
            result.put("message", jsfResult.getMsg());
            
            if (jsfResult.isSuccess() && jsfResult.getData() != null) {
                result.put("data", jsfResult.getData());
                logger.info("查询接口信息成功: {}", jsfResult.getData());
            } else {
                result.put("error", jsfResult.getMsg());
                logger.warn("查询接口信息失败: {}", jsfResult.getMsg());
            }
            
            return result;
        } catch (Exception e) {
            logger.error("getInterfaceInfo 执行失败", e);
            return error("getInterfaceInfo 执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取接口方法的详细信息
     */
    @Tool(name = "getMethodInfo", description = "获取接口方法的详细信息，包含入参和出参信息")
    public Map<String, Object> getMethodInfo(@ToolParamBean GetMethodInfoParams params) {
        try {
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            
            logger.info("获取方法信息，接口名称: {}, 方法名: {}, 操作人: {}",
                params.getInterfaceName(), params.getMethodName(), params.getOperator());

            if (jsfInterfaceService == null) {
                return error("JSF接口服务未配置或不可用");
            }

            JsfResult<String> jsfResult = jsfInterfaceService.getMethodInfo(
                params.getInterfaceName(),
                params.getAlias(),
                params.getIp(),
                params.getPort(),
                params.getMethodName(),
                params.getOperator()
            );
            
            result.put("success", jsfResult.isSuccess());
            result.put("code", jsfResult.getCode());
            result.put("message", jsfResult.getMsg());
            
            if (jsfResult.isSuccess() && jsfResult.getData() != null) {
                result.put("data", jsfResult.getData());
                logger.info("获取方法信息成功");
            } else {
                result.put("error", jsfResult.getMsg());
                logger.warn("获取方法信息失败: {}", jsfResult.getMsg());
            }
            
            return result;
        } catch (Exception e) {
            logger.error("getMethodInfo 执行失败", e);
            return error("getMethodInfo 执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取接口的方法列表
     */
    @Tool(name = "getMethodList", description = "获取接口的所有方法列表")
    public Map<String, Object> getMethodList(@ToolParamBean GetMethodListParams params) {
        try {
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            
            logger.info("获取方法列表，接口名称: {}, 操作人: {}", params.getInterfaceName(), params.getOperator());

            if (jsfInterfaceService == null) {
                return error("JSF接口服务未配置或不可用");
            }

            JsfResult<List<String>> jsfResult = jsfInterfaceService.getMethodList(
                params.getInterfaceName(),
                params.getAlias(),
                params.getIp(),
                params.getPort(),
                null, // methodName - 获取所有方法时传null
                params.getOperator()
            );
            
            result.put("success", jsfResult.isSuccess());
            result.put("code", jsfResult.getCode());
            result.put("message", jsfResult.getMsg());
            
            if (jsfResult.isSuccess() && jsfResult.getData() != null) {
                result.put("data", jsfResult.getData());
                result.put("total", jsfResult.getData().size());
                logger.info("获取方法列表成功，共{}个方法", jsfResult.getData().size());
            } else {
                result.put("error", jsfResult.getMsg());
                logger.warn("获取方法列表失败: {}", jsfResult.getMsg());
            }
            
            return result;
        } catch (Exception e) {
            logger.error("getMethodList 执行失败", e);
            return error("getMethodList 执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据接口名称获取别名列表
     */
    @Tool(name = "getAliasByInterfaceName", description = "根据接口名称获取所有可用的别名列表")
    public Map<String, Object> getAliasByInterfaceName(@ToolParamBean GetAliasByInterfaceNameParams params) {
        try {
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            
            logger.info("获取接口别名，接口名称: {}, 操作人: {}", params.getInterfaceName(), params.getOperator());

            if (jsfProviderAliasService == null) {
                return error("JSF别名服务未配置或不可用");
            }

            JsfResult<List<String>> jsfResult = jsfProviderAliasService.getAliasByInterfaceName(
                params.getInterfaceName(),
                params.getOperator()
            );
            
            result.put("success", jsfResult.isSuccess());
            result.put("code", jsfResult.getCode());
            result.put("message", jsfResult.getMsg());
            
            if (jsfResult.isSuccess() && jsfResult.getData() != null) {
                result.put("data", jsfResult.getData());
                result.put("total", jsfResult.getData().size());
                logger.info("获取接口别名成功，共{}个别名", jsfResult.getData().size());
            } else {
                result.put("error", jsfResult.getMsg());
                logger.warn("获取接口别名失败: {}", jsfResult.getMsg());
            }
            
            return result;
        } catch (Exception e) {
            logger.error("getAliasByInterfaceName 执行失败", e);
            return error("getAliasByInterfaceName 执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 统一错误处理方法
     */
    private Map<String, Object> error(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("error", message);
        return result;
    }

    /**
     * 带堆栈跟踪的错误处理方法
     */
    private Map<String, Object> error(String message, Exception e) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("error", message);
        result.put("stackTrace", ExceptionUtils.getStackTrace(e));
        return result;
    }

    // ==================== 参数类定义 ====================

    /**
     * 获取接口信息参数类
     */
    public static class GetInterfaceInfoParams {
        /** 接口名称 */
        @ToolParam(name = "interfaceName", description = "JSF接口的完整类名，如：com.jd.example.service.ExampleService", required = true)
        private String interfaceName;

        /** 操作人ERP */
        @ToolParam(name = "operator", description = "操作人的ERP账号", required = true)
        private String operator;

        public void validateSelf() {
            if (interfaceName == null || interfaceName.trim().isEmpty()) {
                throw new IllegalArgumentException("interfaceName 不能为空");
            }
            if (operator == null || operator.trim().isEmpty()) {
                throw new IllegalArgumentException("operator 不能为空");
            }
        }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }
        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }
    }

    /**
     * 获取方法信息参数类
     */
    public static class GetMethodInfoParams {
        /** 接口名称 */
        @ToolParam(name = "interfaceName", description = "JSF接口的完整类名", required = true)
        private String interfaceName;

        /** 方法名称 */
        @ToolParam(name = "methodName", description = "要查询的方法名称，如果为空则返回所有方法信息", required = false)
        private String methodName;

        /** 别名 */
        @ToolParam(name = "alias", description = "指定的别名，可选参数", required = false)
        private String alias;

        /** IP地址 */
        @ToolParam(name = "ip", description = "Provider的IP地址，可选参数", required = false)
        private String ip;

        /** 端口号 */
        @ToolParam(name = "port", description = "Provider的端口号，可选参数", required = false)
        private Integer port;

        /** 操作人ERP */
        @ToolParam(name = "operator", description = "操作人的ERP账号", required = true)
        private String operator;

        public void validateSelf() {
            if (interfaceName == null || interfaceName.trim().isEmpty()) {
                throw new IllegalArgumentException("interfaceName 不能为空");
            }
            if (operator == null || operator.trim().isEmpty()) {
                throw new IllegalArgumentException("operator 不能为空");
            }
        }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }
        public String getMethodName() { return methodName; }
        public void setMethodName(String methodName) { this.methodName = methodName; }
        public String getAlias() { return alias; }
        public void setAlias(String alias) { this.alias = alias; }
        public String getIp() { return ip; }
        public void setIp(String ip) { this.ip = ip; }
        public Integer getPort() { return port; }
        public void setPort(Integer port) { this.port = port; }
        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }
    }

    /**
     * 获取方法列表参数类
     */
    public static class GetMethodListParams {
        /** 接口名称 */
        @ToolParam(name = "interfaceName", description = "JSF接口的完整类名", required = true)
        private String interfaceName;

        /** 别名 */
        @ToolParam(name = "alias", description = "指定的别名，可选参数", required = false)
        private String alias;

        /** IP地址 */
        @ToolParam(name = "ip", description = "Provider的IP地址，可选参数", required = false)
        private String ip;

        /** 端口号 */
        @ToolParam(name = "port", description = "Provider的端口号，可选参数", required = false)
        private Integer port;

        /** 操作人ERP */
        @ToolParam(name = "operator", description = "操作人的ERP账号", required = true)
        private String operator;

        public void validateSelf() {
            if (interfaceName == null || interfaceName.trim().isEmpty()) {
                throw new IllegalArgumentException("interfaceName 不能为空");
            }
            if (operator == null || operator.trim().isEmpty()) {
                throw new IllegalArgumentException("operator 不能为空");
            }
        }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }
        public String getAlias() { return alias; }
        public void setAlias(String alias) { this.alias = alias; }
        public String getIp() { return ip; }
        public void setIp(String ip) { this.ip = ip; }
        public Integer getPort() { return port; }
        public void setPort(Integer port) { this.port = port; }
        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }
    }

    /**
     * 根据接口名称获取别名参数类
     */
    public static class GetAliasByInterfaceNameParams {
        /** 接口名称 */
        @ToolParam(name = "interfaceName", description = "JSF接口的完整类名", required = true)
        private String interfaceName;

        /** 操作人ERP */
        @ToolParam(name = "operator", description = "操作人的ERP账号", required = true)
        private String operator;

        public void validateSelf() {
            if (interfaceName == null || interfaceName.trim().isEmpty()) {
                throw new IllegalArgumentException("interfaceName 不能为空");
            }
            if (operator == null || operator.trim().isEmpty()) {
                throw new IllegalArgumentException("operator 不能为空");
            }
        }

        public String getInterfaceName() { return interfaceName; }
        public void setInterfaceName(String interfaceName) { this.interfaceName = interfaceName; }
        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }
    }
}
