package com.jdl.mcp.server.openme.joywork.param;

import java.util.List;

public class JoyworkUrgeTaskParam {
    private String taskId;
    private String urgeContent;
    private List<TaskUser> taskUsers;

    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }
    public String getUrgeContent() { return urgeContent; }
    public void setUrgeContent(String urgeContent) { this.urgeContent = urgeContent; }
    public List<TaskUser> getTaskUsers() { return taskUsers; }
    public void setTaskUsers(List<TaskUser> taskUsers) { this.taskUsers = taskUsers; }

    public static class TaskUser {
        private String erp;
        private String teamId;
        private String app;
        public String getErp() { return erp; }
        public void setErp(String erp) { this.erp = erp; }
        public String getTeamId() { return teamId; }
        public void setTeamId(String teamId) { this.teamId = teamId; }
        public String getApp() { return app; }
        public void setApp(String app) { this.app = app; }
    }
} 