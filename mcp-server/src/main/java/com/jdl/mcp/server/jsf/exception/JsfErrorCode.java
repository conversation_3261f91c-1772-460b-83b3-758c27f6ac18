package com.jdl.mcp.server.jsf.exception;

/**
 * JSF错误码枚举
 * 
 * <AUTHOR> Assistant
 */
public enum JsfErrorCode {
    
    // 通用错误
    SUCCESS(1, "调用成功"),
    FAILURE(0, "失败情况"),
    PARAM_ERROR(3, "参数不符合要求"),
    PERMISSION_DENIED(-4, "应用或者operator无权限"),
    RECORD_NOT_FOUND(-6, "查询不到对应的记录"),
    SERVER_ERROR(5, "服务端异常"),
    
    // 客户端错误
    CONFIG_ERROR(1001, "配置错误"),
    SIGN_ERROR(1002, "签名生成失败"),
    NETWORK_ERROR(1003, "网络调用失败"),
    PARSE_ERROR(1004, "响应解析失败"),
    TIMEOUT_ERROR(1005, "调用超时");
    
    private final int code;
    private final String message;
    
    JsfErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据code获取错误码枚举
     */
    public static JsfErrorCode fromCode(int code) {
        for (JsfErrorCode errorCode : values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return FAILURE;
    }
}
