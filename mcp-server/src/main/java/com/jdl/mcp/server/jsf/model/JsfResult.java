package com.jdl.mcp.server.jsf.model;

/**
 * JSF统一响应结果类
 * 
 * <AUTHOR> Assistant
 */
public class JsfResult<T> {
    
    /**
     * 返回code码
     */
    private Integer code;
    
    /**
     * 调用的返回消息
     */
    private String msg;
    
    /**
     * 数据域
     */
    private T data;
    
    /**
     * 当需要分页查询时，包含数据的总数据
     */
    private Integer total;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    public JsfResult() {
    }
    
    public JsfResult(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.success = code != null && code == 1;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
        this.success = code != null && code == 1;
    }
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public Integer getTotal() {
        return total;
    }
    
    public void setTotal(Integer total) {
        this.total = total;
    }
    
    public Boolean getSuccess() {
        return success;
    }
    
    public void setSuccess(Boolean success) {
        this.success = success;
    }
    
    public boolean isSuccess() {
        return success != null && success;
    }
    
    @Override
    public String toString() {
        return "JsfResult{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                ", total=" + total +
                ", success=" + success +
                '}';
    }
}
