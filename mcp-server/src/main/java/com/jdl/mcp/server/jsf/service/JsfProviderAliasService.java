package com.jdl.mcp.server.jsf.service;

import com.jdl.mcp.server.jsf.model.JsfResult;

import java.util.List;

/**
 * JSF别名服务接口
 * 
 * <AUTHOR> Assistant
 */
public interface JsfProviderAliasService {
    
    /**
     * 根据接口名获取别名列表
     * 
     * @param interfaceName 接口名称，必填
     * @param operator 操作人erp
     * @return 别名列表
     */
    JsfResult<List<String>> getAliasByInterfaceName(String interfaceName, String operator);
}
