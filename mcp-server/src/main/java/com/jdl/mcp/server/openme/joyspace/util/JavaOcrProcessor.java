package com.jdl.mcp.server.openme.joyspace.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 纯Java实现的OCR处理工具类
 * 用于对图片进行简单的OCR识别，判断OCR结果是否适合转换为Markdown，并将OCR文本转换为Markdown格式
 */
public class JavaOcrProcessor {
    private static final Logger logger = LoggerFactory.getLogger(JavaOcrProcessor.class);
    private static final int MIN_TEXT_LENGTH = 10; // 最小文本长度，小于此长度的OCR结果被视为不适合转换

    // OCR配置参数
    private final String language;
    private final int threshold;

    /**
     * 默认构造函数，使用英文作为默认语言
     */
    public JavaOcrProcessor() {
        this("eng", 127);
    }

    /**
     * 带语言参数的构造函数
     * @param language OCR识别的语言，如eng（英文）、chi_sim（简体中文）等
     * @param threshold 二值化阈值（0-255），默认127
     */
    public JavaOcrProcessor(String language, int threshold) {
        this.language = language;
        this.threshold = threshold;
        logger.info("初始化Java OCR处理器，语言: {}, 阈值: {}", language, threshold);
    }

    /**
     * 对图片文件进行OCR识别
     * @param imageFile 图片文件
     * @return OCR识别的文本，如果识别失败则返回null
     */
    public String performOcr(File imageFile) {
        try {
            BufferedImage image = ImageIO.read(imageFile);
            return performOcr(image);
        } catch (IOException e) {
            logger.error("Failed to read image file: " + imageFile.getPath(), e);
            return null;
        }
    }

    /**
     * 对图片对象进行OCR识别
     * @param image 图片对象
     * @return OCR识别的文本，如果识别失败则返回null
     */
    public String performOcr(BufferedImage image) {
        try {
            // 1. 预处理图像
            BufferedImage processedImage = preprocessImage(image);
            
            // 2. 提取文本（简单实现，实际项目中可能需要更复杂的算法）
            String extractedText = extractTextFromImage(processedImage);
            
            return extractedText;
        } catch (Exception e) {
            logger.error("Failed to perform OCR on image", e);
            return null;
        }
    }

    /**
     * 预处理图像，包括灰度化、二值化等
     * @param originalImage 原始图像
     * @return 处理后的图像
     */
    private BufferedImage preprocessImage(BufferedImage originalImage) {
        // 1. 转换为灰度图
        BufferedImage grayImage = new BufferedImage(
                originalImage.getWidth(), 
                originalImage.getHeight(), 
                BufferedImage.TYPE_BYTE_GRAY);
        
        Graphics g = grayImage.getGraphics();
        g.drawImage(originalImage, 0, 0, null);
        g.dispose();
        
        // 2. 二值化处理
        BufferedImage binaryImage = new BufferedImage(
                grayImage.getWidth(), 
                grayImage.getHeight(), 
                BufferedImage.TYPE_BYTE_BINARY);
        
        for (int y = 0; y < grayImage.getHeight(); y++) {
            for (int x = 0; x < grayImage.getWidth(); x++) {
                int grayValue = grayImage.getRGB(x, y) & 0xFF;
                if (grayValue < threshold) {
                    binaryImage.setRGB(x, y, Color.BLACK.getRGB());
                } else {
                    binaryImage.setRGB(x, y, Color.WHITE.getRGB());
                }
            }
        }
        
        return binaryImage;
    }

    /**
     * 从图像中提取文本
     * @param image 处理后的图像
     * @return 提取的文本
     */
    private String extractTextFromImage(BufferedImage image) {
        // 注意：这是一个简化的实现，实际的OCR需要更复杂的算法
        // 在实际项目中，您可能需要使用机器学习模型或其他OCR库
        
        // 简单的文本提取示例：检测图像中是否有文本区域
        boolean hasTextRegions = detectTextRegions(image);

        //todo 此处仅为示例，需要实现识别文档中的图片，转换为ai友好的格式
        return "";
        
        // if (hasTextRegions) {
        //     // 这里返回一个简单的提示，表明图像中可能包含文本
        //     // 在实际应用中，您需要实现真正的文本识别算法
        //     return "图像中检测到可能的文本内容。\n\n" +
        //            "注意：这是使用纯Java实现的简化OCR功能，仅用于演示。\n" +
        //            "对于生产环境，建议使用专业的OCR库或服务。";
        // } else {
        //     return "未检测到文本内容。";
        // }
    }

    /**
     * 检测图像中是否有文本区域
     * @param image 处理后的图像
     * @return 是否检测到文本区域
     */
    private boolean detectTextRegions(BufferedImage image) {
        // 简单的文本区域检测：计算黑色像素的比例
        int blackPixels = 0;
        int totalPixels = image.getWidth() * image.getHeight();
        
        for (int y = 0; y < image.getHeight(); y++) {
            for (int x = 0; x < image.getWidth(); x++) {
                if (image.getRGB(x, y) == Color.BLACK.getRGB()) {
                    blackPixels++;
                }
            }
        }
        
        double blackRatio = (double) blackPixels / totalPixels;
        
        // 如果黑色像素比例在合理范围内，可能包含文本
        return blackRatio > 0.01 && blackRatio < 0.3;
    }

    /**
     * 判断OCR结果是否适合转换为Markdown
     * @param ocrText OCR识别的文本
     * @return 如果适合转换则返回true，否则返回false
     */
    public boolean isTextSuitableForMarkdown(String ocrText) {
        if (ocrText == null || ocrText.trim().length() < MIN_TEXT_LENGTH) {
            return false;
        }

        // 检查是否包含多行文本，这通常表示有结构化内容
        if (ocrText.contains("\n")) {
            return true;
        }

        // 检查是否包含表格结构（如有规律的空格或制表符）
        Pattern tablePattern = Pattern.compile(".*\\|.*\\|.*");
        if (tablePattern.matcher(ocrText).find()) {
            return true;
        }

        // 其他判断逻辑可以根据需要添加

        return false;
    }

    /**
     * 将OCR文本转换为Markdown格式
     * @param ocrText OCR识别的文本
     * @return 转换后的Markdown文本
     */
    public String convertOcrTextToMarkdown(String ocrText) {
        if (ocrText == null) {
            return "";
        }

        // 检查是否是表格
        if (ocrText.contains("|") && ocrText.contains("\n")) {
            // 表格处理：保留原始换行符
            return ocrText + "\n";
        }

        // 简单处理：将文本分割为段落
        String[] paragraphs = ocrText.split("\\n\\s*\\n");
        StringBuilder markdown = new StringBuilder();

        for (String paragraph : paragraphs) {
            // 去除多余的空白字符
            paragraph = paragraph.trim().replaceAll("\\s+", " ");

            // 检查是否是表格行
            if (paragraph.contains("|")) {
                // 简单的表格处理，可以根据需要增强
                markdown.append(paragraph).append("\n");
            } else {
                // 普通段落
                markdown.append(paragraph).append("\n\n");
            }
        }

        return markdown.toString();
    }
}
