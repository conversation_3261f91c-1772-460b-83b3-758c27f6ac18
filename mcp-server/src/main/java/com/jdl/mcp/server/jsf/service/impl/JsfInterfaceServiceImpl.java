package com.jdl.mcp.server.jsf.service.impl;

import com.jd.jsf.open.api.InterfaceService;
import com.jd.jsf.open.api.vo.request.QueryInterfaceRequest;
import com.jd.jsf.open.api.vo.request.QueryMethodInfoRequest;
import com.jd.jsf.open.api.vo.Result;
import com.jd.jsf.open.api.vo.InterfaceInfo;
import com.jdl.mcp.server.jsf.auth.JsfAuthService;
import com.jdl.mcp.server.jsf.exception.JsfErrorCode;
import com.jdl.mcp.server.jsf.exception.JsfException;
import com.jdl.mcp.server.jsf.model.JsfResult;
import com.jdl.mcp.server.jsf.param.BaseJsfRequest;
import com.jdl.mcp.server.jsf.service.JsfInterfaceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * JSF接口信息服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Service
public class JsfInterfaceServiceImpl implements JsfInterfaceService {
    
    private static final Logger log = LoggerFactory.getLogger(JsfInterfaceServiceImpl.class);
    
    @Autowired
    private JsfAuthService jsfAuthService;
    
    // 注入JSF接口服务，需要在Spring配置中配置consumer
    @Autowired(required = false)
    private InterfaceService interfaceService;
    
    @Override
    public JsfResult<InterfaceInfo> getByInterfaceName(String interfaceName, String operator) {
        if (interfaceName == null || interfaceName.trim().isEmpty()) {
            throw new JsfException(JsfErrorCode.PARAM_ERROR, "interfaceName cannot be null or empty");
        }
        
        if (operator == null || operator.trim().isEmpty()) {
            throw new JsfException(JsfErrorCode.PARAM_ERROR, "operator cannot be null or empty");
        }
        
        try {
            // 构建通用认证参数
            BaseJsfRequest commonParams = jsfAuthService.buildCommonParams(operator);
            
            // 创建JSF API请求对象
            QueryInterfaceRequest request = new QueryInterfaceRequest();
            request.setAppKey(commonParams.getAppKey());
            request.setOperator(commonParams.getOperator());
            request.setClientIp(commonParams.getClientIp());
            request.setTimeStamp(commonParams.getTimeStamp());
            request.setSign(commonParams.getSign());
            request.setInterfaceName(interfaceName);
            
            log.info("Calling JSF API getByInterfaceName with request: {}", request);
            
            // 调用JSF接口
            if (interfaceService == null) {
                throw new JsfException(JsfErrorCode.CONFIG_ERROR, "InterfaceService not configured");
            }
            
            Result<InterfaceInfo> result = interfaceService.getByInterfaceName(request);
            log.info("JSF API getByInterfaceName response: {}", result);
            
            // 转换结果
            JsfResult<InterfaceInfo> jsfResult = new JsfResult<>();
            jsfResult.setCode(result.getCode());
            jsfResult.setMsg(result.getMsg());
            jsfResult.setSuccess(result.isSuccess());
            
            if (result.isSuccess() && result.getData() != null) {
                jsfResult.setData(result.getData());
            }
            
            return jsfResult;
        } catch (JsfException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to get interface by name: " + interfaceName, e);
            throw new JsfException(JsfErrorCode.SERVER_ERROR, "Failed to get interface by name", e);
        }
    }
    
    @Override
    public JsfResult<String> getMethodInfo(String interfaceName, String alias, String ip, Integer port, String methodName, String operator) {
        if (interfaceName == null || interfaceName.trim().isEmpty()) {
            throw new JsfException(JsfErrorCode.PARAM_ERROR, "interfaceName cannot be null or empty");
        }
        
        if (operator == null || operator.trim().isEmpty()) {
            throw new JsfException(JsfErrorCode.PARAM_ERROR, "operator cannot be null or empty");
        }
        
        try {
            // 构建通用认证参数
            BaseJsfRequest commonParams = jsfAuthService.buildCommonParams(operator);
            
            // 创建JSF API请求对象
            QueryMethodInfoRequest request = new QueryMethodInfoRequest();
            request.setAppKey(commonParams.getAppKey());
            request.setOperator(commonParams.getOperator());
            request.setClientIp(commonParams.getClientIp());
            request.setTimeStamp(commonParams.getTimeStamp());
            request.setSign(commonParams.getSign());
            request.setInterfaceName(interfaceName);
            if (alias != null) {
                request.setAlias(alias);
            }
            if (ip != null) {
                request.setIp(ip);
            }
            if (port != null) {
                request.setPort(port);
            }
            if (methodName != null) {
                request.setMethodName(methodName);
            }
            
            log.info("Calling JSF API getMethodInfo with request: {}", request);
            
            // 调用JSF接口
            if (interfaceService == null) {
                throw new JsfException(JsfErrorCode.CONFIG_ERROR, "InterfaceService not configured");
            }
            
            Result<String> result = interfaceService.getMethodInfo(request);
            log.info("JSF API getMethodInfo response: {}", result);
            
            // 转换结果
            JsfResult<String> jsfResult = new JsfResult<>();
            jsfResult.setCode(result.getCode());
            jsfResult.setMsg(result.getMsg());
            jsfResult.setSuccess(result.isSuccess());
            jsfResult.setData(result.getData());
            
            return jsfResult;
        } catch (JsfException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to get method info for interface: " + interfaceName, e);
            throw new JsfException(JsfErrorCode.SERVER_ERROR, "Failed to get method info", e);
        }
    }
    
    @Override
    public JsfResult<List<String>> getMethodList(String interfaceName, String alias, String ip, Integer port, String methodName, String operator) {
        if (interfaceName == null || interfaceName.trim().isEmpty()) {
            throw new JsfException(JsfErrorCode.PARAM_ERROR, "interfaceName cannot be null or empty");
        }
        
        if (operator == null || operator.trim().isEmpty()) {
            throw new JsfException(JsfErrorCode.PARAM_ERROR, "operator cannot be null or empty");
        }
        
        try {
            // 构建通用认证参数
            BaseJsfRequest commonParams = jsfAuthService.buildCommonParams(operator);
            
            // 创建JSF API请求对象
            QueryMethodInfoRequest request = new QueryMethodInfoRequest();
            request.setAppKey(commonParams.getAppKey());
            request.setOperator(commonParams.getOperator());
            request.setClientIp(commonParams.getClientIp());
            request.setTimeStamp(commonParams.getTimeStamp());
            request.setSign(commonParams.getSign());
            request.setInterfaceName(interfaceName);
            if (alias != null) {
                request.setAlias(alias);
            }
            if (ip != null) {
                request.setIp(ip);
            }
            if (port != null) {
                request.setPort(port);
            }
            if (methodName != null) {
                request.setMethodName(methodName);
            }
            
            log.info("Calling JSF API getMethodList with request: {}", request);
            
            // 调用JSF接口
            if (interfaceService == null) {
                throw new JsfException(JsfErrorCode.CONFIG_ERROR, "InterfaceService not configured");
            }
            
            Result<List<String>> result = interfaceService.getMethodList(request);
            log.info("JSF API getMethodList response: {}", result);
            
            // 转换结果
            JsfResult<List<String>> jsfResult = new JsfResult<>();
            jsfResult.setCode(result.getCode());
            jsfResult.setMsg(result.getMsg());
            jsfResult.setSuccess(result.isSuccess());
            jsfResult.setData(result.getData());
            
            return jsfResult;
        } catch (JsfException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to get method list for interface: " + interfaceName, e);
            throw new JsfException(JsfErrorCode.SERVER_ERROR, "Failed to get method list", e);
        }
    }
    

}
