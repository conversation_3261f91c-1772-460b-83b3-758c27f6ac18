package com.jdl.mcp.server.openme.global;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.MethodArgumentNotValidException;

//@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(OpenMeException.class)
    public ResponseEntity<OpenMeErrorResponse> handleJoySpaceException(OpenMeException ex) {
        OpenMeErrorResponse errorResponse = new OpenMeErrorResponse(ex.getErrorCode().getCode(), ex.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<OpenMeErrorResponse> handleValidationException(MethodArgumentNotValidException ex) {
        OpenMeErrorResponse errorResponse = new OpenMeErrorResponse(
            OpenMeErrorCode.INVALID_PARAM_CHECK.getCode(),
            ex.getMessage()
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<OpenMeErrorResponse> handleGenericException(Exception ex) {
        OpenMeErrorResponse errorResponse = new OpenMeErrorResponse(
            OpenMeErrorCode.UNKNOWN_ERROR.getCode(),
            "An unexpected error occurred"
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
