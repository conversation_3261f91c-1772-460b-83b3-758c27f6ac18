package com.jdl.mcp.server.jsf.param;

/**
 * 查询方法信息请求参数类
 * 
 * <AUTHOR> Assistant
 */
public class QueryMethodInfoRequest extends BaseJsfRequest {
    
    /**
     * 接口名称，必填
     */
    private String interfaceName;
    
    /**
     * 指定别名，可选
     */
    private String alias;
    
    /**
     * 指定IP，可选
     * 如果未指定ip,默认自动查询一个存活provider,使用该provider的ip:port
     * 如果指定了ip那么同时也需要指定port
     */
    private String ip;
    
    /**
     * 指定端口，可选
     * 参考ip说明
     */
    private Integer port;
    
    /**
     * 方法名，可选
     * 如果接口方法特别多,建议指定方法名进行调用,否则可能返回的信息不全
     */
    private String methodName;
    
    public QueryMethodInfoRequest() {
        super();
    }
    
    public QueryMethodInfoRequest(String appKey, String operator, String clientIp, Long timeStamp, String sign, 
                                  String interfaceName, String alias, String ip, Integer port, String methodName) {
        super(appK<PERSON>, operator, clientIp, timeStamp, sign);
        this.interfaceName = interfaceName;
        this.alias = alias;
        this.ip = ip;
        this.port = port;
        this.methodName = methodName;
    }
    
    public String getInterfaceName() {
        return interfaceName;
    }
    
    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
    }
    
    public String getAlias() {
        return alias;
    }
    
    public void setAlias(String alias) {
        this.alias = alias;
    }
    
    public String getIp() {
        return ip;
    }
    
    public void setIp(String ip) {
        this.ip = ip;
    }
    
    public Integer getPort() {
        return port;
    }
    
    public void setPort(Integer port) {
        this.port = port;
    }
    
    public String getMethodName() {
        return methodName;
    }
    
    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }
    
    @Override
    public String toString() {
        return "QueryMethodInfoRequest{" +
                "interfaceName='" + interfaceName + '\'' +
                ", alias='" + alias + '\'' +
                ", ip='" + ip + '\'' +
                ", port=" + port +
                ", methodName='" + methodName + '\'' +
                ", " + super.toString() +
                '}';
    }
}
