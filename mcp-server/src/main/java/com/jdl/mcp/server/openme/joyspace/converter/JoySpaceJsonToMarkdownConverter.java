package com.jdl.mcp.server.openme.joyspace.converter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jdl.mcp.server.openme.joyspace.util.ImageDownloader;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class JoySpaceJsonToMarkdownConverter {

    private static final Logger logger = LoggerFactory.getLogger(JoySpaceJsonToMarkdownConverter.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static String OUT_PUT_PITH = "./src/main/resources/markdown_output/";

    // 配置参数
    private int imageDownloadTimeout = 10000; // 图片下载超时时间（毫秒）

    // 工具类实例
    private final ImageDownloader imageDownloader;
    //private JavaOcrProcessor imageOcrProcessor; // 非final，允许在异常情况下设置为null

    /**
     * 默认构造函数
     */
    public JoySpaceJsonToMarkdownConverter() {
        this.imageDownloader = new ImageDownloader(imageDownloadTimeout);
    }

    /**
     * 带配置参数的构造函数
     * @param imageDownloadTimeout 图片下载超时时间（毫秒）
     */
    public JoySpaceJsonToMarkdownConverter(int imageDownloadTimeout) {
        this.imageDownloadTimeout = imageDownloadTimeout;
        this.imageDownloader = new ImageDownloader(imageDownloadTimeout);
    }

    public String convertJsonStringToMarkdown(String jsonString) throws IOException {
        JsonNode rootNode = objectMapper.readTree(jsonString);
        return convertJsonNodeToMarkdown(rootNode);
    }

    public List<String> convertJsonStringsToMarkdown(List<String> jsonStrings) throws IOException {
        List<String> markdownStrings = new ArrayList<>();
        for (String jsonString : jsonStrings) {
            markdownStrings.add(convertJsonStringToMarkdown(jsonString));
        }
        return markdownStrings;
    }

    public String convertJsonFileToMarkdownFile(String jsonFilePath) throws IOException {
        String jsonContent = FileUtils.readFileToString(new File(jsonFilePath), StandardCharsets.UTF_8);
        String markdown = convertJsonStringToMarkdown(jsonContent);
        String markdownFilePath = OUT_PUT_PITH + new File(jsonFilePath).getName().replace(".json", ".md");
        FileUtils.writeStringToFile(new File(markdownFilePath), markdown, StandardCharsets.UTF_8);
        return markdownFilePath;
    }

    public List<String> convertJsonFilesToMarkdownFiles(List<String> jsonFilePaths) throws IOException {
        List<String> markdownFilePaths = new ArrayList<>();
        for (String jsonFilePath : jsonFilePaths) {
            markdownFilePaths.add(convertJsonFileToMarkdownFile(jsonFilePath));
        }
        return markdownFilePaths;
    }

    private String convertJsonNodeToMarkdown(JsonNode node) {
        StringBuilder markdown = new StringBuilder();
        if (node.isArray()) {
            for (JsonNode element : node) {
                markdown.append(convertElementToMarkdown(element));
            }
        }
        return markdown.toString();
    }

    private String convertElementToMarkdown(JsonNode element) {
        String type = "";
        if(element.has("type")){
            type = element.get("type").asText();
        }
        StringBuilder markdown = new StringBuilder();

        switch (type) {
            case "p":
                markdown.append(convertParagraphToMarkdown(element));
                break;
            case "list":
                markdown.append(convertListToMarkdown(element));
                break;
            case "table":
                markdown.append(convertTableToMarkdown(element));
                break;
            case "img":
                markdown.append(convertImageToMarkdown(element));
                break;
            case "block-code":
                markdown.append(convertCodeBlockToMarkdown(element));
                break;
            case "block-quote":
                markdown.append(convertBlockQuoteToMarkdown(element));
                break;
            case "divider":
                markdown.append("---\n\n");
                break;
            case "highlight-block":
                markdown.append(convertHighlightBlockToMarkdown(element));
                break;
            case "multi-column":
                markdown.append(convertMultiColumnToMarkdown(element));
                break;
            default:
                logger.error("发现未知 element type: {}", type);
                break;
        }

        return markdown.toString();
    }

    private String convertTableToMarkdown(JsonNode table) {
        StringBuilder markdown = new StringBuilder();
        List<List<String>> rows = new ArrayList<>();

        // First pass: collect all cell contents
        for (JsonNode row : table.get("children")) {
            if (row.get("type").asText().equals("table-row")) {
                List<String> cells = new ArrayList<>();
                for (JsonNode cell : row.get("children")) {
                    if (cell.get("type").asText().equals("table-cell")) {
                        StringBuilder cellContent = new StringBuilder();
                        for (JsonNode child : cell.get("children")) {
                            cellContent.append(convertElementToMarkdown(child));
                        }
                        String content = cellContent.toString().trim().replace("\n", "<br>");
                        cells.add(content);
                    }
                }
                rows.add(cells);
            }
        }

        if (rows.isEmpty()) {
            return "";
        }

        // Header
        markdown.append("|");
        for (String cell : rows.get(0)) {
            markdown.append(" ").append(cell).append(" |");
        }
        markdown.append("\n|");

        // Separator
        for (int i = 0; i < rows.get(0).size(); i++) {
            markdown.append(" --- |");
        }
        markdown.append("\n");

        // Body
        for (int i = 1; i < rows.size(); i++) {
            markdown.append("|");
            for (String cell : rows.get(i)) {
                markdown.append(" ").append(cell).append(" |");
            }
            markdown.append("\n");
        }

        markdown.append("\n");
        return markdown.toString();
    }

    private String convertParagraphToMarkdown(JsonNode paragraph) {
        StringBuilder markdown = new StringBuilder();
        int headerLevel = paragraph.has("header") ? paragraph.get("header").asInt() : 0;

        if (headerLevel > 0) {
            for (int i = 0; i < headerLevel; i++) {
                markdown.append("#");
            }
            markdown.append(" ");
        }

        for (JsonNode child : paragraph.get("children")) {
            markdown.append(convertInlineElementToMarkdown(child));
        }

        markdown.append("\n\n");
        return markdown.toString();
    }

    private String convertInlineElementToMarkdown(JsonNode element) {
        if (element.has("text")) {
            String text = element.get("text").asText();
            if (element.has("bold") && element.get("bold").asBoolean()) {
                text = "**" + text + "**";
            }
            if (element.has("italic") && element.get("italic").asBoolean()) {
                text = "*" + text + "*";
            }
            if (element.has("underLine") && element.get("underLine").asBoolean()) {
                text = "<u>" + text + "</u>";
            }
            if (element.has("strike") && element.get("strike").asBoolean()) {
                text = "~~" + text + "~~";
            }
            if (element.has("code") && element.get("code").asBoolean()) {
                text = "`" + text + "`";
            }
            return text;
        } else if (element.get("type").asText().equals("link")) {
            String url = element.get("url").asText();
            String linkText = element.get("children").get(0).get("text").asText();
            return String.format("[%s](%s)", linkText, url);
        }
        return "";
    }

    private String convertListToMarkdown(JsonNode list) {
        StringBuilder markdown = new StringBuilder();
        String listType = list.has("value") ? list.get("value").asText() : "bullet";
        int index = 1;

        JsonNode children = list.get("children");
        if (children != null && children.isArray()) {
            for (JsonNode item : children) {
                String prefix = listType.equals("bullet") ? "- " : listType.equals("ordered") ? index++ + ". " : "- [ ] ";
                markdown.append(prefix);

                JsonNode itemChildren = item.get("children");
                if (itemChildren != null && itemChildren.isArray()) {
                    for (JsonNode child : itemChildren) {
                        markdown.append(convertInlineElementToMarkdown(child));
                    }
                } else {
                    markdown.append(convertInlineElementToMarkdown(item));
                }
                markdown.append("\n");
            }
        }

        markdown.append("\n");
        return markdown.toString();
    }

    private String convertImageToMarkdown(JsonNode image) {
        String url = image.get("url").asText();
        String alt = image.has("alt") ? image.get("alt").asText() : "";
        return String.format("![%s](%s)\n\n", alt, url);
    }

    private String convertCodeBlockToMarkdown(JsonNode codeBlock) {
        StringBuilder markdown = new StringBuilder();
        String lang = codeBlock.has("lang") ? codeBlock.get("lang").asText() : "";

        markdown.append("```").append(lang).append("\n");
        for (JsonNode line : codeBlock.get("children")) {
            markdown.append(line.get("children").get(0).get("text").asText()).append("\n");
        }
        markdown.append("```\n\n");

        return markdown.toString();
    }

    private String convertBlockQuoteToMarkdown(JsonNode blockQuote) {
        StringBuilder markdown = new StringBuilder();
        for (JsonNode child : blockQuote.get("children")) {
            markdown.append("> ").append(convertInlineElementToMarkdown(child)).append("\n");
        }
        markdown.append("\n");
        return markdown.toString();
    }

    private String convertHighlightBlockToMarkdown(JsonNode highlightBlock) {
        StringBuilder markdown = new StringBuilder();
        markdown.append("<div style=\"background-color: #f0f0f0; padding: 10px;\">\n\n");
        for (JsonNode child : highlightBlock.get("children")) {
            markdown.append(convertElementToMarkdown(child));
        }
        markdown.append("</div>\n\n");
        return markdown.toString();
    }

    private String convertMultiColumnToMarkdown(JsonNode multiColumn) {
        StringBuilder markdown = new StringBuilder();
        markdown.append("<div style=\"display: flex;\">\n");
        for (JsonNode column : multiColumn.get("children")) {
            markdown.append("<div style=\"flex: 1; padding: 10px;\">\n\n");
            for (JsonNode child : column.get("children")) {
                markdown.append(convertElementToMarkdown(child));
            }
            markdown.append("</div>\n");
        }
        markdown.append("</div>\n\n");
        return markdown.toString();
    }
}
