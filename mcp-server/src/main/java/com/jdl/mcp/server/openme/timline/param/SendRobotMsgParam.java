package com.jdl.mcp.server.openme.timline.param;

import java.util.List;
import java.util.UUID;

/**
 * 发送机器人消息的参数
 */
public class SendRobotMsgParam {
    private String erp;
    private String groupId;
    private RobotMsgParams params;
    private String requestId;
    private Long dateTime;

    public SendRobotMsgParam() {
        this.requestId = UUID.randomUUID().toString();
        this.dateTime = System.currentTimeMillis();
    }

    public String getErp() {
        return erp;
    }

    public void setErp(String erp) {
        this.erp = erp;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public RobotMsgParams getParams() {
        return params;
    }

    public void setParams(RobotMsgParams params) {
        this.params = params;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Long getDateTime() {
        return dateTime;
    }

    public void setDateTime(Long dateTime) {
        this.dateTime = dateTime;
    }

    /**
     * 机器人消息参数
     */
    public static class RobotMsgParams {
        private TextMsgBody body;

        public TextMsgBody getBody() {
            return body;
        }

        public void setBody(TextMsgBody body) {
            this.body = body;
        }
    }

    /**
     * 文本消息体
     */
    public static class TextMsgBody {
        private String type;
        private String content;
        private List<AtUser> atUsers;

        public TextMsgBody() {
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public List<AtUser> getAtUsers() {
            return atUsers;
        }

        public void setAtUsers(List<AtUser> atUsers) {
            this.atUsers = atUsers;
        }
    }

    /**
     * @用户信息
     */
    public static class AtUser {
        private String app;
        private String pin;
        private String nickname;

        public String getApp() {
            return app;
        }

        public void setApp(String app) {
            this.app = app;
        }

        public String getPin() {
            return pin;
        }

        public void setPin(String pin) {
            this.pin = pin;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
    }
}
