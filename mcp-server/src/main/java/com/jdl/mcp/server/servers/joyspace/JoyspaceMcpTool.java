package com.jdl.mcp.server.servers.joyspace;

import com.jdl.mcp.core.service.annotation.McpEndpoint;
import com.jdl.mcp.core.service.annotation.Tool;
import com.jdl.mcp.core.service.annotation.ToolParam;
import com.jdl.mcp.core.service.annotation.ToolParamBean;
import com.jdl.mcp.core.utils.NumberUtils;
import com.jdl.mcp.server.openme.joyspace.JoyspaceService;
import com.jdl.mcp.server.openme.joyspace.converter.JoySpaceJsonToMarkdownConverter;
import com.jdl.mcp.server.openme.joyspace.param.GetFileListParam;
import com.jdl.mcp.server.openme.joyspace.param.GetFolderListParam;
import com.jdl.mcp.server.openme.joyspace.param.GetPageContentParam;
import com.jdl.mcp.server.openme.joyspace.param.GetPageInfosParam;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import org.json.JSONObject;
/**
 * 读取JoySpace服务，支持读取子文件夹列表、文件列表、页面信息、页面内容
 * <p>
 * 读取JoySpace服务，支持读取子文件夹列表（get_folder_list）、文件列表（get_file_list）、页面信息（get_page_infos）、页面内容（get_page_content）
 * </p>
 */
@Component
@McpEndpoint(name = "joyspace", description = "读取JoySpace服务，支持读取子文件夹列表、文件列表、页面信息、页面内容，提供对JoySpace文档协作平台的数据访问能力。")
public class JoyspaceMcpTool {
    private static final Logger logger = LoggerFactory.getLogger(JoyspaceMcpTool.class);
    private static final String CONFIG_PATH = "servers/joyspace.properties";
    private static final int PAGE_TYPE_OF_NEW_DOCUMENT = 13;

    JoySpaceJsonToMarkdownConverter converter = new JoySpaceJsonToMarkdownConverter();

    @Autowired
    private JoyspaceService joyspaceService;

    private String joyspaceTeamId;

    public JoyspaceMcpTool() {
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        Properties props = new Properties();
        try (InputStream inputStream = new ClassPathResource(CONFIG_PATH).getInputStream()) {
            props.load(inputStream);
            joyspaceTeamId = props.getProperty("joyspace.teamId", "b10vXr-LbPQ60n1pb7D3");
        } catch (IOException e) {
            logger.error("无法加载配置文件: {}, 使用默认配置", CONFIG_PATH, e);
            joyspaceTeamId = "b10vXr-LbPQ60n1pb7D3";
        }
    }

    @Tool(name = "getFolderList", description = "获取指定文件夹下的子文件夹列表")
    public Map<String, Object> getFolderList(@ToolParamBean GetFolderListParams params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();

            GetFolderListParam getFolderListParam = new GetFolderListParam();
            getFolderListParam.setFolderId(params.getFolderurl());
            getFolderListParam.setSort(params.getSort());

            JSONObject jsonObject = joyspaceService.getFolderList(getFolderListParam);
            if(jsonObject != null){
                result.put("success", true);
                result.put("jsonData", jsonObject.toString());
            }else{
                result.put("success", false);
                result.put("error", "获取文件夹列表失败，返回的json为空对象");
            }

            return result;
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return error;
        }
    }

    

    @Tool(name = "getFileList", description = "获取指定文件夹下的文件列表")
    public Map<String, Object> getFileList(@ToolParamBean GetFileListParams params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();

            GetFileListParam getFileListParam = new GetFileListParam();
            getFileListParam.setFolderId(params.getFolderurl());
            getFileListParam.setSort(params.getSort());
            getFileListParam.setStart(NumberUtils.toInteger(params.getStart(), 0));
            getFileListParam.setLength(NumberUtils.toInteger(params.getLength(), 100));
            List<Integer> pageTypes = new ArrayList<>();
            pageTypes.add(PAGE_TYPE_OF_NEW_DOCUMENT);//只过滤出文档类型的页面
            getFileListParam.setPageTypes(pageTypes);

            JSONObject jsonObject = joyspaceService.getFileList(getFileListParam);
            if(jsonObject != null){
                result.put("success", true);
                result.put("jsonData", jsonObject.toString());
            }else{
                result.put("success", false);
                result.put("error", "获取文件夹列表失败，返回的json为空对象");
            }

            return result;
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return error;
        }
    }

    

    @Tool(name = "getPageInfo", description = "获取指定文件夹下的页面信息")
    public Map<String, Object> getPageInfo(@ToolParamBean GetPageInfoParams params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();

            GetPageInfosParam getPageInfosParam = new GetPageInfosParam();
            List<String> pageIds = new ArrayList<>();
            pageIds.add(params.getPageurl());
            getPageInfosParam.setPageIds(pageIds);
            JSONObject jsonObject = this.joyspaceService.getPageInfos(getPageInfosParam);
            if(jsonObject != null){
                result.put("success", true);
                result.put("jsonData", jsonObject.toString());
                
            }else{
                result.put("success", false);
                result.put("error", "获取文件夹列表失败，返回的json为空对象");
            }

            return result;
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return error;
        }
    }

    @Tool(name = "getPageContent", description = "获取指定页面内容")
    public Map<String, Object> getPageContent(@ToolParamBean GetPageContentParams params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);

            GetPageContentParam getPageContentParam = new GetPageContentParam();
            getPageContentParam.setPageId(params.getPageurl());
            JSONObject jsonObject = joyspaceService.getPageContent(getPageContentParam);
            //logger.info("获取指定页面内容: {}", jsonObject.toString());
            //result.put("jsonObject", jsonObject.toString());
            if(jsonObject != null && jsonObject.getInt("code") == 0 && jsonObject.has("data") 
                && !jsonObject.isNull("data") && jsonObject.getJSONArray("data").length() > 0){
                String markdownData = converter.convertJsonStringToMarkdown(jsonObject.getJSONArray("data").toString());
                result.put("markdownData", markdownData);
                logger.info("获取指定页面内容转换后的markdown: {}",  markdownData);
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取指定页面内容失败: {}", e.getMessage());
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", e.getMessage());
            return error;
        }
    }

    @Data
    public static class GetFolderListParams {
        @ToolParam(name = "folderurl", description = "文件夹url", required = true)
        private String folderurl;
        @ToolParam(name = "sort", description = "排序字段，按什么排序，默认offset 升序updated_at 更新时间升序-updated_at 更新时间降序", required = false)
        private String sort;


        public void validateSelf() {
            if (folderurl == null || folderurl.trim().isEmpty()) {
                throw new IllegalArgumentException("folderurl 不能为空");
            }
        }
    }

    @Data
    public static class GetFileListParams {
        @ToolParam(name = "folderurl", description = "文件夹url", required = true)
        private String folderurl;
        @ToolParam(name = "sort", description = "排序字段，按什么排序，默认offset 升序updated_at 更新时间升序-updated_at 更新时间降序", required = false)
        private String sort;
        @ToolParam(name = "start", description = "起始位置", required = false)
        private String start;
        @ToolParam(name = "length", description = "长度", required = false)
        private String length;


        public void validateSelf() {
            if (folderurl == null || folderurl.trim().isEmpty()) {
                throw new IllegalArgumentException("folderurl 不能为空");
            }
        }
    }

    @Data
    public static class GetPageInfoParams {
        @ToolParam(name = "pageurl", description = "页面url", required = true)
        private String pageurl;


        public void validateSelf() {
            if (pageurl == null || pageurl.trim().isEmpty()) {
                throw new IllegalArgumentException("pageurl 不能为空");
            }
        }
    }

    @Data
    public static class GetPageContentParams {
        @ToolParam(name = "pageurl", description = "页面url", required = true)
        private String pageurl;


        public void validateSelf() {
            if (pageurl == null || pageurl.trim().isEmpty()) {
                throw new IllegalArgumentException("pageurl 不能为空");
            }
        }
    }

} 