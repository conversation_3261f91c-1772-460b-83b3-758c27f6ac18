package com.jdl.mcp.server.openme.joywork.param;

public class JoyworkUpdateTaskParam {
    private String taskId;
    private String openUserId;
    private String title;
    private String remark;
    private Long startTime;
    private Long endTime;
    private Integer priorityType;
    private String content;
    private String mobileContent;
    private String extend;
    private String sysProjectId;
    private String sysGroupId;
    private String sourceDescZh;
    private String sourceDescEn;
    private Integer sourceTrusteeship;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getOpenUserId() {
        return openUserId;
    }

    public void setOpenUserId(String openUserId) {
        this.openUserId = openUserId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Integer getPriorityType() {
        return priorityType;
    }

    public void setPriorityType(Integer priorityType) {
        this.priorityType = priorityType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMobileContent() {
        return mobileContent;
    }

    public void setMobileContent(String mobileContent) {
        this.mobileContent = mobileContent;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public String getSysProjectId() {
        return sysProjectId;
    }

    public void setSysProjectId(String sysProjectId) {
        this.sysProjectId = sysProjectId;
    }

    public String getSysGroupId() {
        return sysGroupId;
    }

    public void setSysGroupId(String sysGroupId) {
        this.sysGroupId = sysGroupId;
    }

    public String getSourceDescZh() {
        return sourceDescZh;
    }

    public void setSourceDescZh(String sourceDescZh) {
        this.sourceDescZh = sourceDescZh;
    }

    public String getSourceDescEn() {
        return sourceDescEn;
    }

    public void setSourceDescEn(String sourceDescEn) {
        this.sourceDescEn = sourceDescEn;
    }

    public Integer getSourceTrusteeship() {
        return sourceTrusteeship;
    }

    public void setSourceTrusteeship(Integer sourceTrusteeship) {
        this.sourceTrusteeship = sourceTrusteeship;
    }
}