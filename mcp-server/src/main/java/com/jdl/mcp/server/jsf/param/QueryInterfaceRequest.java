package com.jdl.mcp.server.jsf.param;

/**
 * 查询接口请求参数类
 * 
 * <AUTHOR> Assistant
 */
public class QueryInterfaceRequest extends BaseJsfRequest {
    
    /**
     * 接口名称，必填
     */
    private String interfaceName;
    
    public QueryInterfaceRequest() {
        super();
    }
    
    public QueryInterfaceRequest(String appKey, String operator, String clientIp, Long timeStamp, String sign, String interfaceName) {
        super(appKey, operator, clientIp, timeStamp, sign);
        this.interfaceName = interfaceName;
    }
    
    public String getInterfaceName() {
        return interfaceName;
    }
    
    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
    }
    
    @Override
    public String toString() {
        return "QueryInterfaceRequest{" +
                "interfaceName='" + interfaceName + '\'' +
                ", " + super.toString() +
                '}';
    }
}
