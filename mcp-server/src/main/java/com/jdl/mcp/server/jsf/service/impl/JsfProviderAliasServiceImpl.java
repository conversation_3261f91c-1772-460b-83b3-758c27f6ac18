package com.jdl.mcp.server.jsf.service.impl;

import com.jd.jsf.open.api.ProviderAliaService;
import com.jd.jsf.open.api.vo.request.QueryInterfaceRequest;
import com.jd.jsf.open.api.vo.Result;
import com.jdl.mcp.server.jsf.auth.JsfAuthService;
import com.jdl.mcp.server.jsf.exception.JsfErrorCode;
import com.jdl.mcp.server.jsf.exception.JsfException;
import com.jdl.mcp.server.jsf.model.JsfResult;
import com.jdl.mcp.server.jsf.param.BaseJsfRequest;
import com.jdl.mcp.server.jsf.service.JsfProviderAliasService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * JSF别名服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Service
public class JsfProviderAliasServiceImpl implements JsfProviderAliasService {
    
    private static final Logger log = LoggerFactory.getLogger(JsfProviderAliasServiceImpl.class);
    
    @Autowired
    private JsfAuthService jsfAuthService;
    
    // 注入JSF别名服务，需要在Spring配置中配置consumer
    @Autowired(required = false)
    private ProviderAliaService providerAliaService;
    
    @Override
    public JsfResult<List<String>> getAliasByInterfaceName(String interfaceName, String operator) {
        if (interfaceName == null || interfaceName.trim().isEmpty()) {
            throw new JsfException(JsfErrorCode.PARAM_ERROR, "interfaceName cannot be null or empty");
        }
        
        if (operator == null || operator.trim().isEmpty()) {
            throw new JsfException(JsfErrorCode.PARAM_ERROR, "operator cannot be null or empty");
        }
        
        try {
            // 构建通用认证参数
            BaseJsfRequest commonParams = jsfAuthService.buildCommonParams(operator);
            
            // 创建JSF API请求对象
            QueryInterfaceRequest request = new QueryInterfaceRequest();
            request.setAppKey(commonParams.getAppKey());
            request.setOperator(commonParams.getOperator());
            request.setClientIp(commonParams.getClientIp());
            request.setTimeStamp(commonParams.getTimeStamp());
            request.setSign(commonParams.getSign());
            request.setInterfaceName(interfaceName);
            
            log.info("Calling JSF API getAliasByInterfaceName with request: {}", request);
            
            // 调用JSF接口
            if (providerAliaService == null) {
                throw new JsfException(JsfErrorCode.CONFIG_ERROR, "ProviderAliaService not configured");
            }
            
            Result<List<String>> result = providerAliaService.getAliasByInterfaceName(request);
            log.info("JSF API getAliasByInterfaceName response: {}", result);
            
            // 转换结果
            JsfResult<List<String>> jsfResult = new JsfResult<>();
            jsfResult.setCode(result.getCode());
            jsfResult.setMsg(result.getMsg());
            jsfResult.setSuccess(result.isSuccess());
            jsfResult.setData(result.getData());
            jsfResult.setTotal(result.getTotal());
            
            return jsfResult;
        } catch (JsfException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to get alias by interface name: " + interfaceName, e);
            throw new JsfException(JsfErrorCode.SERVER_ERROR, "Failed to get alias by interface name", e);
        }
    }
}
