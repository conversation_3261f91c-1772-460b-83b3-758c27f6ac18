package com.jdl.mcp.server.openme.joyspace;

import com.jdl.mcp.server.openme.joyspace.param.GetFolderListParam;
import com.jdl.mcp.server.openme.joyspace.param.GetFileListParam;
import com.jdl.mcp.server.openme.joyspace.param.GetPageInfosParam;
import com.jdl.mcp.server.openme.joyspace.param.GetPageContentParam;
import com.jdl.mcp.server.openme.joyspace.param.GetLocationParam;
import com.jdl.mcp.server.openme.joyspace.param.GetAttachmentPresignUrlParam;
import org.json.JSONObject;

public interface JoyspaceService {
    JSONObject getFolderList(GetFolderListParam param);
    JSONObject getFileList(GetFileListParam param);
    JSONObject getPageInfos(GetPageInfosParam param);
    JSONObject getPageContent(GetPageContentParam param);
    JSONObject getLocation(GetLocationParam param);
    JSONObject getAttachmentPresignUrl(GetAttachmentPresignUrlParam param);
}
