package com.jdl.mcp.server.openme.joyspace.param;

import java.util.List;

public class GetFileListParam {
    private String folderId;
    private List<Creator> creators;
    private List<Integer> pageTypes;
    private String sort;
    private int start;
    private int length = 100;

    public String getFolderId() {
        return folderId;
    }

    public void setFolderId(String folderId) {
        this.folderId = folderId;
    }

    public List<Creator> getCreators() {
        return creators;
    }

    public void setCreators(List<Creator> creators) {
        this.creators = creators;
    }

    public List<Integer> getPageTypes() {
        return pageTypes;
    }

    public void setPageTypes(List<Integer> pageTypes) {
        this.pageTypes = pageTypes;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public static class Creator {
        private String erp;
        private String tenantCode;
        private String orgCode;
        private int userType;

        // Getters and setters

        public String getErp() {
            return erp;
        }

        public void setErp(String erp) {
            this.erp = erp;
        }

        public String getTenantCode() {
            return tenantCode;
        }

        public void setTenantCode(String tenantCode) {
            this.tenantCode = tenantCode;
        }

        public String getOrgCode() {
            return orgCode;
        }

        public void setOrgCode(String orgCode) {
            this.orgCode = orgCode;
        }

        public int getUserType() {
            return userType;
        }

        public void setUserType(int userType) {
            this.userType = userType;
        }
    }
}
