package com.jdl.mcp.server.openme.global;

public class OpenMeException extends RuntimeException {
    private final OpenMeErrorCode errorCode;

    public OpenMeException(OpenMeErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public OpenMeException(OpenMeErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public OpenMeException(OpenMeErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public OpenMeErrorCode getErrorCode() {
        return errorCode;
    }
}
