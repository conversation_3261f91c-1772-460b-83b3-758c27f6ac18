package com.jdl.mcp.server.openme.timline;

import com.jdl.mcp.server.openme.timline.param.SendJUEMsgParam;
import com.jdl.mcp.server.openme.timline.param.SendRobotMsgParam;
import org.json.JSONObject;

/**
 * 时间线服务接口
 * 提供发送机器人消息等功能
 */
public interface TimlineService {
    /**
     * 发送机器人消息
     * @param param 发送机器人消息的参数
     * @return 响应结果
     */
    JSONObject sendRobotMsg(SendRobotMsgParam param);

    /**
     * 发送互动卡片消息
     * @param param 发送互动卡片消息的参数
     * @return 响应结果
     */
    JSONObject sendJUEMsg(SendJUEMsgParam param);
}
