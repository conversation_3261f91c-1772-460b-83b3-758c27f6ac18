package com.jdl.mcp.solon.server;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jdl.mcp.core.service.enmus.Role;
import com.jdl.mcp.solon.chat.tool.McpFunctionTool;
import io.modelcontextprotocol.server.McpServer;
import io.modelcontextprotocol.server.McpServerFeatures;
import io.modelcontextprotocol.server.McpSyncServer;
import io.modelcontextprotocol.server.transport.WebRxSseServerTransportProvider;
import io.modelcontextprotocol.spec.McpSchema;
import lombok.extern.slf4j.Slf4j;
import org.noear.snack.ONode;
import org.noear.solon.Solon;
import org.noear.solon.ai.chat.tool.FunctionTool;
import org.noear.solon.ai.chat.tool.ToolProvider;
import org.noear.solon.ai.mcp.server.McpServerProperties;
import org.noear.solon.core.Props;
import org.noear.solon.core.bean.LifecycleBean;
import org.noear.solon.core.util.PathUtil;
import org.noear.solon.core.util.RunUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

@Slf4j
public class McpServerEndpointProvider implements LifecycleBean {
    private final WebRxSseServerTransportProvider mcpTransportProvider;
    private final McpServer.SyncSpecification mcpServerSpec;
    private final McpServerProperties serverProperties;
    private final String sseEndpoint;
    private final String messageEndpoint;

    public McpServerEndpointProvider(Properties properties) {
        this(Props.from(properties).bindTo(new McpServerProperties()));
    }

    public McpServerEndpointProvider(McpServerProperties serverProperties) {
        this.serverProperties = serverProperties;
        this.sseEndpoint = serverProperties.getSseEndpoint();
        this.messageEndpoint = PathUtil.mergePath(this.sseEndpoint, "message");

        //如果启用了
        this.mcpTransportProvider = WebRxSseServerTransportProvider.builder()
                .messageEndpoint(this.messageEndpoint)
                .sseEndpoint(this.sseEndpoint)
                .objectMapper(new ObjectMapper())
                .build();

        this.mcpServerSpec = McpServer.sync(this.mcpTransportProvider)
                .serverInfo(serverProperties.getName(), serverProperties.getVersion());

    }

    /**
     * 名字
     */
    public String getName() {
        return serverProperties.getName();
    }

    /**
     * 端点
     */
    public String getSseEndpoint() {
        return sseEndpoint;
    }

    /**
     * 获取传输提供者
     */
    public WebRxSseServerTransportProvider getTransport() {
        return mcpTransportProvider;
    }

    /**
     * 登记工具
     */
    public void addTool(FunctionTool functionTool) {
        addToolSpec(functionTool);
    }

    /**
     * 登记工具
     */
    public void addTool(ToolProvider toolProvider) {
        for (FunctionTool functionTool : toolProvider.getTools()) {
            addToolSpec(functionTool);
        }
    }

    /**
     * 移除工具
     */
    public void removeTool(String toolName) {
        if (server != null) {
            server.removeTool(toolName);
        }
    }

    /**
     * 移除工具
     */
    public void removeTool(ToolProvider toolProvider) {
        if (server != null) {
            for (FunctionTool functionTool : toolProvider.getTools()) {
                server.removeTool(functionTool.name());
            }
        }
    }

    /**
     * 通知工具变化
     */
    public void notifyToolsListChanged() {
        if (server != null) {
            server.notifyToolsListChanged();
        }
    }

    private McpSyncServer server;

    @Override
    public void start() throws Throwable {
        mcpTransportProvider.toHttpHandler(Solon.app());
    }


    @Override
    public void postStart() throws Throwable {
        server = mcpServerSpec.build();

        log.info("Mcp-Server started, name={}, version={}, sseEndpoint={}, messageEndpoint={}, toolRegistered={}",
                serverProperties.getName(),
                serverProperties.getVersion(),
                this.sseEndpoint,
                this.messageEndpoint,
                toolCount);

        if (serverProperties.getHeartbeatInterval() != null
                && serverProperties.getHeartbeatInterval().getSeconds() > 0) {
            //启用 sse 心跳（保持客户端不断开）
            RunUtil.delayAndRepeat(() -> {
                RunUtil.runAndTry(() -> {
                    mcpTransportProvider.sendHeartbeat();
                });
            }, serverProperties.getHeartbeatInterval().toMillis());
        }
    }

    @Override
    public void stop() throws Throwable {
        if (server != null) {
            server.close();
        }
    }

    private int toolCount = 0;

    protected void addToolSpec(FunctionTool functionTool) {
        ONode jsonSchema = buildJsonSchema(functionTool);
        String jsonSchemaStr = jsonSchema.toJson();

        McpServerFeatures.SyncToolSpecification toolSpec = new McpServerFeatures.SyncToolSpecification(
                new McpSchema.Tool(functionTool.name(), functionTool.description(), jsonSchemaStr),
                (exchange, request) -> {
                    McpSchema.CallToolResult toolResult = null;
                    try {
                        String rst = functionTool.handle(request);
                        List<McpSchema.Role> audience = null;
                        if (functionTool instanceof McpFunctionTool) {
                            Role[] roles = ((McpFunctionTool) functionTool).audience();
                            if (roles != null && roles.length > 0) {
                                audience = new ArrayList<>();
                                for (Role role : roles) {
                                    audience.add(McpSchema.Role.valueOf(role.name()));
                                }
                            }
                        }
                        toolResult = new McpSchema.CallToolResult(Arrays.asList(new McpSchema.TextContent(audience, null, rst)), false);
                    } catch (Throwable ex) {
                        toolResult = new McpSchema.CallToolResult(Arrays.asList(new McpSchema.TextContent(ex.getMessage())), true);
                    }

                    return toolResult;
                });

        toolCount++;
        if (server != null) {
            server.addTool(toolSpec);
        } else {
            mcpServerSpec.tools(toolSpec);
        }
    }

    protected ONode buildJsonSchema(FunctionTool functionTool) {
        ONode jsonSchema = new ONode();
        jsonSchema.set("$schema", "http://json-schema.org/draft-07/schema#");
        jsonSchema.setAll(functionTool.inputSchema());

        return jsonSchema;
    }

    public void addPrompt() {
        McpSchema.Prompt prompt = new McpSchema.Prompt("test-prompt", "Joy space 需求文档分析", new ArrayList<>());
        McpServerFeatures.SyncPromptSpecification specification = new McpServerFeatures.SyncPromptSpecification(prompt,
                (exchange, req) -> {
                    return new McpSchema.GetPromptResult("Test prompt description", Arrays
                            .asList(new McpSchema.PromptMessage(McpSchema.Role.ASSISTANT, new McpSchema.TextContent("Test content"))));
                }

        );
        this.mcpServerSpec.prompts(specification);

        McpSchema.Resource resource = new McpSchema.Resource("test://resource", "Test Resource", "Test resource description", "text/plain",
                null);
        McpServerFeatures.SyncResourceSpecification specification1 = new McpServerFeatures.SyncResourceSpecification(
                resource, (exchange, req) -> {
            return new McpSchema.ReadResourceResult(new ArrayList<>());
        });

        mcpServerSpec.resources(specification1);
    }
}
