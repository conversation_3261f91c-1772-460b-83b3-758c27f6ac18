package com.jdl.mcp.generator.template;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class TemplateEngine {
    private static final Logger logger = LoggerFactory.getLogger(TemplateEngine.class);
    private final Configuration configuration;

    public TemplateEngine() {
        configuration = new Configuration(Configuration.VERSION_2_3_31);
        try {
            configuration.setClassLoaderForTemplateLoading(getClass().getClassLoader(), "templates");
            configuration.setDefaultEncoding("UTF-8");
            configuration.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        } catch (Exception e) {
            logger.error("无法初始化模板引擎", e);
            throw new RuntimeException("无法初始化模板引擎", e);
        }
    }

    public void processTemplate(String templateName, Object dataModel, String outputPath) throws IOException, TemplateException {
        Template template = configuration.getTemplate(templateName);
        
        // 确保输出目录存在
        Path outputDir = Paths.get(outputPath).getParent();
        if (outputDir != null) {
            Files.createDirectories(outputDir);
        }

        try (Writer writer = new FileWriter(outputPath)) {
            template.process(dataModel, writer);
        }
    }

    public String processTemplateToString(String templateName, Object dataModel) throws IOException, TemplateException {
        Template template = configuration.getTemplate(templateName);
        try (Writer writer = new StringWriter()) {
            template.process(dataModel, writer);
            return writer.toString();
        }
    }
} 