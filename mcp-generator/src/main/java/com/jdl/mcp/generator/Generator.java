package com.jdl.mcp.generator;

import com.jdl.mcp.generator.config.ConfigLoader;
import com.jdl.mcp.generator.model.ServiceConfig;
import com.jdl.mcp.generator.template.TemplateEngine;
import com.jdl.mcp.generator.utils.NameConverter;
import freemarker.template.TemplateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class Generator {
    private static final Logger logger = LoggerFactory.getLogger(Generator.class);
    private final ConfigLoader configLoader;
    private final TemplateEngine templateEngine;

    private boolean force = false;
    private boolean dryRun = false;
    private boolean verbose = false;
    private List<String> generatedFiles = new ArrayList<>();

    // 固定的输出目录
    private static final String MCP_SERVER_BASE = "mcp-server";
    private static final String MCP_ADMIN_BASE = "mcp-admin";
    private static final String SERVER_SOURCE_PATH = "src/main/java/com/jdl/mcp/server/servers";
    private static final String SERVER_TEST_PATH = "src/test/java/com/jdl/mcp/server/servers";
    private static final String ADMIN_RESOURCES_PATH = "src/main/resources";
    private static final String ADMIN_DOCS_PATH = "src/main/resources/static/docs/servers";

    public Generator() {
        this.configLoader = new ConfigLoader();
        this.templateEngine = new TemplateEngine();
    }

    public void setForce(boolean force) {
        this.force = force;
    }

    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }

    public void setVerbose(boolean verbose) {
        this.verbose = verbose;
    }

    public List<String> getGeneratedFiles() {
        return generatedFiles;
    }

    public void generate(String configPath) throws IOException, TemplateException {
        logger.info("开始生成代码，配置文件：{}", configPath);
        if (verbose) {
            logger.info("选项：force={}, dryRun={}", force, dryRun);
        }

        // 加载配置
        ServiceConfig config = configLoader.loadConfig(configPath);
        if (config == null || config.getServer() == null) {
            throw new IllegalArgumentException("配置文件格式错误或缺少必要信息");
        }

        String serviceId = config.getServer().getId();
        logger.info("服务ID：{}", serviceId);

        try {
            // 创建必要的目录
            createDirectories(serviceId);

            // 生成业务代码
            generateMcpTool(config, serviceId);

            // 生成配置文件
            generateProperties(config, serviceId);

            // 生成文档
            generateMarkdown(config, serviceId);

            // 生成测试类
            generateTest(config, serviceId);

            logger.info("代码生成完成！");
            if (verbose) {
                logger.info("生成的文件列表：");
                generatedFiles.forEach(file -> logger.info("- {}", file));
            }
        } catch (Exception e) {
            logger.error("代码生成失败", e);
            throw e;
        }
    }

    private void createDirectories(String serviceId) throws IOException {
        if (dryRun) {
            logger.info("预览模式：将创建以下目录");
            logger.info("- {}/{}/{}", MCP_SERVER_BASE, SERVER_SOURCE_PATH, serviceId);
            logger.info("- {}/{}/servers", MCP_ADMIN_BASE, ADMIN_RESOURCES_PATH);
            logger.info("- {}/{}", MCP_ADMIN_BASE, ADMIN_DOCS_PATH);
            logger.info("- {}/{}/{}", MCP_SERVER_BASE, SERVER_TEST_PATH, serviceId);
            return;
        }

        // 创建业务代码目录
        Path mcpToolPath = Paths.get(MCP_SERVER_BASE, SERVER_SOURCE_PATH, serviceId);
        Files.createDirectories(mcpToolPath);
        logger.info("创建业务代码目录: {}", mcpToolPath);

        // 创建配置文件目录
        Path propertiesPath = Paths.get(MCP_ADMIN_BASE, ADMIN_RESOURCES_PATH, "servers");
        Files.createDirectories(propertiesPath);
        logger.info("创建配置文件目录: {}", propertiesPath);

        // 创建文档目录
        Path docsPath = Paths.get(MCP_ADMIN_BASE, ADMIN_DOCS_PATH);
        Files.createDirectories(docsPath);
        logger.info("创建文档目录: {}", docsPath);

        // 创建测试类目录
        Path testPath = Paths.get(MCP_SERVER_BASE, SERVER_TEST_PATH, serviceId);
        Files.createDirectories(testPath);
        logger.info("创建测试类目录: {}", testPath);
    }

    private void generateMcpTool(ServiceConfig config, String serviceId) throws IOException, TemplateException {
        String className = NameConverter.toPascalCase(serviceId) + "McpTool";
        String outputPath = Paths.get(MCP_SERVER_BASE, SERVER_SOURCE_PATH,
            serviceId, className + ".java").toString();

        // 检查文件是否已存在
        if (!force && !dryRun && Files.exists(Paths.get(outputPath))) {
            throw new IOException("文件已存在：" + outputPath + "。使用 --force 选项强制覆盖。");
        }

        // 处理方法名称
        if (config.getMethods() != null) {
            config.getMethods().forEach(method -> {
                // 处理方法名称
                String methodName = method.getName();
                method.setName(NameConverter.convert(methodName, false));

                // 处理参数名称，保留驼峰命名法
                if (method.getParams() != null) {
                    method.getParams().forEach(param -> {
                        String paramName = param.getName();
                        // 如果参数名已经是驼峰命名法，则保持不变
                        if (paramName.contains("_")) {
                            param.setName(NameConverter.convert(paramName, false));
                        }
                    });
                }
            });
        }

        if (!dryRun) {
            templateEngine.processTemplate("mcp-tool.ftl", config, outputPath);
            generatedFiles.add(outputPath);
        }
        logger.info("{}生成业务代码: {}", dryRun ? "预览：" : "", outputPath);
    }

    private void generateProperties(ServiceConfig config, String serviceId) throws IOException, TemplateException {
        String outputPath = Paths.get(MCP_ADMIN_BASE, ADMIN_RESOURCES_PATH, "servers",
            serviceId + ".properties").toString();

        // 检查文件是否已存在
        if (!force && !dryRun && Files.exists(Paths.get(outputPath))) {
            throw new IOException("文件已存在：" + outputPath + "。使用 --force 选项强制覆盖。");
        }

        if (!dryRun) {
            templateEngine.processTemplate("properties.ftl", config, outputPath);
            generatedFiles.add(outputPath);
        }
        logger.info("{}生成配置文件: {}", dryRun ? "预览：" : "", outputPath);
    }

    private void generateMarkdown(ServiceConfig config, String serviceId) throws IOException, TemplateException {
        String outputPath = Paths.get(MCP_ADMIN_BASE, ADMIN_DOCS_PATH,
            serviceId + ".md").toString();

        // 检查文件是否已存在
        if (!force && !dryRun && Files.exists(Paths.get(outputPath))) {
            throw new IOException("文件已存在：" + outputPath + "。使用 --force 选项强制覆盖。");
        }

        if (!dryRun) {
            templateEngine.processTemplate("markdown.ftl", config, outputPath);
            generatedFiles.add(outputPath);
        }
        logger.info("{}生成文档: {}", dryRun ? "预览：" : "", outputPath);
    }

    private void generateTest(ServiceConfig config, String serviceId) throws IOException, TemplateException {
        String className = NameConverter.toPascalCase(serviceId) + "McpTool";
        String outputPath = Paths.get(MCP_SERVER_BASE, SERVER_TEST_PATH,
            serviceId, className + "Test.java").toString();

        // 检查文件是否已存在
        if (!force && !dryRun && Files.exists(Paths.get(outputPath))) {
            throw new IOException("文件已存在：" + outputPath + "。使用 --force 选项强制覆盖。");
        }

        if (!dryRun) {
            templateEngine.processTemplate("test.ftl", config, outputPath);
            generatedFiles.add(outputPath);
        }
        logger.info("{}生成测试类: {}", dryRun ? "预览：" : "", outputPath);
    }
}