package com.jdl.mcp.generator.cli;

import com.jdl.mcp.generator.Generator;
import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;

import java.io.File;
import java.util.concurrent.Callable;

@Command(
    name = "mcp-generator",
    version = "1.0",
    description = "MCP服务代码生成器",
    mixinStandardHelpOptions = true
)
public class GeneratorCommand implements Callable<Integer> {

    @Option(
        names = {"-c", "--config"},
        description = "配置文件路径",
        required = true
    )
    private File configFile;

    @Option(
        names = {"--force"},
        description = "强制覆盖已存在的文件"
    )
    private boolean force;

    @Option(
        names = {"--dry-run"},
        description = "预览将要生成的文件，但不实际生成"
    )
    private boolean dryRun;

    @Option(
        names = {"--verbose"},
        description = "显示详细的日志信息"
    )
    private boolean verbose;

    @Override
    public Integer call() throws Exception {
        // 验证配置文件
        if (!configFile.exists()) {
            System.err.println("错误：配置文件不存在：" + configFile);
            return 1;
        }

        try {
            Generator generator = new Generator();
            
            // 设置生成器选项
            generator.setForce(force);
            generator.setDryRun(dryRun);
            generator.setVerbose(verbose);

            // 生成代码
            generator.generate(configFile.getPath());

            // 如果是详细模式，输出生成的文件列表
            if (verbose) {
                System.out.println("\n生成的文件列表：");
                generator.getGeneratedFiles().forEach(file -> System.out.println("- " + file));
            }

            return 0;
        } catch (Exception e) {
            System.err.println("错误：生成代码失败");
            if (verbose) {
                e.printStackTrace();
            } else {
                System.err.println(e.getMessage());
            }
            return 1;
        }
    }

    public static void main(String[] args) {
        int exitCode = new CommandLine(new GeneratorCommand()).execute(args);
        System.exit(exitCode);
    }
} 