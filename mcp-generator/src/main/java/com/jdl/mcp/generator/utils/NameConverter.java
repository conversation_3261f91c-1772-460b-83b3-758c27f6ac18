package com.jdl.mcp.generator.utils;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 命名转换工具类
 */
public class NameConverter {

    /**
     * 下划线命名转驼峰命名
     * 例如：get_current_time -> getCurrentTime
     *
     * @param name 下划线命名
     * @return 驼峰命名
     */
    public static String toCamelCase(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }

        // 先全部转小写
        name = name.toLowerCase();

        // 按下划线分割
        String[] parts = name.split("_");
        
        // 第一个部分保持小写，其他部分首字母大写
        StringBuilder result = new StringBuilder(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            if (!parts[i].isEmpty()) {
                result.append(Character.toUpperCase(parts[i].charAt(0)))
                      .append(parts[i].substring(1));
            }
        }
        
        return result.toString();
    }

    /**
     * 下划线命名转帕斯卡命名（首字母大写的驼峰命名）
     * 例如：get_current_time -> GetCurrentTime
     *
     * @param name 下划线命名
     * @return 帕斯卡命名
     */
    public static String toPascalCase(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }

        String camelCase = toCamelCase(name);
        return Character.toUpperCase(camelCase.charAt(0)) + camelCase.substring(1);
    }

    /**
     * 首字母大写
     * 例如：getCurrentTime -> GetCurrentTime
     *
     * @param name 输入字符串
     * @return 首字母大写的字符串
     */
    public static String capitalize(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        return Character.toUpperCase(name.charAt(0)) + name.substring(1);
    }

    /**
     * 首字母小写
     * 例如：GetCurrentTime -> getCurrentTime
     *
     * @param name 输入字符串
     * @return 首字母小写的字符串
     */
    public static String uncapitalize(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        return Character.toLowerCase(name.charAt(0)) + name.substring(1);
    }

    /**
     * 处理特殊单词的大小写
     * 例如：timezone -> Timezone, format -> Format
     *
     * @param name 输入字符串
     * @return 处理后的字符串
     */
    public static String handleSpecialWords(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }

        // 定义需要特殊处理的单词及其对应的正确形式
        String[][] specialWords = {
            {"timezone", "Timezone"},
            {"format", "Format"}
        };

        String result = name;
        for (String[] pair : specialWords) {
            String pattern = "(?i)" + pair[0]; // 忽略大小写
            result = result.replaceAll(pattern, pair[1]);
        }

        return result;
    }

    /**
     * 组合处理：下划线转驼峰并处理特殊单词
     * 例如：get_current_timezone -> getCurrentTimezone
     *
     * @param name 输入字符串
     * @param capitalizeFirst 是否首字母大写
     * @return 处理后的字符串
     */
    public static String convert(String name, boolean capitalizeFirst) {
        if (name == null || name.isEmpty()) {
            return name;
        }

        String result = toCamelCase(name);
        result = handleSpecialWords(result);
        return capitalizeFirst ? capitalize(result) : result;
    }
} 