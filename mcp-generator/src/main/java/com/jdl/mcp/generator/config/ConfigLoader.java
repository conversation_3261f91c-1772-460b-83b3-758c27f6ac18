package com.jdl.mcp.generator.config;

import com.jdl.mcp.generator.model.ServiceConfig;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;
import org.yaml.snakeyaml.nodes.Node;
import org.yaml.snakeyaml.nodes.NodeId;
import org.yaml.snakeyaml.nodes.ScalarNode;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

public class ConfigLoader {
    private final Yaml yaml;

    public ConfigLoader() {
        Constructor constructor = new Constructor();
        constructor.getPropertyUtils().setSkipMissingProperties(true);
        this.yaml = new Yaml(constructor);
    }

    public ServiceConfig loadConfig(String configPath) throws IOException {
        try (InputStream inputStream = new FileInputStream(new File(configPath))) {
            return yaml.loadAs(inputStream, ServiceConfig.class);
        }
    }

    public ServiceConfig loadConfigFromResource(String resourcePath) throws IOException {
        try (InputStream inputStream = getClass().getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new IOException("Resource not found: " + resourcePath);
            }
            return yaml.loadAs(inputStream, ServiceConfig.class);
        }
    }
} 