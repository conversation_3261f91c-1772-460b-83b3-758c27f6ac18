server:
  # 服务ID，必须唯一，全部小写，不带分隔符
  id: time
  # 服务名称
  name: 时间服务
  # 服务描述
  description: 提供时间和时区转换服务
  # 服务版本
  version: 1.0.0
  # 服务类型，默认API
  type: API
  # 服务使用说明
  usage: 获取当前时间和在不同时区之间转换时间，支持时区转换

# 服务方法配置
methods:
  - name: get_current_time
    description: 获取当前时间，如果不指定时区则使用配置的默认时区
    params:
      - name: timezone
        type: string
        required: false
        description: 时区ID，如：Asia/Shanghai, America/New_York等
        example: Asia/Shanghai
    output:
      type: Map
      fields:
        - name: success
          type: Boolean
          description: 操作是否成功
        - name: timezone
          type: String
          description: 时区ID
        - name: datetime
          type: String
          description: 完整的日期时间
        - name: date
          type: String
          description: 日期部分
        - name: time
          type: String
          description: 时间部分
        - name: timestamp
          type: Long
          description: 时间戳，单位：毫秒
        - name: is_dst
          type: Boolean
          description: 是否处于夏令时
        - name: zone_offset
          type: String
          description: 时区偏移量
        - name: error
          type: String
          description: 错误信息，仅当success为false时返回

  - name: convert_time
    description: 在不同时区之间转换时间
    params:
      - name: source_timezone
        type: string
        required: true
        description: 源时区ID，如：Asia/Shanghai
        example: Asia/Shanghai
      - name: target_timezone
        type: string
        required: true
        description: 目标时区ID，如：America/New_York
        example: America/New_York
      - name: time
        type: string
        required: true
        description: 要转换的时间，格式为HH:mm
        example: 14:30
    output:
      type: Map
      fields:
        - name: success
          type: Boolean
          description: 操作是否成功
        - name: source
          type: Map
          description: 源时间信息
        - name: target
          type: Map
          description: 目标时间信息
        - name: time_difference
          type: String
          description: 时差
        - name: error
          type: String
          description: 错误信息，仅当success为false时返回

# 业务配置
business:
  # 是否启用业务配置，默认为true
  enabled: true

  # 配置项列表，执行业务逻辑时需要读取的配置，在servers/time.properties中配置的内容
  properties:
    - key: "time.default_timezone"
      value: "Asia/Shanghai"
      description: "默认时区"
    - key: "time.use_24hour_format"
      value: "true"
      description: "是否使用24小时制"
    - key: "time.date_format"
      value: "yyyy-MM-dd"
      description: "日期格式"
    - key: "time.time_format"
      value: "HH:mm:ss"
      description: "时间格式"
    - key: "time.datetime_format"
      value: "yyyy-MM-dd'T'HH:mm:ssXXX"
      description: "日期时间格式"