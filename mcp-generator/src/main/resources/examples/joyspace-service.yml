server:
  # 服务ID，必须唯一，全部小写，不带分隔符
  id: joyspace
  # 服务名称
  name: 读取JoySpace服务
  # 服务描述
  description: 读取JoySpace服务，支持读取子文件夹列表、文件列表、页面信息、页面内容
  # 服务版本
  version: 1.0.0
  # 服务类型，默认API
  type: API
  # 服务使用说明
  usage: 读取JoySpace服务，支持读取子文件夹列表（get_folder_list）、文件列表（get_file_list）、页面信息（get_page_infos）、页面内容（get_page_content）

# 服务方法配置
methods:
  - name: get_folder_list
    description: 获取指定文件夹下的子文件夹列表
    params:
      - name: folderUrl
        type: String
        required: true
        description: 文件夹url
        example: https://joyspace.jd.com/teams/b10vXr-LbPQ60n1pb7D3/0q9HlwuJQu5zYMKYQ5Hx
      # - name: creators
      #   type: List
      #   required: false
      #   description: 文件夹创建人
      #   example: [{"erp":"xxx","tenantCode":"xxx","orgCode": "xxx","userType": 1}]
      - name: sort
        type: String
        required: false
        description: 排序字段，按什么排序，默认offset 升序updated_at 更新时间升序-updated_at 更新时间降序
        example: updated_at
    output:
      type: Map
      fields:
        - name: code
          type: String
          description: 错误码，非0取值表示失败
        - name: msg
          type: String
          description: 错误信息
        - name: jsonData
          type: String
          description: 完整的原始json数据
        - name: markdownData
          type: String
          description: 转换后的markdown数据

  - name: get_file_list
    description: 获取指定文件夹下的文件列表
    params:
      - name: folderUrl
        type: String
        required: true
        description: 文件夹url
        example: https://joyspace.jd.com/teams/b10vXr-LbPQ60n1pb7D3/0q9HlwuJQu5zYMKYQ5Hx
      # - name: creators
      #   type: List
      #   required: false
      #   description: 文件夹创建人
      #   example: [{"erp":"xxx","tenantCode":"xxx","orgCode": "xxx","userType": 1}]
      # - name: pageTypes
      #   type: List
      #   required: false
      #   description: 要筛选的文档类型集合，13-新文档10-表格
      #   example: [13,10]
      - name: sort
        type: String
        required: false
        description: 排序字段，按什么排序，默认offset 升序updated_at 更新时间升序-updated_at 更新时间降序
        example: updated_at
      - name: start
        type: int
        required: false
        description: 起始位置
        example: 0
      - name: length
        type: int
        required: false
        description: 长度
        example: 10
    output:
      type: Map
      fields:
        - name: code
          type: String
          description: 错误码，非0取值表示失败
        - name: msg
          type: String
          description: 错误信息
        - name: jsonData
          type: String
          description: 完整的原始json数据
        - name: markdownData
          type: String
          description: 转换后的markdown数据

  - name: get_page_info
    description: 获取指定文件夹下的页面信息
    params:
      - name: pageUrl
        type: String
        required: true
        description: 页面url
        example: "https://joyspace.jd.com/page/DPgHDxqCMEYdGtwwQ9ju"
    output:
      type: Map
      fields:
        - name: code
          type: String
          description: 错误码，非0取值表示失败
        - name: msg
          type: String
          description: 错误信息
        - name: jsonData
          type: String
          description: 完整的原始json数据
        - name: markdownData  
          type: String
          description: 转换后的markdown数据

  - name: get_page_content
    description: 获取指定页面内容
    params:
      - name: pageUrl
        type: String
        required: true
        description: 页面url
        example: "https://joyspace.jd.com/page/jKnRdFujF3nfmz9mLsd7u"
    output:
      type: Map   
      fields:
        - name: code
          type: String
          description: 错误码，非0取值表示失败
        - name: msg
          type: String
          description: 错误信息
        - name: jsonData
          type: String
          description: 完整的原始json数据
        - name: markdownData
          type: String
          description: 转换后的markdown数据
# 业务配置
business:
  # 是否启用业务配置，默认为true
  enabled: true
  # 配置项列表，执行业务逻辑时需要读取的配置，在servers/time.properties中配置的内容
  properties:
    - key: "joyspace.teamId"
      value: "b10vXr-LbPQ60n1pb7D3"
      description: "团队id"