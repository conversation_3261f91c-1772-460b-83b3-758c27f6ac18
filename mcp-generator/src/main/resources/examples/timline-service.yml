server:
  # 服务ID，必须唯一，全部小写，不带分隔符
  id: timline
  # 服务名称
  name: 机器人发消息服务
  # 服务描述
  description: 通过机器人发消息服务，当前默认使用物流交易小蜜机器人
  # 服务版本
  version: 1.0.0
  # 服务类型，默认API
  type: API
  # 服务使用说明
  usage: 通过机器人发消息服务，当前默认使用物流交易小蜜机器人

# 服务方法配置
methods:
  - name: send_message
    description: 发送普通机器人消息
    params:
      - name: receiver
        type: String
        required: true
        description: 接收人，可以是用户erp，也可以是群组id
        example: erp or groupId
      - name: receiverType
        type: int
        required: false
        description: 接收人类型，1-用户 2-群组
        example: 默认值=1
      - name: messageType
        type: int
        required: false
        description: 消息类型，普通消息类型为1，卡片消息类型为2
        example: 默认为普通消息=1
      - name: messageContent
        type: String
        required: true
        description: 消息内容，JSON格式
        example: 构造参数类后序列化获取
      - name: robotId
        type: String
        required: false
        description: 机器人id，默认为物流交易小蜜
        example: 00_55e779bf635b4f14
    output:
      type: Map
      fields:
        - name: success
          type: boolean
          description: 是否成功 true/false
        - name: result
          type: String
          description: 结果描述，成功时为空，失败时有具体错误信息
        - name: jsonData
          type: String
          description: 完整的接口返回原始json数据


# 业务配置
business:
  # 是否启用业务配置，默认为true
  enabled: true
  # 配置项列表，执行业务逻辑时需要读取的配置，在servers/timline.properties中配置的内容
  properties:
    - key: "timline_default_message_robot"
      value: "00_55e779bf635b4f14"
      description: "默认机器人id，物流交易小蜜机器人id"
    - key: "timline_default_message_type"
      value: "1"
      description: "默认消息类型，1-普通消息 2-卡片消息"
    - key: "timline_default_receiver_type"
      value: "1"
      description: "默认接收人类型，1-用户 2-群组"

#mvn spring-boot:run -Dspring-boot.run.arguments="-c src/main/resources/examples/timline-service.yml --force --verbose"