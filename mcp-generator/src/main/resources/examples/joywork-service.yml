server:
  # 服务ID，必须唯一，全部小写，不带分隔符
  id: joywork
  # 服务名称
  name: 待办服务
  # 服务描述
  description: 通过待办服务，管理待办任务
  # 服务版本
  version: 1.0.0
  # 服务类型，默认API
  type: API
  # 服务使用说明
  usage: 通过待办服务，创建待办任务，更新待办任务，更新待办任务状态，催办待办任务

# 服务方法配置
methods:
  - name: create_task
    description: 创建待办任务
    params:
      - name: title
        type: String
        required: true
        description: 任务标题
        example: 交易618备战-硬件扩容
      - name: remark
        type: String
        required: true
        description: 任务说明
        example: 请在5月10日廊坊第一次军演之前完成硬件扩容
      - name: startTime
        type: String
        required: true
        description: 任务开始时间
        example: '2025-05-10 10:00:00'
      - name: endTime
        type: String
        required: true
        description: 任务结束时间
        example: '2025-05-11 10:00:00'
      - name: executor
        type: String
        required: true
        description: 任务执行人ERP，多个用逗号分隔
        example: bjliandahu,wangwangang
    output:
      type: Map
      fields:
        - name: success
          type: boolean
          description: 是否成功 true/false
        - name: result
          type: String
          description: 结果描述，成功时为空，失败时有具体错误信息
        - name: taskId
          type: String
          description: 任务id
        - name: sourceId
          type: String
          description: 任务来源id，用于创建任务时的唯一标识

  - name: update_task
    description: 更新待办任务
    params:
      - name: taskId
        type: String
        required: true
        description: 任务id
        example: 1234567890
      - name: title
        type: String
        required: true
        description: 任务标题
        example: 交易618备战-硬件扩容
      - name: remark
        type: String
        required: true
        description: 任务说明
        example: 请在5月10日廊坊第一次军演之前完成硬件扩容  
      - name: startTime
        type: String
        required: true
        description: 任务开始时间
        example: '2025-05-10 10:00:00'
      - name: endTime
        type: String
        required: true
        description: 任务结束时间
        example: '2025-05-11 10:00:00'
    output:
      type: Map
      fields:
        - name: success
          type: boolean
          description: 是否成功 true/false
        - name: result
          type: String
          description: 结果描述，成功时为空，失败时有具体错误信息
        - name: data
          type: String
          description: 返回结果

  - name: update_task_status
    description: 更新待办任务状态
    params:
      - name: taskId
        type: String
        required: true
        description: 任务id
        example: 1234567890
      - name: taskStatus
        type: String
        required: true
        description: 任务状态，taskStatus = 1 未完成；taskStatus = 2 完成
        example: 1
    output:
      type: Map
      fields:
        - name: success
          type: boolean
          description: 是否成功 true/false
        - name: result
          type: String
          description: 结果描述，成功时为空，失败时有具体错误信息
        - name: data
          type: String
          description: 返回结果

  - name: urge_task
    description: 催办待办任务
    params:
      - name: taskId
        type: String
        required: true
        description: 任务id
        example: 1234567890
      - name: urgeContent
        type: String
        required: true
        description: 催办内容
        example: 请尽快完成任务
      - name: taskUsers
        type: String
        required: true
        description: 抄送人，多个用逗号分隔
        example: bjliandahu,wangwangang
    output:
      type: Map
      fields:
        - name: success
          type: boolean
          description: 是否成功 true/false
        - name: result
          type: String
          description: 结果描述，成功时为空，失败时有具体错误信息
        - name: data
          type: String
          description: 返回结果
# 业务配置
business:
  # 是否启用业务配置，默认为true
  enabled: true
  # 配置项列表，执行业务逻辑时需要读取的配置，在servers/timline.properties中配置的内容
  properties:
    - key: "joywork_content"
      value: "http://mcp.jdl.com"
      description: "pc端跳转链接"
    - key: "joywork_mobile_content"
      value: "https://mcp.jdl.com"
      description: "移动端跳转链接"
    - key: "joywork_sourceDescZh"
      value: "请前往MCP平台处理"
      description: "中文任务来源描述"
    - key: "joywork_sourceDescEn"
      value: "Please go to MCP platform to handle"
      description: "英文任务来源描述"
    - key: "joywork_sourceTrusteeship"
      value: "1"
      description: "是否能从joywork完成该任务，0否，1是"

#mvn spring-boot:run -Dspring-boot.run.arguments="-c src/main/resources/examples/joywork-service.yml --force --verbose"