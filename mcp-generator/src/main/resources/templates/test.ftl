package com.jdl.mcp.server.servers.${server.id};

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class ${server.id?cap_first}McpToolTest {
    private ${server.id?cap_first}McpTool tool;

    @BeforeEach
    void setUp() {
        tool = new ${server.id?cap_first}McpTool();
<#list business.properties as prop>
    <#-- 改进的属性名处理，确保使用正确的驼峰命名法并保留服务ID前缀 -->
    <#assign propName = prop.key>
    <#if propName?starts_with("${server.id}_")>
        <#assign propNameWithoutPrefix = propName?replace("${server.id}_", "")>
        <#assign parts = propNameWithoutPrefix?split("_")>
        <#assign camelCaseName = "${server.id}" + parts[0]?cap_first>
        <#list parts[1..] as part>
            <#assign camelCaseName = camelCaseName + part?cap_first>
        </#list>
    <#else>
        <#assign parts = propName?split("_")>
        <#assign camelCaseName = parts[0]>
        <#list parts[1..] as part>
            <#assign camelCaseName = camelCaseName + part?cap_first>
        </#list>
    </#if>
        ReflectionTestUtils.setField(tool, "${camelCaseName}", "${prop.value}");
</#list>
    }

<#list methods as method>
    @Test
    void test${method.name?cap_first?replace("_", "")}() {
        // 准备测试数据
        ${server.id?cap_first}McpTool.${method.name?cap_first?replace("_", "")}Params params = new ${server.id?cap_first}McpTool.${method.name?cap_first?replace("_", "")}Params();
<#list method.params as param>
<#if param.example??>
        params.set${param.name?cap_first?replace("_", "")}("${param.example}");
</#if>
</#list>

        // 执行测试
        Map<String, Object> result = tool.${method.name?replace("_", "")}(params);

        // 验证结果
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
<#list method.output.fields as field>
<#if field.name != "success" && field.name != "error">
        assertNotNull(result.get("${field.name}"));
</#if>
</#list>
    }

    @Test
    void test${method.name?cap_first?replace("_", "")}WithInvalidParams() {
        // 准备测试数据
        ${server.id?cap_first}McpTool.${method.name?cap_first?replace("_", "")}Params params = new ${server.id?cap_first}McpTool.${method.name?cap_first?replace("_", "")}Params();
<#list method.params as param>
<#if param.required>
        // 不设置必需参数 ${param.name}
</#if>
</#list>

        // 执行测试
        Map<String, Object> result = tool.${method.name?replace("_", "")}(params);

        // 验证结果
        assertNotNull(result);
        assertFalse((Boolean) result.get("success"));
        assertNotNull(result.get("error"));
    }

</#list>
}