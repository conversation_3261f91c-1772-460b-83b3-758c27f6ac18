package com.jdl.mcp.server.servers.${server.id};

import com.jdl.mcp.core.service.annotation.McpEndpoint;
import com.jdl.mcp.core.service.annotation.Tool;
import com.jdl.mcp.core.service.annotation.ToolParam;
import com.jdl.mcp.core.service.annotation.ToolParamBean;
import lombok.Data;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * ${server.description}
 * <p>
 * ${server.usage}
 * </p>
 */
@Component
@McpEndpoint(name = "${server.id}", description = "${server.description}")
public class ${server.id?cap_first}McpTool {
    private static final Logger logger = LoggerFactory.getLogger(${server.id?cap_first}McpTool.class);
    private static final String CONFIG_PATH = "servers/${server.id}.properties";

<#list business.properties as prop>
    <#if prop.key == "time.use_24hour_format">
    /** ${prop.description!'是否使用24小时制'} */
    private boolean use24HourFormat;
    <#else>
    <#-- 改进的属性名处理，确保使用正确的驼峰命名法并保留服务ID前缀 -->
    <#assign propName = prop.key>
    <#if propName?starts_with("${server.id}_")>
        <#assign propNameWithoutPrefix = propName?replace("${server.id}_", "")>
        <#assign parts = propNameWithoutPrefix?split("_")>
        <#assign camelCaseName = "${server.id}" + parts[0]?cap_first>
        <#list parts[1..] as part>
            <#assign camelCaseName = camelCaseName + part?cap_first>
        </#list>
    <#else>
        <#assign parts = propName?split("_")>
        <#assign camelCaseName = parts[0]>
        <#list parts[1..] as part>
            <#assign camelCaseName = camelCaseName + part?cap_first>
        </#list>
    </#if>
    /** ${prop.description!'配置项'} */
    private String ${camelCaseName};
    </#if>
</#list>

    public ${server.id?cap_first}McpTool() {
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        Properties props = new Properties();
        try (InputStream inputStream = new ClassPathResource(CONFIG_PATH).getInputStream()) {
            props.load(inputStream);
<#list business.properties as prop>
    <#if prop.key == "time.use_24hour_format">
            use24HourFormat = Boolean.parseBoolean(props.getProperty("${prop.key}", "${prop.value}"));
    <#else>
    <#-- 改进的属性名处理，确保使用正确的驼峰命名法并保留服务ID前缀 -->
    <#assign propName = prop.key>
    <#if propName?starts_with("${server.id}_")>
        <#assign propNameWithoutPrefix = propName?replace("${server.id}_", "")>
        <#assign parts = propNameWithoutPrefix?split("_")>
        <#assign camelCaseName = "${server.id}" + parts[0]?cap_first>
        <#list parts[1..] as part>
            <#assign camelCaseName = camelCaseName + part?cap_first>
        </#list>
    <#else>
        <#assign parts = propName?split("_")>
        <#assign camelCaseName = parts[0]>
        <#list parts[1..] as part>
            <#assign camelCaseName = camelCaseName + part?cap_first>
        </#list>
    </#if>
            ${camelCaseName} = props.getProperty("${prop.key}", "${prop.value}");
    </#if>
</#list>
        } catch (IOException e) {
            logger.error("无法加载配置文件: {}, 使用默认配置", CONFIG_PATH, e);
<#list business.properties as prop>
    <#if prop.key == "time.use_24hour_format">
            use24HourFormat = ${prop.value?matches("true")?string("true", "false")};
    <#else>
    <#-- 改进的属性名处理，确保使用正确的驼峰命名法并保留服务ID前缀 -->
    <#assign propName = prop.key>
    <#if propName?starts_with("${server.id}_")>
        <#assign propNameWithoutPrefix = propName?replace("${server.id}_", "")>
        <#assign parts = propNameWithoutPrefix?split("_")>
        <#assign camelCaseName = "${server.id}" + parts[0]?cap_first>
        <#list parts[1..] as part>
            <#assign camelCaseName = camelCaseName + part?cap_first>
        </#list>
    <#else>
        <#assign parts = propName?split("_")>
        <#assign camelCaseName = parts[0]>
        <#list parts[1..] as part>
            <#assign camelCaseName = camelCaseName + part?cap_first>
        </#list>
    </#if>
            ${camelCaseName} = "${prop.value}";
    </#if>
</#list>
        }
    }

<#list methods as method>
    @Tool(name = "${method.name}", description = "${method.description}")
    public Map<String, Object> ${method.name}(@ToolParamBean ${method.name?cap_first}Params params) {
        try {
            // 验证参数
            if (params == null) {
                throw new IllegalArgumentException("参数不能为空");
            }
            params.validateSelf();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);

            // TODO: 实现业务逻辑

            return result;
        } catch (Exception e) {
            return error(e, true);
        }
    }
</#list>

    // 参数类定义
<#list methods as method>
    @Data
    public static class ${method.name?cap_first}Params {
<#list method.params as param>
        @ToolParam(name = "${param.name}", description = "${param.description}", required = ${param.required?string("true", "false")})
        private String ${param.name?uncap_first};
</#list>

<#if method.name == "getCurrentTime">
        public String getTimezone() { return timezone != null ? timezone : defaultTimezone; }
</#if>

        public void validateSelf() {
<#list method.params as param>
<#if param.required>
            if (${param.name?uncap_first} == null || ${param.name?uncap_first}.trim().isEmpty()) {
                throw new IllegalArgumentException("${param.name?replace("_", " ")} 不能为空");
            }
</#if>
</#list>
<#if method.name == "convertTime">
            if (!time.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$")) {
                throw new IllegalArgumentException("时间格式错误，应为HH:mm，如：14:30");
            }
</#if>
        }

        // 生成紧凑的getter和setter方法
<#list method.params as param>
        public String get${param.name?cap_first?replace("_", "")}() { return ${param.name?uncap_first}; }
        public void set${param.name?cap_first?replace("_", "")}(String ${param.name?uncap_first}) { this.${param.name?uncap_first} = ${param.name?uncap_first}; }
</#list>
    }
</#list>

    private Map<String, Object> error(Exception e, boolean isPrintStackTrace) {
        String message = e.getMessage();
        if (isPrintStackTrace) {
            message += "\n" + ExceptionUtils.getStackTrace(e);
        }
        logger.error(message);
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("error", message);
        return error;
    }

    private Map<String, Object> error(String message) {
        logger.error(message);
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("error", message);
        return error;
    }
}