# ${server.name}

${server.description}

## 基本信息

- 服务ID：${server.id}
- 版本：${server.version}
- 类型：${server.type}
- 使用说明：${server.usage}

## 方法列表

<#list methods as method>
### ${method.name}

${method.description}

#### 参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
<#list method.params as param>
| ${param.name} | ${param.type} | ${param.required?string("是", "否")} | ${param.description} | ${param.example!""} |
</#list>

#### 返回值

| 字段名 | 类型 | 描述 |
|--------|------|------|
<#list method.output.fields as field>
| ${field.name} | ${field.type} | ${field.description} |
</#list>

#### 示例

```json
{
<#list method.output.fields as field>
  "${field.name}": <#if field.type == "String">"value"<#elseif field.type == "boolean">true<#elseif field.type == "Long">1234567890<#elseif field.type == "Map">{}<#else>"value"</#if><#if !field?is_last>,</#if>
</#list>
}
```

</#list>

## 配置项

| 配置项 | 默认值 | 描述 |
|--------|--------|------|
<#list business.properties as prop>
| ${prop.key} | ${prop.value} | ${prop.description} |
</#list>

## 使用示例

<#list methods as method>
### ${method.name}

```bash
curl -X GET "http://localhost:8080/api/v1/${server.id}?method=${method.name}<#list method.params as param>&${param.name}=${param.example!""}</#list>"
```

响应：

```json
{
<#list method.output.fields as field>
  "${field.name}": <#if field.type == "String">"value"<#elseif field.type == "boolean">true<#elseif field.type == "Long">1234567890<#elseif field.type == "Map">{}<#else>"value"</#if><#if !field?is_last>,</#if>
</#list>
}
```

</#list>

## API接口

<#if api?? && api.enabled>
- 路径: ${api.path}
- 方法: ${api.method}

### 查询参数

| 参数名 | 必填 | 描述 | 示例 |
|--------|------|------|------|
<#list api.queryParams as param>
| ${param.name} | ${param.required?string("是", "否")} | ${param.description} | ${param.example} |
</#list>

### 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
<#list methods[0].output.fields as field>
    "${field.name}": <#if field.type == "String">"示例值"<#elseif field.type == "boolean">true<#elseif field.type == "Long">1234567890<#elseif field.type == "Map">{}<#else>"示例值"</#if><#if !field?is_last>,</#if>
</#list>
  }
}
```

### 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 调用示例

#### curl调用示例

```bash
curl -X GET "${api.path}?method=${methods[0].name}<#list methods[0].params as param>&${param.name}=${param.example!""}</#list>" \
  -H "Content-Type: application/json"
```
<#else>
该服务未启用API接口。
</#if>

## MCP协议接口

### 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/${server.id}/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 工具信息

- **工具ID**: `${server.id}`
- **描述**: `${server.description}`
- **参数**:
<#list methods[0].params as param>
  - **${param.name}**: ${param.description} (${param.type}, ${param.required?string("必填", "可选")})
</#list>

### MCP协议调用示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/${server.id}/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行工具
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "${server.id}",
        params: {
<#list methods[0].params as param>
            ${param.name}: "${param.example!""}"<#if !param?is_last>,</#if>
</#list>
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
});
```

## 1. 服务概述

## 2. 功能说明

## 3. 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
<#list methods as tool>
<#list tool.params as param>
| ${param.name} | ${param.type} | ${param.required?string("是", "否")} | - | ${param.description} |
</#list>
</#list>

## 4. 输出说明

| 字段名 | 类型 | 说明 |
|-------|------|------|
<#list methods as tool>
<#list tool.output.fields as field>
| ${field.name} | ${field.type} | ${field.description} |
</#list>
</#list>

## 5. 配置说明

| 配置项 | 默认值 | 说明 |
|-------|--------|------|
<#list business.properties as prop>
| ${prop.key} | ${prop.value} | ${prop.description} |
</#list>

## 6. REST API接口

### 6.1 基本信息

- **路径**: /api/v1/${server.id}
- **方法**: GET
- **说明**: ${server.description}

### 6.2 API参数说明

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
<#list methods as tool>
<#list tool.params as param>
| ${param.name} | ${param.type} | ${param.required?string("是", "否")} | ${param.description} |
</#list>
</#list>

### 6.3 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
<#list methods[0].output.fields as field>
    "${field.name}": <#if field.type == "String">"示例值"<#elseif field.type == "boolean">true<#elseif field.type == "Long">1234567890<#elseif field.type == "Map">{}<#else>"示例值"</#if><#if !field?is_last>,</#if>
</#list>
  }
}
```

### 6.4 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 6.5 REST API调用示例

#### 6.5.1 请求参数JSON示例

```json
{
<#list methods[0].params as param>
  "${param.name}": "${param.example!""}"<#if !param?is_last>,</#if>
</#list>
}
```

#### 6.5.2 curl调用示例

```bash
curl -X GET "/api/v1/${server.id}?method=${methods[0].name}<#list methods[0].params as param>&${param.name}=value<#if !param?is_last>&</#if></#list>" \
  -H "Content-Type: application/json"
```

## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/${server.id}/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `${server.id}`
- **描述**: `${server.description}`
- **参数**:
<#list methods[0].params as param>
  - **${param.name}**: ${param.description} (${param.type}, ${param.required?string("必填", "可选")})
</#list>

### 7.4 MCP协议调用示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/${server.id}/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行工具
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "${server.id}",
        params: {
<#list methods[0].params as param>
            "${param.name}": "${param.example!""}"<#if !param?is_last>,</#if>
</#list>
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/${server.id}/sse`（仅访问${server.name}服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用${server.name}服务的请求，例如：`使用${server.name}服务`
3. Cline会自动调用MCP服务，并返回结果

## 8. 常见问题

1. **问题**: 如何配置${server.name}服务？
   **回答**: 可以通过修改`application.properties`或`application.yml`文件中的相关配置项来配置服务。

2. **问题**: ${server.name}服务支持哪些数据格式？
   **回答**: 服务支持JSON格式的数据交换。

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [开发指南](/docs/guides/development)
- [代码生成器指南](/docs/guides/code-generator)