package com.jdl.mcp.generator.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class NameConverterTest {

    @Test
    public void testToCamelCase() {
        assertEquals("getCurrentTime", NameConverter.toCamelCase("get_current_time"));
        assertEquals("convertTime", NameConverter.toCamelCase("convert_time"));
        assertEquals("defaultTimezone", NameConverter.toCamelCase("default_timezone"));
        assertEquals("", NameConverter.toCamelCase(""));
        assertNull(NameConverter.toCamelCase(null));
    }

    @Test
    public void testToPascalCase() {
        assertEquals("GetCurrentTime", NameConverter.toPascalCase("get_current_time"));
        assertEquals("ConvertTime", NameConverter.toPascalCase("convert_time"));
        assertEquals("DefaultTimezone", NameConverter.toPascalCase("default_timezone"));
        assertEquals("", NameConverter.toPascalCase(""));
        assertNull(NameConverter.toPascalCase(null));
    }

    @Test
    public void testCapitalize() {
        assertEquals("GetCurrentTime", NameConverter.capitalize("getCurrentTime"));
        assertEquals("ConvertTime", NameConverter.capitalize("convertTime"));
        assertEquals("", NameConverter.capitalize(""));
        assertNull(NameConverter.capitalize(null));
    }

    @Test
    public void testUncapitalize() {
        assertEquals("getCurrentTime", NameConverter.uncapitalize("GetCurrentTime"));
        assertEquals("convertTime", NameConverter.uncapitalize("ConvertTime"));
        assertEquals("", NameConverter.uncapitalize(""));
        assertNull(NameConverter.uncapitalize(null));
    }

    @Test
    public void testHandleSpecialWords() {
        assertEquals("defaultTimezone", NameConverter.handleSpecialWords("defaulttimezone"));
        assertEquals("dateFormat", NameConverter.handleSpecialWords("dateformat"));
        assertEquals("timeFormat", NameConverter.handleSpecialWords("timeformat"));
        assertEquals("", NameConverter.handleSpecialWords(""));
        assertNull(NameConverter.handleSpecialWords(null));
    }

    @Test
    public void testConvert() {
        // 测试转换为驼峰命名（首字母小写）
        assertEquals("getCurrentTime", NameConverter.convert("get_current_time", false));
        assertEquals("convertTime", NameConverter.convert("convert_time", false));
        assertEquals("defaultTimezone", NameConverter.convert("default_timezone", false));

        // 测试转换为帕斯卡命名（首字母大写）
        assertEquals("GetCurrentTime", NameConverter.convert("get_current_time", true));
        assertEquals("ConvertTime", NameConverter.convert("convert_time", true));
        assertEquals("DefaultTimezone", NameConverter.convert("default_timezone", true));

        // 测试边界情况
        assertEquals("", NameConverter.convert("", false));
        assertNull(NameConverter.convert(null, false));
    }
} 