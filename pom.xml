<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.jdl.mcp</groupId>
    <artifactId>mcp-platform</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>MCP :: Platform</name>
    <url>http://jdl.com</url>
    <modules>
        <module>mcp-admin</module>
        <module>mcp-core</module>
        <module>mcp-server</module>
        <module>mcp-generator</module>
        <module>mcp-solon</module>
    </modules>
    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <solon.version>3.2.0</solon.version>
        <spring.boot.version>2.7.18</spring.boot.version>
        <swagger.version>1.6.15</swagger.version>


        <jsf.version>1.7.8-HOTFIX-T3</jsf.version>
        <sc.core.version>1.0.3-SNAPSHOT</sc.core.version>
        <sso.version>1.0.5-SNAPSHOT</sso.version>
        <susf.version>1.2.2-SNAPSHOT</susf.version>
        <jd.fastjson.version>1.2.83-jdsec.rc1</jd.fastjson.version>

    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.active>dev</profile.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profile.active>prod</profile.active>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <profile.active>uat</profile.active>
            </properties>
        </profile>
    </profiles>
    <dependencyManagement>
        <dependencies>
            <!-- Base-->
            <dependency>
                <groupId>com.jdl.mcp</groupId>
                <artifactId>mcp-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.mcp</groupId>
                <artifactId>mcp-server</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.mcp</groupId>
                <artifactId>mcp-solon</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- Swagger UI -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!-- SC Core-->
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-utils</artifactId>
                <version>${sc.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-json</artifactId>
                <version>${sc.core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jdl.sc</groupId>
                        <artifactId>core-test</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-http</artifactId>
                <version>${sc.core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jdl.sc</groupId>
                        <artifactId>core-test</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jdl.sc</groupId>
                <artifactId>core-jsf</artifactId>
                <version>${sc.core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jdl.sc</groupId>
                        <artifactId>core-test</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- solon Core-->
            <dependency>
                <groupId>org.noear</groupId>
                <artifactId>solon-lib</artifactId>
                <version>${solon.version}</version>
            </dependency>

            <dependency>
                <groupId>org.noear</groupId>
                <artifactId>solon-web-servlet</artifactId>
                <version>${solon.version}</version>
            </dependency>

            <dependency>
                <groupId>org.noear</groupId>
                <artifactId>solon-ai-mcp</artifactId>
                <version>${solon.version}</version>
            </dependency>
            <!-- spring  -->

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- JD -->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>${jsf.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.ssa</groupId>
                <artifactId>oidc-client</artifactId>
                <version>${sso.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <!-- 由于低版本的fastjson存在反序列化漏洞，推荐使用内部加固版本1.2.83-jdsec.rc1的alibaba.fastjson组件，以免触发安全工单。详情参考：https://cf.jd.com/pages/viewpage.action?pageId=897987105 -->
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${jd.fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.susf</groupId>
                <artifactId>susf-client</artifactId>
                <version>${susf.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.4</version>
                <scope>compile</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
