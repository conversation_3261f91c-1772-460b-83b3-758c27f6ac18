package com.jdl.mcp.core.utils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.Locale;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 数字工具类，提供安全灵活的字符串转数字功能
 *
 * <AUTHOR> Assistant
 */
public class NumberUtils {

    /**
     * 数字格式正则表达式（包括整数、小数、科学计数法）
     */
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^[-+]?\\d+(\\.\\d+)?([eE][-+]?\\d+)?$");

    /**
     * 判断字符串是否为数字（整数、小数、科学计数法）
     *
     * @param str 待检查的字符串
     * @return 如果是数字格式返回true，否则返回false
     */
    public static boolean isNumber(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        return NUMBER_PATTERN.matcher(str.trim()).matches();
    }

    /**
     * 安全地将字符串转换为Integer
     *
     * @param str 待转换的字符串
     * @return 转换后的Integer对象，如果转换失败则返回null
     */
    public static Integer toInteger(String str) {
        return toInteger(str, null);
    }

    /**
     * 安全地将字符串转换为Integer，失败时返回默认值
     *
     * @param str          待转换的字符串
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的Integer对象，如果转换失败则返回默认值
     */
    public static Integer toInteger(String str, Integer defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(str.trim());
        } catch (NumberFormatException e) {
            try {
                // 尝试作为浮点数解析，然后转为整数
                Double doubleValue = Double.parseDouble(str.trim());
                return doubleValue.intValue();
            } catch (NumberFormatException ex) {
                return defaultValue;
            }
        }
    }

    /**
     * 安全地将字符串转换为Long
     *
     * @param str 待转换的字符串
     * @return 转换后的Long对象，如果转换失败则返回null
     */
    public static Long toLong(String str) {
        return toLong(str, null);
    }

    /**
     * 安全地将字符串转换为Long，失败时返回默认值
     *
     * @param str          待转换的字符串
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的Long对象，如果转换失败则返回默认值
     */
    public static Long toLong(String str, Long defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Long.parseLong(str.trim());
        } catch (NumberFormatException e) {
            try {
                // 尝试作为浮点数解析，然后转为长整数
                Double doubleValue = Double.parseDouble(str.trim());
                return doubleValue.longValue();
            } catch (NumberFormatException ex) {
                return defaultValue;
            }
        }
    }

    /**
     * 安全地将字符串转换为Double
     *
     * @param str 待转换的字符串
     * @return 转换后的Double对象，如果转换失败则返回null
     */
    public static Double toDouble(String str) {
        return toDouble(str, null);
    }

    /**
     * 安全地将字符串转换为Double，失败时返回默认值
     *
     * @param str          待转换的字符串
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的Double对象，如果转换失败则返回默认值
     */
    public static Double toDouble(String str, Double defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Double.parseDouble(str.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 安全地将字符串转换为Float
     *
     * @param str 待转换的字符串
     * @return 转换后的Float对象，如果转换失败则返回null
     */
    public static Float toFloat(String str) {
        return toFloat(str, null);
    }

    /**
     * 安全地将字符串转换为Float，失败时返回默认值
     *
     * @param str          待转换的字符串
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的Float对象，如果转换失败则返回默认值
     */
    public static Float toFloat(String str, Float defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Float.parseFloat(str.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 安全地将字符串转换为BigDecimal
     *
     * @param str 待转换的字符串
     * @return 转换后的BigDecimal对象，如果转换失败则返回null
     */
    public static BigDecimal toBigDecimal(String str) {
        return toBigDecimal(str, null);
    }

    /**
     * 安全地将字符串转换为BigDecimal，失败时返回默认值
     *
     * @param str          待转换的字符串
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的BigDecimal对象，如果转换失败则返回默认值
     */
    public static BigDecimal toBigDecimal(String str, BigDecimal defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return new BigDecimal(str.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 安全地将字符串转换为BigInteger
     *
     * @param str 待转换的字符串
     * @return 转换后的BigInteger对象，如果转换失败则返回null
     */
    public static BigInteger toBigInteger(String str) {
        return toBigInteger(str, null);
    }

    /**
     * 安全地将字符串转换为BigInteger，失败时返回默认值
     *
     * @param str          待转换的字符串
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的BigInteger对象，如果转换失败则返回默认值
     */
    public static BigInteger toBigInteger(String str, BigInteger defaultValue) {
        if (str == null || str.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return new BigInteger(str.trim());
        } catch (NumberFormatException e) {
            try {
                // 尝试作为浮点数解析，然后转为BigInteger
                BigDecimal decimal = new BigDecimal(str.trim());
                return decimal.toBigInteger();
            } catch (NumberFormatException ex) {
                return defaultValue;
            }
        }
    }

    /**
     * 将字符串解析为数字，自动判断类型（Integer, Long, Double, BigDecimal）
     *
     * @param str 待解析的字符串
     * @return 解析后的Number对象，如果解析失败则返回null
     */
    public static Number parseNumber(String str) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }

        String trimmed = str.trim();

        // 尝试解析为整数
        try {
            // 检查是否在Integer范围内
            int intValue = Integer.parseInt(trimmed);
            return intValue;
        } catch (NumberFormatException e) {
            // 继续尝试其他类型
        }

        // 尝试解析为长整数
        try {
            // 检查是否在Long范围内
            long longValue = Long.parseLong(trimmed);
            return longValue;
        } catch (NumberFormatException e) {
            // 继续尝试其他类型
        }

        // 尝试解析为双精度浮点数
        try {
            // 检查是否包含小数点或科学计数法
            if (trimmed.contains(".") || trimmed.contains("e") || trimmed.contains("E")) {
                double doubleValue = Double.parseDouble(trimmed);
                return doubleValue;
            }
        } catch (NumberFormatException e) {
            // 继续尝试其他类型
        }

        // 最后尝试解析为BigDecimal
        try {
            return new BigDecimal(trimmed);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 解析带有货币符号或千分位分隔符的数字字符串
     *
     * @param str    带格式的数字字符串，如 "$1,234.56" 或 "1.234,56" 等
     * @param locale 地区设置，用于确定数字格式，如果为null则使用默认地区
     * @return 解析后的Number对象，如果解析失败则返回null
     */
    public static Number parseFormattedNumber(String str, Locale locale) {
        if (str == null || str.trim().isEmpty()) {
            return null;
        }

        if (locale == null) {
            locale = Locale.getDefault();
        }

        try {
            // 使用NumberFormat解析带格式的数字
            NumberFormat format = NumberFormat.getInstance(locale);
            return format.parse(str.trim());
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 将数字格式化为指定模式的字符串
     *
     * @param number 要格式化的数字
     * @param pattern 格式模式，如 "#,##0.00" 表示带两位小数的千分位格式
     * @return 格式化后的字符串，如果输入为null则返回null
     */
    public static String format(Number number, String pattern) {
        if (number == null) {
            return null;
        }
        
        if (pattern == null || pattern.isEmpty()) {
            return number.toString();
        }
        
        DecimalFormat formatter = new DecimalFormat(pattern);
        return formatter.format(number);
    }

    /**
     * 比较两个数字是否相等，支持不同数字类型之间的比较
     *
     * @param n1 第一个数字
     * @param n2 第二个数字
     * @return 如果两个数字相等返回true，否则返回false
     */
    public static boolean equals(Number n1, Number n2) {
        if (n1 == n2) {
            return true;
        }
        if (n1 == null || n2 == null) {
            return false;
        }
        
        // 转换为BigDecimal进行精确比较
        BigDecimal bd1 = n1 instanceof BigDecimal ? (BigDecimal) n1 : new BigDecimal(n1.toString());
        BigDecimal bd2 = n2 instanceof BigDecimal ? (BigDecimal) n2 : new BigDecimal(n2.toString());
        
        return bd1.compareTo(bd2) == 0;
    }
}
