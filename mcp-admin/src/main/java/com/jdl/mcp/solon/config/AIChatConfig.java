package com.jdl.mcp.solon.config;

import lombok.Data;
import org.noear.solon.ai.chat.ChatConfig;
import org.noear.solon.ai.chat.ChatModel;
import org.noear.solon.annotation.Bean;
import org.noear.solon.annotation.Configuration;
import org.noear.solon.annotation.Inject;

@Configuration
public class AIChatConfig {
    @Bean
    public MyChatConfig chatConfig(@Inject("${solon.ai.chat.chatrhino}") MyChatConfig config) {
        return config;
    }

    @Bean
    public ChatModel chatModel(@Inject MyChatConfig config) {
        return ChatModel.of(config).build();
    }

    @Data
    public static class MyChatConfig extends ChatConfig {
        private String systemPrompt;

        public MyChatConfig() {
            super();
        }
    }
}
