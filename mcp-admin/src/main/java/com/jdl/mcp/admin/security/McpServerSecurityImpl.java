/*
package com.jdl.mcp.admin.security;

import com.jd.common.web.LoginContext;
import com.jd.susf.service.domain.DataInfo;
import com.jdl.mcp.admin.rpc.SusfServiceRpc;
import com.jdl.sc.core.utils.CollectionUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.file.AccessDeniedException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class McpServerSecurityImpl implements McpServerSecurity {
    @Autowired
    private SusfServiceRpc susfServiceRpc;

    @SneakyThrows
    @Override
    public boolean checkPermission(McpServer mcpServer, Map<String, Object> params) {
        //TODO 先不做权限校验
        if (true) {
            return true;
        }
        List<DataInfo> dataList;
        try {
            List<String> resourceList = new ArrayList<>();
            resourceList.add(mcpServer.getId());
            String pin = LoginContext.getLoginContext().getPin();
            dataList = susfServiceRpc.findDataListList(pin, "McpServer", resourceList);
        } catch (Exception e) {
            log.error("用户验证资源功能错误", e);
            //接口调用异常，直接降级为可访问
            throw new AccessDeniedException("用户验证资源功能错误:" + e.getMessage());
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return false;
        }
        return true;
    }
}
*/
