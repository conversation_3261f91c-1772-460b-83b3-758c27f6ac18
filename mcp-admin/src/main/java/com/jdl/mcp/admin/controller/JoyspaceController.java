package com.jdl.mcp.admin.controller;

import com.jdl.mcp.admin.client.McpServerClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Joyspace控制器
 * 提供Joyspace服务的页面和API
 */
@Controller
@RequestMapping("/joyspace")
public class JoyspaceController {

    private static final Logger logger = LoggerFactory.getLogger(JoyspaceController.class);

    @Autowired
    private McpServerClient mcpServerClient;

    /**
     * Joyspace页面
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping
    public String testPage(Model model) {
        logger.info("访问Joyspace页面");

        // 获取Joyspace服务信息
        Map<String, Object> server = mcpServerClient.getServer("joyspace");
        model.addAttribute("server", server);
        model.addAttribute("activeMenu", "joyspace");
        model.addAttribute("contentTitle", "Joyspace API 测试");
        model.addAttribute("breadcrumb", "Joyspace");

        return "joyspace-in-base";
    }

    /**
     * 获取文件夹列表
     * @param folderurl 文件夹URL
     * @param sort 排序方式
     * @return 执行结果
     */
    @PostMapping("/getFolderList")
    @ResponseBody
    public Map<String, Object> getFolderList(@RequestParam String folderurl,
                                            @RequestParam(required = false) String sort) {
        Map<String, Object> params = new HashMap<>();
        params.put("tool", "getFolderList");
        params.put("folderurl", folderurl);
        if (sort != null && !sort.isEmpty()) {
            params.put("sort", sort);
        }

        logger.info("调用getFolderList，参数：{}", params);
        return mcpServerClient.executeServer("joyspace", params);
    }

    /**
     * 获取文件列表
     * @param folderurl 文件夹URL
     * @param sort 排序方式
     * @param start 起始位置
     * @param length 长度
     * @return 执行结果
     */
    @PostMapping("/getFileList")
    @ResponseBody
    public Map<String, Object> getFileList(@RequestParam String folderurl,
                                          @RequestParam(required = false) String sort,
                                          @RequestParam(required = false) String start,
                                          @RequestParam(required = false) String length) {
        Map<String, Object> params = new HashMap<>();
        params.put("tool", "getFileList");
        params.put("folderurl", folderurl);
        if (sort != null && !sort.isEmpty()) {
            params.put("sort", sort);
        }
        if (start != null && !start.isEmpty()) {
            params.put("start", start);
        }
        if (length != null && !length.isEmpty()) {
            params.put("length", length);
        }

        logger.info("调用getFileList，参数：{}", params);
        return mcpServerClient.executeServer("joyspace", params);
    }

    /**
     * 获取页面信息
     * @param pageurl 页面URL
     * @return 执行结果
     */
    @PostMapping("/getPageInfo")
    @ResponseBody
    public Map<String, Object> getPageInfo(@RequestParam String pageurl) {
        Map<String, Object> params = new HashMap<>();
        params.put("tool", "getPageInfo");
        params.put("pageurl", pageurl);

        logger.info("调用getPageInfo，参数：{}", params);
        return mcpServerClient.executeServer("joyspace", params);
    }

    /**
     * 获取页面内容
     * @param pageurl 页面URL
     * @return 执行结果
     */
    @PostMapping("/getPageContent")
    @ResponseBody
    public Map<String, Object> getPageContent(@RequestParam String pageurl) {
        Map<String, Object> params = new HashMap<>();
        params.put("tool", "getPageContent");
        params.put("pageurl", pageurl);

        logger.info("调用getPageContent，参数：{}", params);
        return mcpServerClient.executeServer("joyspace", params);
    }
}
