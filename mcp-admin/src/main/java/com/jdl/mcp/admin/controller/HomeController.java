package com.jdl.mcp.admin.controller;

import com.jdl.mcp.admin.client.McpServerClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 首页控制器
 */
@Controller
public class HomeController {

    @Autowired
    private McpServerClient mcpServerClient;

    // 注释掉根路径映射，因为已经在AdminController中定义了
    // /**
    //  * 根路径重定向到首页
    //  * @return 重定向视图
    //  */
    // @GetMapping("/")
    // public String index() {
    //     return "redirect:/home";
    // }

    /**
     * 首页
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping("/home")
    public String home(Model model) {
        // 获取所有服务
        List<Map<String, Object>> servers = mcpServerClient.getAllServers();

        // 添加到模型
        model.addAttribute("servers", servers);

        // 添加服务ID列表
        List<String> serverIds = servers.stream()
                .map(server -> (String) server.get("id"))
                .collect(Collectors.toList());
        model.addAttribute("serverIds", serverIds);

        return "home-with-iframe";
    }

    /**
     * 聊天页面
     * @return 视图名称
     */
    @GetMapping("/chat-page")
    public String chat() {
        return "chat";
    }

    /**
     * API文档页面
     * @return 视图名称
     */
    @GetMapping("/api-docs")
    public String apiDocs() {
        return "api-docs";
    }

    /**
     * 文档中心页面
     * @return 视图名称
     */
    @GetMapping("/documentation")
    public String documentation() {
        return "redirect:/docs/";
    }

    /**
     * API调用示例页面
     * @return 视图名称
     */
    @GetMapping("/api-examples")
    public String apiExamples() {
        return "api-examples";
    }
}
