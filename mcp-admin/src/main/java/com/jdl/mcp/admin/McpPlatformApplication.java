package com.jdl.mcp.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

/**
 * MCP Platform主应用程序
 * 作为整个系统的统一入口点
 */
@ImportResource(value = {
        "classpath:spring/spring-main.xml"
})
@SpringBootApplication
@ComponentScan(basePackages = {"com.jdl.mcp"})
public class McpPlatformApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(McpPlatformApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return super.configure(application);
    }
}
