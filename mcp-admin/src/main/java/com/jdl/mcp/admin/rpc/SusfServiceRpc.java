package com.jdl.mcp.admin.rpc;


import com.jd.susf.client.context.SusfContext;
import com.jd.susf.service.api.SusfPermissionService;
import com.jd.susf.service.api.SusfUserService;
import com.jd.susf.service.domain.DataInfo;
import com.jd.susf.service.domain.Menu;
import com.jd.susf.service.dto.SusfDataDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
//@Component
public class SusfServiceRpc {
    @Autowired
    private SusfPermissionService susfPermissionService;

    @Autowired
    private SusfUserService susfUserService;

    @Autowired
    private SusfContext susfContext;


    public Map<String, Boolean> judgeUserHasResourceList(String pin, List<String> resourceList) {
        SusfDataDto susfDataDto = new SusfDataDto();
        susfDataDto.setTenementCode(susfContext.getTenementCode());
        susfDataDto.setAppCode(susfContext.getAppCode());
        susfDataDto.setUserSystemType(susfContext.getUserSystemType());
        susfDataDto.setCallUser(pin);
        susfDataDto.setTraceId(String.valueOf(System.nanoTime()));
        susfDataDto.setResourceCodeList(resourceList);
        susfDataDto.setUserCode(pin);
        return susfPermissionService.judgeUserHasResourceList(susfDataDto);
    }


    public List<DataInfo> findDataListList(String pin, String typeCode, List<String> resourceList) {
        SusfDataDto dto = new SusfDataDto();
        dto.setUserCode(pin);
        dto.setTenementCode(susfContext.getTenementCode());
        dto.setAppCode(susfContext.getAppCode());
        dto.setUserSystemType(susfContext.getUserSystemType());
        dto.setTypeCode(typeCode);
        dto.setResourceCodeList(resourceList);
        dto.setCallUser(pin);
        List<DataInfo> list = susfPermissionService.findDataListWithDistinct(dto);
        return list;
    }

    public List<Menu> getMenus(String tid, String pin) {
        SusfDataDto dto = new SusfDataDto();
        dto.setTenementCode(susfContext.getTenementCode());
        dto.setAppCode(susfContext.getAppCode());
        dto.setUserSystemType(susfContext.getUserSystemType());
        dto.setUserCode(pin);
        dto.setCallUser(pin);
        dto.setTraceId(tid);
        List<Menu> menus = susfPermissionService.findAllResourceList(dto);
        if (menus == null ||
                menus.size() == 0 ||
                menus.get(0).getChildren() == null ||
                menus.get(0).getChildren().size() == 0
        ) {
            return null;
        }
        return menus.get(0).getChildren();
    }

}
