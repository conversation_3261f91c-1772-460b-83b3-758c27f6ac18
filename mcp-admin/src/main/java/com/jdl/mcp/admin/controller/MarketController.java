package com.jdl.mcp.admin.controller;

import com.jdl.mcp.admin.client.McpServerClient;
import com.jdl.mcp.admin.util.MarkdownParserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP市场控制器
 */
@Controller
public class MarketController {

    private static final Logger logger = LoggerFactory.getLogger(MarketController.class);

    @Autowired
    private McpServerClient mcpServerClient;

    /**
     * MCP市场页面
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping("/market")
    public String market(Model model) {
        // 获取所有服务
        List<Map<String, Object>> servers = mcpServerClient.getAllServers();
        List<Map<String, Object>> serverInfoList = new ArrayList<>();

        // 构建服务信息列表，添加图标和颜色信息
        for (Map<String, Object> server : servers) {
            Map<String, Object> serverInfo = new HashMap<>(server);

            // 设置图标和颜色
            String type = (String) server.get("type");
            serverInfo.put("icon", getIconForServerType(type));
            serverInfo.put("color", getColorForServerType(type));

            // 确保status字段存在
            if (!serverInfo.containsKey("status")) {
                boolean running = Boolean.TRUE.equals(server.get("running"));
                serverInfo.put("status", running ? "RUNNING" : "STOPPED");
            }

            // 确保status字段是大写的
            if (serverInfo.containsKey("status") && serverInfo.get("status") instanceof String) {
                String status = (String) serverInfo.get("status");
                serverInfo.put("status", status.toUpperCase());
            }

            serverInfoList.add(serverInfo);
        }

        // 添加一些示例服务，用于测试
        if (serverInfoList.isEmpty()) {
            // 添加示例服务
            addExampleServer(serverInfoList, "amap", "高德地图", "高德地图MCP Server提供了12大核心接口，提供完整的地理信息服务。", "1.0.0", "API", "RUNNING");
            addExampleServer(serverInfoList, "agentbay", "无影 AgentBay", "无影AgentBay MCP Server为开发者和Agent应用提供一站式服务。", "2.1.0", "AI", "STOPPED");
            addExampleServer(serverInfoList, "tongxun", "通讯", "通讯是阿里云信息通讯服务的大模型接入层，支持通讯公共号等。", "1.5.0", "NETWORK", "RUNNING");
            addExampleServer(serverInfoList, "baidu-llm", "百度 LLM", "支持LLM 智能快速进行各类文本生成和内容创作的SaaS和DaaS平台。", "3.0.0", "AI", "RUNNING");
            addExampleServer(serverInfoList, "aviation", "飞常准-Aviation", "飞常准技术提供的国内首个完全MCP协议的航空数据服务平台 — Aviation MCP Server。", "1.0.0", "API", "STOPPED");
            addExampleServer(serverInfoList, "chatppt", "ChatPPT", "ChatPPT MCP Server 自动已经开放了10个智能PPT文档制作的核心能力。", "2.0.0", "AI", "RUNNING");
            addExampleServer(serverInfoList, "github", "GitHub", "GitHub 官方提供的服务，为开发者和和企业提供代码托管和协作功能。", "1.0.0", "API", "RUNNING");
            addExampleServer(serverInfoList, "ai-note", "AI小记", "一款全能AI个人知识记录工具，自动为每篇笔记生成标签和摘要。", "1.0.0", "AI", "STOPPED");
            addExampleServer(serverInfoList, "meitu", "美图影像", "美图影像MCP Server开发支持图像处理API的服务，提供各种图像处理功能。", "1.0.0", "FILE", "RUNNING");
        }

        // 添加调试日志
        System.out.println("服务列表大小: " + serverInfoList.size());
        for (Map<String, Object> server : serverInfoList) {
            System.out.println("服务: " + server.get("name") + ", 类型: " + server.get("type") + ", 状态: " + server.get("status"));
        }

        // 添加到模型
        model.addAttribute("servers", serverInfoList);
        model.addAttribute("activeMenu", "market");
        model.addAttribute("contentTitle", "MCP服务市场");
        model.addAttribute("breadcrumb", "MCP市场");

        return "market";
    }

    /**
     * MCP服务详情页面
     * @param serverId 服务ID
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping("/market/{serverId}")
    public String serverDetail(@PathVariable String serverId, Model model) {
        // 获取服务详情
        Map<String, Object> server = mcpServerClient.getServer(serverId);
        if (server == null || server.isEmpty()) {
            return "redirect:/market";
        }

        // 设置图标和颜色
        String type = (String) server.get("type");
        server.put("icon", getIconForServerType(type));
        server.put("color", getColorForServerType(type));

        // 确保status字段存在并且是大写的
        if (!server.containsKey("status")) {
            boolean running = Boolean.TRUE.equals(server.get("running"));
            server.put("status", running ? "RUNNING" : "STOPPED");
        } else if (server.get("status") instanceof String) {
            String status = (String) server.get("status");
            server.put("status", status.toUpperCase());
        }

        // 加载服务文档
        try {
            String docPath = "static/docs/servers/" + serverId + ".md";
            ClassPathResource resource = new ClassPathResource(docPath);

            if (resource.exists()) {
                String markdownContent = StreamUtils.copyToString(resource.getInputStream(), java.nio.charset.StandardCharsets.UTF_8);

                // 解析Markdown内容
                Map<String, Object> parsedMarkdown = MarkdownParserUtil.parseMarkdown(markdownContent);

                // 提取服务概述
                Map<String, String> serviceInfo = (Map<String, String>) parsedMarkdown.get("serviceInfo");
                if (serviceInfo != null) {
                    server.put("documentOverview", serviceInfo.get("overview"));
                    server.put("documentFeatures", serviceInfo.get("features"));
                }

                // 提取方法参数和输出
                Map<String, List<Map<String, String>>> methodParams = (Map<String, List<Map<String, String>>>) parsedMarkdown.get("methodParams");
                Map<String, List<Map<String, String>>> methodOutputs = (Map<String, List<Map<String, String>>>) parsedMarkdown.get("methodOutputs");

                if (methodParams != null && !methodParams.isEmpty()) {
                    server.put("documentMethodParams", methodParams);
                }

                if (methodOutputs != null && !methodOutputs.isEmpty()) {
                    server.put("documentMethodOutputs", methodOutputs);
                }

                // 提取API示例
                Map<String, String> apiExamples = (Map<String, String>) parsedMarkdown.get("apiExamples");
                if (apiExamples != null && !apiExamples.isEmpty()) {
                    server.put("documentApiExamples", apiExamples);
                }

                // 添加原始Markdown内容
                server.put("documentMarkdown", markdownContent);

                logger.info("成功加载服务文档: {}", serverId);
            } else {
                logger.warn("服务文档不存在: {}", docPath);
            }
        } catch (Exception e) {
            logger.error("加载服务文档失败: {}", serverId, e);
        }

        // 添加到模型
        model.addAttribute("server", server);
        model.addAttribute("activeMenu", "market");
        model.addAttribute("contentTitle", server.get("name") + " - 详情");
        model.addAttribute("breadcrumb", "MCP市场 / " + server.get("name"));

        return "server-detail-new";
    }

    /**
     * 根据服务类型获取图标
     * @param serverType 服务类型
     * @return 图标CSS类
     */
    private String getIconForServerType(String serverType) {
        if (serverType == null) {
            return "fas fa-cogs";
        }

        switch (serverType.toUpperCase()) {
            case "API":
                return "fas fa-cloud";
            case "DATABASE":
                return "fas fa-database";
            case "CACHE":
                return "fas fa-server";
            case "RPC":
                return "fas fa-exchange-alt";
            case "FILE":
                return "fas fa-file";
            case "NETWORK":
                return "fas fa-network-wired";
            case "AI":
                return "fas fa-robot";
            default:
                return "fas fa-cogs";
        }
    }

    /**
     * 根据服务类型获取颜色
     * @param serverType 服务类型
     * @return Bootstrap颜色类
     */
    private String getColorForServerType(String serverType) {
        if (serverType == null) {
            return "secondary";
        }

        switch (serverType.toUpperCase()) {
            case "API":
                return "primary";
            case "DATABASE":
                return "success";
            case "CACHE":
                return "info";
            case "RPC":
                return "warning";
            case "FILE":
                return "danger";
            case "NETWORK":
                return "dark";
            case "AI":
                return "purple";
            default:
                return "secondary";
        }
    }

    /**
     * 添加示例服务
     * @param serverList 服务列表
     * @param id 服务ID
     * @param name 服务名称
     * @param description 服务描述
     * @param version 服务版本
     * @param type 服务类型
     * @param status 服务状态
     */
    private void addExampleServer(List<Map<String, Object>> serverList, String id, String name, String description, String version, String type, String status) {
        Map<String, Object> server = new HashMap<>();
        server.put("id", id);
        server.put("name", name);
        server.put("description", description);
        server.put("version", version);
        server.put("type", type);
        server.put("status", status);
        server.put("icon", getIconForServerType(type));
        server.put("color", getColorForServerType(type));
        server.put("running", "RUNNING".equals(status));

        serverList.add(server);
    }
}
