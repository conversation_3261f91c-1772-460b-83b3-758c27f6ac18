package com.jdl.mcp.admin.config;

import com.jdl.mcp.admin.controller.ApiDynamicRegister;
import com.jdl.mcp.core.service.tool.McpEndpointProvider;
import com.jdl.mcp.core.service.McpServiceManager;
import com.jdl.mcp.core.service.annotation.McpEndpoint;
import com.jdl.mcp.core.service.impl.McpServiceManagerImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.OpenAPIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;


import java.util.Map;

/**
 * 本地MCP服务配置
 * 用于注册和管理本地MCP服务
 */
@Configuration
public class LocalMcpServerConfig {

    private static final Logger logger = LoggerFactory.getLogger(LocalMcpServerConfig.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private OpenAPIService openAPIService;

    private McpServiceManager mcpServieManager;

    @Autowired
    private ApiDynamicRegister apiDynamicRegister;

    /**
     * 创建RestTemplate
     *
     * @return RestTemplate
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * 创建本地MCP服务管理器
     *
     * @return McpServerManager
     */
    @Bean
    @Primary
    public McpServiceManager mcpServieManager() {
        logger.info("初始化本地MCP服务管理器");
        mcpServieManager = new McpServiceManagerImpl();
        return mcpServieManager;
    }


    public ResponseEntity<String> handleDynamicRequest() {
        return ResponseEntity.ok("Dynamic Endpoint Response");
    }

    /**
     * 在应用程序启动完成后自动注册所有MCP服务
     *
     * @param event 上下文刷新事件
     */
    @EventListener
    public void onApplicationEvent(ContextRefreshedEvent event) {

        Map<String, Object> map = applicationContext.getBeansWithAnnotation(McpEndpoint.class);
        openAPIService.addMappings(map);
        logger.info("发现{}个McpEndpoint服务", map.size());
        for (Object value : map.values()) {
            McpEndpointProvider provider = new McpEndpointProvider(value);
            logger.info("注册MCP服务: {}", provider.getId());

            mcpServieManager.registerServer(provider);
            apiDynamicRegister.register(provider);
        }
    }
}
