package com.jdl.mcp.admin.ai;

import com.jdl.mcp.core.service.McpServiceManager;
import com.jdl.mcp.core.service.tool.McpEndpointProvider;
import com.jdl.mcp.solon.chat.tool.McpFunctionTool;
import com.jdl.mcp.solon.chat.tool.McpMethodToolProvider;
import com.jdl.mcp.solon.config.AIChatConfig;
import com.jdl.sc.core.utils.Arrays;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.noear.snack.ONode;
import org.noear.solon.Solon;
import org.noear.solon.ai.chat.ChatModel;
import org.noear.solon.ai.chat.ChatRequest;
import org.noear.solon.ai.chat.ChatResponse;
import org.noear.solon.ai.chat.message.ChatMessage;
import org.noear.solon.ai.chat.tool.FunctionTool;
import org.noear.solon.annotation.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MCP函数调用服务
 * 使用大模型的函数调用功能集成MCP服务
 */
@Slf4j
@Service
public class McpFunctionCallingService {
    private static final String ERROR_MSG = "抱歉，处理您的请求时出现错误。请稍后再试。";

    @Autowired
    private McpServiceManager mcpServiceManager;

    /**
     * 聊天函数调用结果
     */
    @Data
    public static class FunctionCallResult {
        private final String aiResponse;
        private final Object functionResult;

        public static FunctionCallResult error(String errorMsg) {
            return new FunctionCallResult(ERROR_MSG + errorMsg, null);
        }
    }

    /**
     * 发送聊天消息
     * @param userMessage 用户消息
     * @return 聊天结果
     */
    public FunctionCallResult chat(String userMessage) {
        log.info("Processing user message: {}", userMessage);

        try {
            ChatModel chatModel = Solon.context().getBean(ChatModel.class);
            AIChatConfig.MyChatConfig config = Solon.context().getBean(AIChatConfig.MyChatConfig.class);

            ExecutionResult result = new ExecutionResult();
            ChatResponse response = buildChatRequest(chatModel, config, userMessage, result)
                    .call();

            return new FunctionCallResult(response.getMessage().getContent(), result.getData());
        } catch (Exception e) {
            log.error("Chat processing failed: {}", e.getMessage(), e);
            return FunctionCallResult.error(e.getMessage());
        }
    }

    /**
     * 构建聊天请求
     */
    private ChatRequest buildChatRequest(ChatModel chatModel,
                                         AIChatConfig.MyChatConfig config,
                                         String userMessage,
                                         ExecutionResult result) {
        List<ChatMessage> messages = Arrays.asList(
                ChatMessage.ofSystem(config.getSystemPrompt()),
                ChatMessage.ofUser(userMessage)
        );

        List<FunctionTool> tools = mcpServiceManager.getAllServers().stream()
                .flatMap(server -> new McpMethodToolProvider(server).getTools().stream())
                .map(tool -> new ToolWrapper(tool, result))
                .collect(Collectors.toList());

        return chatModel.prompt(messages)
                .options(opts -> tools.forEach(opts::toolsAdd));
    }

    /**
     * 工具包装器（记录执行结果）
     */
    private static class ToolWrapper implements FunctionTool {
        private final FunctionTool delegate;
        private final ExecutionResult result;

        ToolWrapper(FunctionTool tool, ExecutionResult result) {
            this.delegate = tool;
            this.result = result;
        }

        @Override
        public String name() {
            return delegate.name();
        }

        @Override
        public String description() {
            return delegate.description();
        }

        @Override
        public ONode inputSchema() {
            return delegate.inputSchema();
        }

        @Override
        public String handle(Map<String, Object> args) throws Throwable {
            if (delegate instanceof McpFunctionTool) {
                McpFunctionTool mcpTool = (McpFunctionTool) delegate;
                result.setData(mcpTool.execute(args));
                return mcpTool.mmlResult(result.getData());
            }
            return delegate.handle(args);
        }
    }

    /**
     * 执行结果容器
     */
    private static class ExecutionResult {
        private Object data;

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }
    }
}