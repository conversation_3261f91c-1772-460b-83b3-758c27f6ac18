package com.jdl.mcp.admin.util;

import org.commonmark.node.*;
import org.commonmark.parser.Parser;
import org.commonmark.ext.gfm.tables.TablesExtension;
import org.commonmark.ext.gfm.tables.TableBlock;
import org.commonmark.ext.gfm.tables.TableHead;
import org.commonmark.ext.gfm.tables.TableBody;
import org.commonmark.ext.gfm.tables.TableRow;
import org.commonmark.ext.gfm.tables.TableCell;
import org.commonmark.renderer.html.HtmlRenderer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Markdown解析工具类
 * 用于解析Markdown文档内容，提取重要部分（如服务概述、参数说明等）
 */
public class MarkdownParserUtil {

    private static final Logger logger = LoggerFactory.getLogger(MarkdownParserUtil.class);

    /**
     * 解析Markdown内容，提取文档结构
     * @param markdown Markdown内容
     * @return 解析后的文档结构
     */
    public static Map<String, Object> parseMarkdown(String markdown) {
        if (markdown == null || markdown.trim().isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, Object> result = new HashMap<>();

        try {
            // 设置解析选项，添加表格扩展
            List<org.commonmark.Extension> extensions = Collections.singletonList(TablesExtension.create());

            // 创建解析器
            Parser parser = Parser.builder().extensions(extensions).build();
            org.commonmark.node.Node document = parser.parse(markdown);

            // 提取标题和内容
            Map<String, String> sections = extractSections(document);
            result.put("sections", sections);

            // 提取服务基本信息
            Map<String, String> serviceInfo = extractServiceInfo(markdown);
            result.put("serviceInfo", serviceInfo);

            // 提取方法参数
            Map<String, List<Map<String, String>>> methodParams = extractMethodParams(document);
            result.put("methodParams", methodParams);

            // 提取方法输出
            Map<String, List<Map<String, String>>> methodOutputs = extractMethodOutputs(document);
            result.put("methodOutputs", methodOutputs);

            // 提取API示例
            Map<String, String> apiExamples = extractApiExamples(markdown);
            result.put("apiExamples", apiExamples);

        } catch (Exception e) {
            logger.error("解析Markdown内容失败", e);
        }

        return result;
    }

    /**
     * 提取文档的各个部分
     * @param document 解析后的文档节点
     * @return 各部分内容的映射
     */
    private static Map<String, String> extractSections(org.commonmark.node.Node document) {
        Map<String, String> sections = new LinkedHashMap<>();

        // 遍历文档查找标题和内容
        String currentHeading = null;
        StringBuilder currentContent = new StringBuilder();

        org.commonmark.node.Node node = document.getFirstChild();
        while (node != null) {
            if (node instanceof Heading) {
                // 如果已经有标题，保存之前的内容
                if (currentHeading != null && currentContent.length() > 0) {
                    sections.put(currentHeading, currentContent.toString().trim());
                    currentContent.setLength(0);
                }

                // 获取新标题
                currentHeading = getNodeText(node);
            } else if (node instanceof Paragraph) {
                if (currentHeading != null) {
                    // 提取段落文本
                    String paragraphText = getNodeText(node);
                    currentContent.append(paragraphText).append("\n\n");
                }
            } else if (node instanceof TableBlock) {
                if (currentHeading != null) {
                    // 使用HTML渲染器将表格转换为HTML
                    HtmlRenderer renderer = HtmlRenderer.builder().build();
                    String tableHtml = renderer.render(node);
                    currentContent.append(tableHtml).append("\n\n");
                }
            }

            node = node.getNext();
        }

        // 保存最后一个部分
        if (currentHeading != null && currentContent.length() > 0) {
            sections.put(currentHeading, currentContent.toString().trim());
        }

        return sections;
    }

    /**
     * 提取服务基本信息
     * @param markdown Markdown内容
     * @return 服务基本信息
     */
    private static Map<String, String> extractServiceInfo(String markdown) {
        Map<String, String> serviceInfo = new HashMap<>();

        // 提取服务ID
        Pattern serviceIdPattern = Pattern.compile("\\*\\*服务ID\\*\\*:\\s*([\\w-]+)");
        Matcher serviceIdMatcher = serviceIdPattern.matcher(markdown);
        if (serviceIdMatcher.find()) {
            serviceInfo.put("serviceId", serviceIdMatcher.group(1).trim());
        }

        // 提取版本
        Pattern versionPattern = Pattern.compile("\\*\\*版本\\*\\*:\\s*([\\d\\.]+)");
        Matcher versionMatcher = versionPattern.matcher(markdown);
        if (versionMatcher.find()) {
            serviceInfo.put("version", versionMatcher.group(1).trim());
        }

        // 提取类型
        Pattern typePattern = Pattern.compile("\\*\\*类型\\*\\*:\\s*(\\w+)");
        Matcher typeMatcher = typePattern.matcher(markdown);
        if (typeMatcher.find()) {
            serviceInfo.put("type", typeMatcher.group(1).trim());
        }

        // 提取服务概述
        Pattern overviewPattern = Pattern.compile("## 1\\. 服务概述\\s+([\\s\\S]*?)(?=##|$)");
        Matcher overviewMatcher = overviewPattern.matcher(markdown);
        if (overviewMatcher.find()) {
            String overview = overviewMatcher.group(1).trim();
            // 移除服务ID、版本、类型等行
            overview = overview.replaceAll("- \\*\\*服务ID\\*\\*:.*?\\n", "")
                              .replaceAll("- \\*\\*版本\\*\\*:.*?\\n", "")
                              .replaceAll("- \\*\\*类型\\*\\*:.*?\\n", "")
                              .replaceAll("- \\*\\*生成时间\\*\\*:.*?\\n", "")
                              .trim();
            serviceInfo.put("overview", overview);
        }

        // 提取功能说明
        Pattern featuresPattern = Pattern.compile("## 2\\. 功能说明\\s+([\\s\\S]*?)(?=##|$)");
        Matcher featuresMatcher = featuresPattern.matcher(markdown);
        if (featuresMatcher.find()) {
            serviceInfo.put("features", featuresMatcher.group(1).trim());
        }

        return serviceInfo;
    }

    /**
     * 提取方法参数
     * @param document 解析后的文档节点
     * @return 方法参数映射
     */
    private static Map<String, List<Map<String, String>>> extractMethodParams(org.commonmark.node.Node document) {
        Map<String, List<Map<String, String>>> methodParams = new HashMap<>();

        // 使用正则表达式匹配方法参数标题
        Pattern methodParamPattern = Pattern.compile("\\d+\\.\\d+\\s+(.*?)\\s+方法参数");

        // 遍历文档查找标题和表格
        org.commonmark.node.Node node = document.getFirstChild();
        while (node != null) {
            if (node instanceof Heading) {
                Heading heading = (Heading) node;
                // 获取标题文本
                String headingText = getNodeText(heading);

                Matcher matcher = methodParamPattern.matcher(headingText);
                if (matcher.matches()) {
                    // 提取方法名
                    String methodName = matcher.group(1).trim();

                    // 查找该标题后的表格
                    org.commonmark.node.Node nextNode = node.getNext();
                    while (nextNode != null && !(nextNode instanceof Heading)) {
                        if (nextNode instanceof TableBlock) {
                            List<Map<String, String>> params = parseTable((TableBlock) nextNode);
                            methodParams.put(methodName, params);
                            break;
                        }
                        nextNode = nextNode.getNext();
                    }
                }
            }
            node = node.getNext();
        }

        return methodParams;
    }

    /**
     * 提取方法输出
     * @param document 解析后的文档节点
     * @return 方法输出映射
     */
    private static Map<String, List<Map<String, String>>> extractMethodOutputs(org.commonmark.node.Node document) {
        Map<String, List<Map<String, String>>> methodOutputs = new HashMap<>();

        // 使用正则表达式匹配方法输出标题
        Pattern methodOutputPattern = Pattern.compile("\\d+\\.\\d+\\s+(.*?)\\s+方法输出");

        // 遍历文档查找标题和表格
        org.commonmark.node.Node node = document.getFirstChild();
        while (node != null) {
            if (node instanceof Heading) {
                Heading heading = (Heading) node;
                // 获取标题文本
                String headingText = getNodeText(heading);

                Matcher matcher = methodOutputPattern.matcher(headingText);
                if (matcher.matches()) {
                    // 提取方法名
                    String methodName = matcher.group(1).trim();

                    // 查找该标题后的表格
                    org.commonmark.node.Node nextNode = node.getNext();
                    while (nextNode != null && !(nextNode instanceof Heading)) {
                        if (nextNode instanceof TableBlock) {
                            List<Map<String, String>> outputs = parseTable((TableBlock) nextNode);
                            methodOutputs.put(methodName, outputs);
                            break;
                        }
                        nextNode = nextNode.getNext();
                    }
                }
            }
            node = node.getNext();
        }

        return methodOutputs;
    }

    /**
     * 获取节点的文本内容
     * @param node 节点
     * @return 文本内容
     */
    private static String getNodeText(org.commonmark.node.Node node) {
        StringBuilder text = new StringBuilder();
        org.commonmark.node.Node child = node.getFirstChild();
        while (child != null) {
            if (child instanceof Text) {
                text.append(((Text) child).getLiteral());
            }
            child = child.getNext();
        }
        return text.toString().trim();
    }

    /**
     * 解析表格
     * @param tableBlock 表格节点
     * @return 表格内容列表
     */
    private static List<Map<String, String>> parseTable(TableBlock tableBlock) {
        List<Map<String, String>> result = new ArrayList<>();

        // 获取表头
        List<String> headers = new ArrayList<>();
        TableHead tableHead = null;
        TableBody tableBody = null;

        // 查找表头和表体
        org.commonmark.node.Node child = tableBlock.getFirstChild();
        while (child != null) {
            if (child instanceof TableHead) {
                tableHead = (TableHead) child;
            } else if (child instanceof TableBody) {
                tableBody = (TableBody) child;
            }
            child = child.getNext();
        }

        // 如果没有表头或表体，返回空结果
        if (tableHead == null || tableBody == null) {
            return result;
        }

        // 解析表头
        TableRow headerRow = (TableRow) tableHead.getFirstChild();
        if (headerRow != null) {
            org.commonmark.node.Node headerCell = headerRow.getFirstChild();
            while (headerCell != null) {
                if (headerCell instanceof TableCell) {
                    headers.add(getNodeText(headerCell));
                }
                headerCell = headerCell.getNext();
            }
        }

        // 如果没有表头列，返回空结果
        if (headers.isEmpty()) {
            return result;
        }

        // 解析表体行
        TableRow bodyRow = (TableRow) tableBody.getFirstChild();
        while (bodyRow != null) {
            Map<String, String> row = new HashMap<>();

            org.commonmark.node.Node cell = bodyRow.getFirstChild();
            int columnIndex = 0;

            while (cell != null && columnIndex < headers.size()) {
                if (cell instanceof TableCell) {
                    String headerName = headers.get(columnIndex);
                    String cellValue = getNodeText(cell);

                    if (!headerName.isEmpty()) {
                        row.put(headerName, cellValue);
                    }

                    columnIndex++;
                }
                cell = cell.getNext();
            }

            if (!row.isEmpty()) {
                result.add(row);
            }

            bodyRow = (TableRow) bodyRow.getNext();
        }

        return result;
    }

    /**
     * 提取API示例
     * @param markdown Markdown内容
     * @return API示例映射
     */
    private static Map<String, String> extractApiExamples(String markdown) {
        Map<String, String> apiExamples = new HashMap<>();

        // 提取REST API调用示例
        Pattern restApiPattern = Pattern.compile("### \\d+\\.\\d+\\.\\d+\\s+(.*?)示例\\s+```bash\\s+(.*?)\\s+```", Pattern.DOTALL);
        Matcher restApiMatcher = restApiPattern.matcher(markdown);
        while (restApiMatcher.find()) {
            String exampleName = restApiMatcher.group(1).trim();
            String exampleCode = restApiMatcher.group(2).trim();
            apiExamples.put("rest_" + exampleName, exampleCode);
        }

        // 提取MCP协议调用示例
        Pattern mcpApiPattern = Pattern.compile("```javascript\\s+(.*?)\\s+```", Pattern.DOTALL);
        Matcher mcpApiMatcher = mcpApiPattern.matcher(markdown);
        if (mcpApiMatcher.find()) {
            apiExamples.put("mcp_example", mcpApiMatcher.group(1).trim());
        }

        return apiExamples;
    }

    /**
     * 提取文档中的特定部分
     * @param markdown Markdown内容
     * @param sectionName 部分名称
     * @return 提取的内容
     */
    @SuppressWarnings("unchecked")
    public static String extractSection(String markdown, String sectionName) {
        Map<String, Object> parsedMarkdown = parseMarkdown(markdown);
        Map<String, String> sections = (Map<String, String>) parsedMarkdown.get("sections");

        if (sections != null && sections.containsKey(sectionName)) {
            return sections.get(sectionName);
        }

        return null;
    }

    /**
     * 提取服务概述
     * @param markdown Markdown内容
     * @return 服务概述
     */
    @SuppressWarnings("unchecked")
    public static Map<String, String> extractServiceOverview(String markdown) {
        Map<String, Object> parsedMarkdown = parseMarkdown(markdown);
        return (Map<String, String>) parsedMarkdown.get("serviceInfo");
    }

    /**
     * 提取方法参数说明
     * @param markdown Markdown内容
     * @param methodName 方法名
     * @return 方法参数列表
     */
    @SuppressWarnings("unchecked")
    public static List<Map<String, String>> extractMethodParameters(String markdown, String methodName) {
        Map<String, Object> parsedMarkdown = parseMarkdown(markdown);
        Map<String, List<Map<String, String>>> methodParams = (Map<String, List<Map<String, String>>>) parsedMarkdown.get("methodParams");

        if (methodParams != null && methodParams.containsKey(methodName)) {
            return methodParams.get(methodName);
        }

        return Collections.emptyList();
    }

    /**
     * 提取方法输出说明
     * @param markdown Markdown内容
     * @param methodName 方法名
     * @return 方法输出列表
     */
    @SuppressWarnings("unchecked")
    public static List<Map<String, String>> extractMethodOutputs(String markdown, String methodName) {
        Map<String, Object> parsedMarkdown = parseMarkdown(markdown);
        Map<String, List<Map<String, String>>> methodOutputs = (Map<String, List<Map<String, String>>>) parsedMarkdown.get("methodOutputs");

        if (methodOutputs != null && methodOutputs.containsKey(methodName)) {
            return methodOutputs.get(methodName);
        }

        return Collections.emptyList();
    }

    /**
     * 提取API示例
     * @param markdown Markdown内容
     * @param exampleType 示例类型（rest或mcp）
     * @return API示例
     */
    @SuppressWarnings("unchecked")
    public static String extractApiExample(String markdown, String exampleType) {
        Map<String, Object> parsedMarkdown = parseMarkdown(markdown);
        Map<String, String> apiExamples = (Map<String, String>) parsedMarkdown.get("apiExamples");

        if (apiExamples != null) {
            if ("mcp".equals(exampleType) && apiExamples.containsKey("mcp_example")) {
                return apiExamples.get("mcp_example");
            } else {
                // 查找rest开头的示例
                for (Map.Entry<String, String> entry : apiExamples.entrySet()) {
                    if (entry.getKey().startsWith("rest_")) {
                        return entry.getValue();
                    }
                }
            }
        }

        return null;
    }
}
