package com.jdl.mcp.admin.controller;

import com.jdl.mcp.admin.client.McpServerClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/joywork")
public class JoyworkController {
    private static final Logger logger = LoggerFactory.getLogger(JoyworkController.class);

    @Autowired
    private McpServerClient mcpServerClient;

    @GetMapping
    public String testPage(Model model) {
        logger.info("访问Joywork页面");
        Map<String, Object> server = mcpServerClient.getServer("joywork");
        model.addAttribute("server", server);
        model.addAttribute("activeMenu", "joywork");
        model.addAttribute("contentTitle", "Joywork API 测试");
        model.addAttribute("breadcrumb", "joywork");
        return "joywork-in-base";
    }

    @PostMapping("/createTask")
    @ResponseBody
    public Map<String, Object> createTask(@RequestParam String title,
            @RequestParam String remark,
            @RequestParam String starttime,
            @RequestParam String endtime,
            @RequestParam String executor) {
        Map<String, Object> params = new HashMap<>();
        params.put("tool", "createTask");
        params.put("title", title);
        params.put("remark", remark);
        params.put("starttime", starttime);
        params.put("endtime", endtime);
        params.put("executor", executor);
        logger.info("调用createTask，参数：{}", params);
        return mcpServerClient.executeServer("joywork", params);
    }

    @PostMapping("/updateTask")
    @ResponseBody
    public Map<String, Object> updateTask(@RequestParam String taskid,
            @RequestParam String title,
            @RequestParam String remark,
            @RequestParam String starttime,
            @RequestParam String endtime) {
        Map<String, Object> params = new HashMap<>();
        params.put("tool", "updateTask");
        params.put("taskid", taskid);
        params.put("title", title);
        params.put("remark", remark);
        params.put("starttime", starttime);
        params.put("endtime", endtime);
        logger.info("调用updateTask，参数：{}", params);
        return mcpServerClient.executeServer("joywork", params);
    }

    @PostMapping("/updateTaskStatus")
    @ResponseBody
    public Map<String, Object> updateTaskStatus(@RequestParam String taskid,
            @RequestParam String taskstatus) {
        Map<String, Object> params = new HashMap<>();
        params.put("tool", "updateTaskStatus");
        params.put("taskid", taskid);
        params.put("taskstatus", taskstatus);
        logger.info("调用updateTaskStatus，参数：{}", params);
        return mcpServerClient.executeServer("joywork", params);
    }

    @PostMapping("/urgeTask")
    @ResponseBody
    public Map<String, Object> urgeTask(@RequestParam String taskid,
            @RequestParam String urgecontent,
            @RequestParam String taskusers) {
        Map<String, Object> params = new HashMap<>();
        params.put("tool", "urgeTask");
        params.put("taskid", taskid);
        params.put("urgecontent", urgecontent);
        params.put("taskusers", taskusers);
        logger.info("调用urgeTask，参数：{}", params);
        return mcpServerClient.executeServer("joywork", params);
    }
}