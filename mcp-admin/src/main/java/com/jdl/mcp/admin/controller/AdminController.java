package com.jdl.mcp.admin.controller;

import com.jdl.mcp.admin.client.McpServerClient;
import io.swagger.v3.oas.annotations.Hidden;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理后台控制器
 */
@Controller
@Hidden
public class AdminController {

    @Autowired
    private McpServerClient mcpServerClient;

    /**
     * 首页
     * @return 视图名称
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/dashboard";
    }

    /**
     * 仪表盘页面
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        List<Map<String, Object>> servers = mcpServerClient.getAllServers();
        model.addAttribute("servers", servers);
        return "dashboard";
    }

    /**
     * MCP Server详情页面
     * @param serverId MCP Server ID
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping("/server/{serverId}")
    public String serverDetail(@PathVariable String serverId, Model model) {
        Map<String, Object> server = mcpServerClient.getServer(serverId);
        if (server == null || server.isEmpty()) {
            return "redirect:/dashboard";
        }

        model.addAttribute("server", server);
        return "server-detail";
    }

    /**
     * 启动MCP Server
     * @param serverId MCP Server ID
     * @return 操作结果
     */
    @PostMapping("/server/{serverId}/start")
    @ResponseBody
    public Map<String, Object> startServer(@PathVariable String serverId) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = mcpServerClient.startServer(serverId);
            if (success) {
                result.put("success", true);
                result.put("message", "MCP Server启动成功");
            } else {
                result.put("success", false);
                result.put("message", "MCP Server启动失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "MCP Server启动失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 停止MCP Server
     * @param serverId MCP Server ID
     * @return 操作结果
     */
    @PostMapping("/server/{serverId}/stop")
    @ResponseBody
    public Map<String, Object> stopServer(@PathVariable String serverId) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = mcpServerClient.stopServer(serverId);
            if (success) {
                result.put("success", true);
                result.put("message", "MCP Server停止成功");
            } else {
                result.put("success", false);
                result.put("message", "MCP Server停止失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "MCP Server停止失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取所有MCP Server状态
     * @return MCP Server状态列表
     */
    @GetMapping("/api/servers")
    @ResponseBody
    public List<Map<String, Object>> getServers() {
        return mcpServerClient.getAllServers();
    }


}
