package com.jdl.mcp.admin.controller;

import com.jdl.mcp.admin.client.McpServerClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 健康检查控制器
 */
@RestController
@RequestMapping("/health")
public class HealthCheckController {

    @Autowired
    private McpServerClient mcpServerClient;

    /**
     * 系统健康检查
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        
        // 获取所有服务状态
        List<Map<String, Object>> servers = mcpServerClient.getAllServers();
        
        // 统计服务状态
        int totalServers = servers.size();
        int runningServers = 0;
        
        for (Map<String, Object> server : servers) {
            if (Boolean.TRUE.equals(server.get("running"))) {
                runningServers++;
            }
        }
        
        // 添加服务统计信息
        Map<String, Object> servicesInfo = new HashMap<>();
        servicesInfo.put("total", totalServers);
        servicesInfo.put("running", runningServers);
        servicesInfo.put("stopped", totalServers - runningServers);
        
        result.put("services", servicesInfo);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 服务健康检查
     */
    @GetMapping("/{serverId}")
    public ResponseEntity<Map<String, Object>> serviceHealthCheck(@PathVariable String serverId) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取服务信息
        Map<String, Object> server = mcpServerClient.getServer(serverId);
        
        if (server.isEmpty()) {
            result.put("status", "DOWN");
            result.put("message", "服务不存在: " + serverId);
        } else {
            boolean running = Boolean.TRUE.equals(server.get("running"));
            
            result.put("status", running ? "UP" : "DOWN");
            result.put("timestamp", System.currentTimeMillis());
            result.put("service", server);
            
            // 尝试执行简单的健康检查调用
            if (running) {
                try {
                    // 创建一个空参数调用，检查服务是否正常响应
                    Map<String, Object> response = mcpServerClient.executeServer(serverId, new HashMap<>());
                    result.put("check", response != null ? "SUCCESS" : "FAILED");
                } catch (Exception e) {
                    result.put("check", "FAILED");
                    result.put("error", e.getMessage());
                }
            }
        }
        
        return ResponseEntity.ok(result);
    }
}
