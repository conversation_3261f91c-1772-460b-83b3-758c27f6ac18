package com.jdl.mcp.admin.controller;

import com.jdl.mcp.core.service.McpServiceManager;
import com.jdl.mcp.core.service.tool.McpEndpointProvider;
import com.jdl.mcp.core.service.tool.McpToolParamDesc;
import com.jdl.mcp.core.service.tool.McpToolProvider;
import com.jdl.sc.core.utils.StringUtils;
import io.swagger.v3.oas.models.*;
import io.swagger.v3.oas.models.media.*;
import io.swagger.v3.oas.models.parameters.RequestBody;
import io.swagger.v3.oas.models.responses.ApiResponse;
import io.swagger.v3.oas.models.responses.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.customizers.OpenApiCustomiser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Swagger文档自定义处理器
 *
 * <p>功能说明：
 * 1. 自动扫描MCP服务端点并生成对应的OpenAPI文档
 * 2. 动态构建请求参数和响应模型
 * 3. 支持嵌套参数结构的文档生成</p>
 */
@Component
public class ApiSwaggerDocsCustomiser implements OpenApiCustomiser {
    private static final Logger logger = LoggerFactory.getLogger(ApiSwaggerDocsCustomiser.class);
    // 常量定义
    private static final String MEDIA_TYPE_JSON = "application/json";
    private static final String SUCCESS_RESPONSE_CODE = "200";
    private static final String SUCCESS_DESCRIPTION = "成功";
    private static final String OBJECT_TYPE = "object";
    private static final String STRING_TYPE = "string";
    private static final String INTEGER_TYPE = "integer";
    private static final String NUMBER_TYPE = "number";
    private static final String BOOLEAN_TYPE = "boolean";
    private static final String ARRAY_TYPE = "array";

    @Autowired
    private McpServiceManager mcpServiceManager;

    /**
     * 自定义OpenAPI文档生成
     *
     * @param openApi OpenAPI文档对象
     */
    @Override
    public void customise(OpenAPI openApi) {
        // 遍历所有MCP服务提供者
        mcpServiceManager.getAllServers().forEach(provider ->
                // 遍历每个提供者的所有工具
                provider.getTools().values().forEach(toolProvider ->
                        registerEndpoint(openApi, provider, toolProvider)
                )
        );
    }

    /**
     * 注册API端点到OpenAPI文档
     *
     * @param openApi      OpenAPI文档对象
     * @param provider     服务端点提供者
     * @param toolProvider 工具提供者
     */
    private void registerEndpoint(OpenAPI openApi, McpEndpointProvider provider, McpToolProvider toolProvider) {
        String tag = provider.getId() + (StringUtils.isBlank(provider.getEndpoint().description()) ? "" : " - " + provider.getEndpoint().description());
        String path = buildPath(provider.getUrl(), toolProvider.getName());
        PathItem pathItem = createPathItem(tag, toolProvider);
        openApi.path(path, pathItem);
    }

    /**
     * 构建API路径
     *
     * @param baseUrl  基础URL
     * @param endpoint 端点名称
     * @return 完整路径字符串
     */
    private String buildPath(String baseUrl, String endpoint) {
        return String.format("%s/%s", baseUrl, endpoint);
    }

    /**
     * 创建PathItem对象
     *
     * @param tag          API分组标签
     * @param toolProvider 工具提供者
     * @return PathItem对象
     */
    private PathItem createPathItem(String tag, McpToolProvider toolProvider) {
        // 创建 POST 操作
        Operation postOperation = new Operation()
                .tags(Collections.singletonList(tag))  // 设置API分组
                .summary(toolProvider.getDescription()) // 设置API摘要
                .requestBody(createRequestBody(toolProvider)) // 设置请求体
                .responses(createSuccessResponse()); // 设置响应

        // 返回只支持 POST 的 PathItem
        return new PathItem()
                .post(postOperation);
    }


    /**
     * 创建请求体
     *
     * @param toolProvider 工具提供者
     * @return RequestBody对象
     */
    private RequestBody createRequestBody(McpToolProvider toolProvider) {
        return new RequestBody()
                .content(new Content()
                        .addMediaType(MEDIA_TYPE_JSON,
                                new MediaType().schema(createSchema(toolProvider))));
    }

    /**
     * 创建参数Schema
     *
     * @param toolProvider 工具提供者
     * @return Schema对象
     */
    private Schema<?> createSchema(McpToolProvider toolProvider) {
        // 构建参数属性映射
        Map<String, Schema> properties = toolProvider.getParams().stream()
                .flatMap(this::extractParameters)
                .collect(Collectors.toMap(
                        McpToolParamDesc::getName,
                        this::createParamSchema
                ));

        return new Schema<>()
                .type(OBJECT_TYPE)
                .properties(properties)
                .required(getRequiredFields(toolProvider.getParams()));
    }

    /**
     * 提取参数(支持嵌套参数结构)
     *
     * @param param 参数描述
     * @return 参数流
     */
    private Stream<McpToolParamDesc> extractParameters(McpToolParamDesc param) {
        return Optional.ofNullable(param.getParams())
                .filter(params -> !params.isEmpty())
                .map(Collection::stream)
                .orElse(Stream.of(param));
    }

    /**
     * 创建参数Schema
     *
     * @param param 参数描述
     * @return Schema对象
     */
    private Schema<?> createParamSchema(McpToolParamDesc param) {
        Schema<?> schema;
        Class<?> paramType = param.getType();

        // 根据参数类型创建不同的 Schema
        if (paramType == String.class) {
            schema = new StringSchema().type(STRING_TYPE);
        } else if (paramType == Integer.class || paramType == int.class) {
            schema = new IntegerSchema().type(INTEGER_TYPE);
        } else if (paramType == Long.class || paramType == long.class) {
            schema = new IntegerSchema().type(INTEGER_TYPE).format("int64");
        } else if (paramType == Double.class || paramType == double.class ||
                paramType == Float.class || paramType == float.class) {
            schema = new NumberSchema().type(NUMBER_TYPE);
        } else if (paramType == Boolean.class || paramType == boolean.class) {
            schema = new BooleanSchema().type(BOOLEAN_TYPE);
        } else if (paramType.isArray() || Collection.class.isAssignableFrom(paramType)) {
            schema = new ArraySchema().type(ARRAY_TYPE);
        } else {
            // 默认为字符串
            schema = new StringSchema().type(STRING_TYPE);
        }

        // 设置描述
        schema.description(param.getDescription());

        // 设置默认值（仅设置字符串类型的默认值，避免类型转换问题）
        String defaultValue = param.getDefaultValue();
        if (defaultValue != null && !defaultValue.isEmpty()) {
            // 对于所有类型，我们都使用字符串形式的默认值
            // 这样可以避免类型转换问题
            schema.setDefault(defaultValue);
        }

        if (param.isRequired()) {
            schema.setRequired(Collections.singletonList(param.getName()));
        }

        return schema;
    }

    /**
     * 获取必填字段列表
     *
     * @param params 参数列表
     * @return 必填字段名称列表
     */
    private List<String> getRequiredFields(List<McpToolParamDesc> params) {
        return params.stream()
                .flatMap(this::extractParameters)
                .filter(McpToolParamDesc::isRequired)
                .map(McpToolParamDesc::getName)
                .collect(Collectors.toList());
    }

    /**
     * 创建成功响应
     *
     * @return ApiResponses对象
     */
    private ApiResponses createSuccessResponse() {
        return new ApiResponses()
                .addApiResponse(SUCCESS_RESPONSE_CODE,
                        new ApiResponse().description(SUCCESS_DESCRIPTION));
    }
}