package com.jdl.mcp.admin.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 参数处理工具类
 */
public class ParamProcessingUtils {
    private static final Logger log = LoggerFactory.getLogger(ParamProcessingUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 转换参数值
     * @param value 参数值
     * @return 转换后的值
     */
    public static Object convertParamValue(String value) {
        if (value == null) {
            return null;
        }
        
        // 尝试转换为数字
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Long.parseLong(value);
            }
        } catch (NumberFormatException e) {
            // 不是数字，继续
        }
        
        // 尝试转换为布尔值
        if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
            return Boolean.parseBoolean(value);
        }
        
        // 尝试解析 JSON
        if ((value.startsWith("{") && value.endsWith("}")) || 
            (value.startsWith("[") && value.endsWith("]"))) {
            try {
                return objectMapper.readValue(value, Object.class);
            } catch (Exception e) {
                log.debug("无法将值解析为 JSON: {}", value);
            }
        }
        
        // 其他情况，保持字符串
        return value;
    }
    
    /**
     * 根据目标类型转换参数值
     * @param value 参数值
     * @param targetType 目标类型
     * @return 转换后的值
     */
    public static Object convertParamValue(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }
        
        // 如果类型已经匹配，直接返回
        if (targetType.isInstance(value)) {
            return value;
        }
        
        // 字符串转换为目标类型
        if (value instanceof String) {
            String strValue = (String) value;
            
            if (targetType == Integer.class || targetType == int.class) {
                return Integer.parseInt(strValue);
            } else if (targetType == Long.class || targetType == long.class) {
                return Long.parseLong(strValue);
            } else if (targetType == Double.class || targetType == double.class) {
                return Double.parseDouble(strValue);
            } else if (targetType == Boolean.class || targetType == boolean.class) {
                return Boolean.parseBoolean(strValue);
            }
        }
        
        // 其他情况，尝试使用 Jackson 进行转换
        try {
            return objectMapper.convertValue(value, targetType);
        } catch (Exception e) {
            log.warn("参数转换失败: {} -> {}", value, targetType.getName());
            return value;
        }
    }
    
    /**
     * 验证参数
     * @param params 参数
     * @param requiredParams 必填参数
     * @return 验证结果
     */
    public static Map<String, String> validateParams(Map<String, Object> params, String... requiredParams) {
        Map<String, String> errors = new HashMap<>();
        
        for (String param : requiredParams) {
            if (!params.containsKey(param) || params.get(param) == null) {
                errors.put(param, "参数不能为空");
            }
        }
        
        return errors;
    }
}
