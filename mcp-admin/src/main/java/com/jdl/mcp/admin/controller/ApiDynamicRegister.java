package com.jdl.mcp.admin.controller;


import com.jdl.mcp.core.service.tool.McpEndpointProvider;
import com.jdl.mcp.core.service.tool.McpToolProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.springframework.web.util.pattern.PathPatternParser;

import java.lang.reflect.Method;
import java.util.Objects;

@Component
public class ApiDynamicRegister {
    private static final Logger logger = LoggerFactory.getLogger(ApiDynamicRegister.class);
    private static final PathPatternParser PATH_PATTERN_PARSER = new PathPatternParser();

    private RequestMappingHandlerMapping handlerMapping;

    @Autowired
    private ApplicationContext applicationContext;

    public ApiDynamicRegister() {
    }

    private RequestMappingHandlerMapping getHandlerMapping() {
        if (handlerMapping == null) {
            this.handlerMapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
        }
        return handlerMapping;
    }

    public void register(McpEndpointProvider provider) {
        Objects.requireNonNull(provider, "Provider cannot be null");
        logger.info("Registering endpoints for provider: {}", provider.getId());

        provider.getTools().values().forEach(tool ->
                registerEndpoint(provider, tool)
        );
    }

    private void registerEndpoint(McpEndpointProvider provider, McpToolProvider tool) {
        try {
            String path = buildPath(provider.getUrl(), tool.getName());

            // 注册 POST 请求处理器
            registerMapping(
                    path,
                    RequestMethod.POST,
                    tool.getTarget(),
                    tool.getMethod()
            );

            logger.debug("Registered endpoint: {} for tool: {} (supports POST only)", path, tool.getName());
        } catch (Exception e) {
            logger.error("Failed to register endpoint for tool: {}", tool.getName(), e);
            throw new EndpointRegistrationException("Failed to register endpoint: " + tool.getName(), e);
        }
    }

    private String buildPath(String baseUrl, String toolName) {
        return String.format("%s/%s", baseUrl, toolName);
    }

    private void registerMapping(String path, RequestMethod method,
                                 Object target, Method handlerMethod) {
        validateArguments(path, method, target, handlerMethod);

        RequestMappingInfo mappingInfo = createMappingInfo(path, method);
        HandlerMethod handler = new HandlerMethod(target, handlerMethod);

        getHandlerMapping().registerMapping(mappingInfo, handler.getBean(), handler.getMethod());
    }

    private RequestMappingInfo createMappingInfo(String path, RequestMethod method) {
        RequestMappingInfo.BuilderConfiguration config = new RequestMappingInfo.BuilderConfiguration();
        config.setPatternParser(PATH_PATTERN_PARSER);

        return RequestMappingInfo.paths(path)
                .methods(method)
                .options(config)
                .build();
    }

    private void validateArguments(String path, RequestMethod method,
                                   Object target, Method handlerMethod) {
        Objects.requireNonNull(path, "Path cannot be null");
        Objects.requireNonNull(method, "HTTP method cannot be null");
        Objects.requireNonNull(target, "Target object cannot be null");
        Objects.requireNonNull(handlerMethod, "Handler method cannot be null");
    }

    private static class EndpointRegistrationException extends RuntimeException {
        public EndpointRegistrationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}