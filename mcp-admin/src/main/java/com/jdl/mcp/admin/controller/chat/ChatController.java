package com.jdl.mcp.admin.controller.chat;

import com.jdl.mcp.admin.ai.McpFunctionCallingService;
import com.jdl.mcp.core.service.McpServiceManager;
import com.jdl.mcp.core.service.tool.McpEndpointProvider;
import com.jdl.mcp.core.service.tool.McpToolParamDesc;
import io.swagger.v3.oas.annotations.Hidden;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 聊天控制器
 */
@Controller
@Hidden
public class ChatController {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private McpFunctionCallingService mcpFunctionCallingService;

    @Autowired
    private McpServiceManager mcpServiceManager;

    /**
     * 聊天页面
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping("/chat")
    public String chatPage(Model model) {
        // 获取所有可用的MCP服务
        List<McpEndpointProvider> servers = mcpServiceManager.getAllServers();
        List<Map<String, Object>> serverInfoList = new ArrayList<>();

        // 构建服务信息列表
        for (McpEndpointProvider server : servers) {
            Map<String, Object> serverInfo = new HashMap<>();
            String serverId = server.getId();
            serverInfo.put("id", serverId);
            serverInfo.put("name", serverId);
            
            // 获取工具描述作为服务描述
            String description = server.getTools().values().stream()
                    .map(tool -> tool.getDescription())
                    .filter(desc -> desc != null && !desc.isEmpty())
                    .findFirst()
                    .orElse("");
            serverInfo.put("description", description);

            // 设置图标和示例
            String icon = getIconForServerType(server.getEndpoint().type().name());
            String example = getExampleFromTools(server);
            String detailedUsage = getDetailedUsageFromTools(server);

            serverInfo.put("icon", icon);
            serverInfo.put("example", example);
            serverInfo.put("detailedUsage", detailedUsage);

            serverInfoList.add(serverInfo);
        }

        model.addAttribute("servers", serverInfoList);
        return "chat-in-base";
    }

    /**
     * 根据服务类型获取图标
     * @param serverType 服务类型
     * @return 图标CSS类
     */
    private String getIconForServerType(String serverType) {
        if (serverType == null) {
            return "fas fa-cogs";
        }

        switch (serverType.toUpperCase()) {
            case "API":
                return "fas fa-cloud";
            case "DATABASE":
                return "fas fa-database";
            case "CACHE":
                return "fas fa-server";
            case "RPC":
                return "fas fa-exchange-alt";
            case "FILE":
                return "fas fa-file";
            case "NETWORK":
                return "fas fa-network-wired";
            default:
                return "fas fa-cogs";
        }
    }

    /**
     * 从服务获取示例
     * @param server MCP服务
     * @return 示例文本
     */
    private String getExampleFromTools(McpEndpointProvider server) {
        // 从工具列表中获取第一个工具的描述作为示例
        return server.getTools().values().stream()
                .map(tool -> "使用" + tool.getName() + "工具: " + tool.getDescription())
                .findFirst()
                .orElse("使用" + server.getId() + "服务");
    }

    /**
     * 从服务获取详细使用说明
     * @param server MCP服务
     * @return 详细使用说明
     */
    private String getDetailedUsageFromTools(McpEndpointProvider server) {
        StringBuilder detailedUsage = new StringBuilder();
        
        server.getTools().values().forEach(tool -> {
            if (detailedUsage.length() > 0) {
                detailedUsage.append("<br />");
            }
            
            // 添加工具名称和描述
            detailedUsage.append(tool.getName()).append(": ");
            if (tool.getDescription() != null && !tool.getDescription().isEmpty()) {
                detailedUsage.append(tool.getDescription());
            }
            
            // 添加参数信息
            if (!tool.getParams().isEmpty()) {
                detailedUsage.append("<br />参数：");
                appendParams(detailedUsage, tool.getParams(), "");
            }
        });

        return detailedUsage.length() > 0 ? detailedUsage.toString() : "可以执行" + server.getId() + "相关的操作。";
    }

    /**
     * 递归添加参数信息
     * @param sb StringBuilder对象
     * @param params 参数列表
     * @param indent 缩进
     */
    private void appendParams(StringBuilder sb, List<McpToolParamDesc> params, String indent) {
        params.forEach(param -> {
            List<McpToolParamDesc> nestedParams = param.getParams();
            if (nestedParams != null && !nestedParams.isEmpty()) {
                // 如果有嵌套参数，只显示嵌套参数的信息
                appendParams(sb, nestedParams, indent);
            } else {
                // 没有嵌套参数时，显示当前参数的信息
                sb.append("<br />").append(indent).append("- ").append(param.getName());
                if (param.getDescription() != null && !param.getDescription().isEmpty()) {
                    sb.append("(").append(param.getDescription()).append(")");
                }
                if (param.isRequired()) {
                    sb.append("[必填]");
                }
                if (param.getDefaultValue() != null && !param.getDefaultValue().isEmpty()) {
                    sb.append("[默认值: ").append(param.getDefaultValue()).append("]");
                }
            }
        });
    }

    /**
     * 发送消息
     * @param message 消息内容
     * @return 响应
     */
    @PostMapping("/chat/send")
    @ResponseBody
    public Map<String, Object> send(@RequestParam("message") String message) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 检查消息是否为空
            if (message == null || message.trim().isEmpty()) {
                log.warn("收到空消息");
                response.put("success", false);
                response.put("message", "请输入消息内容");
                return response;
            }

            log.info("收到用户消息: {}", message);

            // 使用MCP函数调用服务处理消息
            McpFunctionCallingService.FunctionCallResult result = mcpFunctionCallingService.chat(message);

            // 检查结果是否为空
            if (result == null) {
                log.error("聊天服务返回空结果");
                response.put("success", false);
                response.put("message", "抱歉，服务器处理您的请求时出现错误。请稍后再试。");
                return response;
            }

            log.info("AI回复: {}, 函数调用结果: {}", result.getAiResponse(), result.getFunctionResult());

            response.put("success", true);
            response.put("message", result.getAiResponse());

            // 如果有函数调用结果，添加到响应中
            if (result.getFunctionResult() != null) {
                // 如果是错误结果，添加错误标记
                Object functionResult = result.getFunctionResult();
                if (functionResult instanceof Map && ((Map<?, ?>) functionResult).containsKey("error")) {
                    log.warn("函数调用返回错误: {}", functionResult);
                }
                response.put("functionResults", new Object[]{functionResult});
            }
        } catch (Exception e) {
            log.error("聊天失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "抱歉，处理您的请求时出现错误。请稍后再试。");
        }

        return response;
    }
}
