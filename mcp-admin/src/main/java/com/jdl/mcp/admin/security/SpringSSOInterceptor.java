package com.jdl.mcp.admin.security;

import com.jd.common.web.LoginContext;
import com.jd.ssa.oidc.client.interceptor.ErpSsoInterceptor;
import com.jdl.sc.core.utils.TraceIdUtils;
import org.jboss.logging.MDC;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class SpringSSOInterceptor extends ErpSsoInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String tid = TraceIdUtils.getTraceId(null);
        if (request.getRequestURI().startsWith("/logout") ||
                request.getRequestURI().startsWith("/api/logout")) {
            this.toLogout(request, response);
            return false;
        }
        if (this.isExclude(request.getRequestURI())) {
            return true;
        }
        boolean result = super.preHandle(request, response, handler);
        if (result) {
            MDC.put("traceId", LoginContext.getLoginContext().getPin() + "-" + tid);
            //userCheckAndAdd(hiddenLoginResult.getData());
        }
        return result;
    }

}
