package com.jdl.mcp.admin.client;

import com.jdl.mcp.admin.utils.ParamProcessingUtils;
import com.jdl.mcp.core.service.McpServiceManager;
import com.jdl.mcp.core.service.tool.McpEndpointProvider;
import com.jdl.mcp.core.service.tool.McpToolParamDesc;
import com.jdl.mcp.core.service.tool.McpToolProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import com.jdl.mcp.core.service.annotation.ToolParam;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 本地MCP Server客户端
 * 直接使用McpServiceManager，不通过HTTP调用
 */
@Component
@Primary
public class LocalMcpServerClient implements McpServerClient {

    private static final Logger log = LoggerFactory.getLogger(LocalMcpServerClient.class);
    private final McpServiceManager mcpServiceManager;

    @Autowired
    public LocalMcpServerClient(McpServiceManager mcpServiceManager) {
        this.mcpServiceManager = mcpServiceManager;
        log.info("初始化本地MCP Server客户端");
    }

    /**
     * 获取所有MCP Server
     *
     * @return MCP Server列表
     */
    @Override
    public List<Map<String, Object>> getAllServers() {
        try {
            List<McpEndpointProvider> servers = mcpServiceManager.getAllServers();
            List<Map<String, Object>> result = new ArrayList<>();

            for (McpEndpointProvider server : servers) {
                Map<String, Object> serverMap = new HashMap<>();
                String serverId = server.getId();
                serverMap.put("id", serverId);
                serverMap.put("name", serverId);
                serverMap.put("description", server.getDescription());

                // 获取第一个工具的版本作为服务版本
                String version = server.getEndpoint().version(); // 默认版本
                serverMap.put("version", version);

                // 获取服务类型
                String type = server.getEndpoint().type().name();
                serverMap.put("type", type);

                // 设置运行状态
                boolean running = !server.getTools().isEmpty();
                serverMap.put("running", running);
                serverMap.put("status", running ? "RUNNING" : "STOPPED");
                result.add(serverMap);
            }

            return result;
        } catch (Exception e) {
            log.error("获取MCP Server异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取MCP Server
     *
     * @param serverId 服务ID
     * @return MCP Server
     */
    @Override
    public Map<String, Object> getServer(String serverId) {
        try {
            McpEndpointProvider server = mcpServiceManager.getServer(serverId);
            if (server == null) {
                return Collections.emptyMap();
            }

            Map<String, Object> serverMap = new HashMap<>();
            serverMap.put("id", server.getId());
            serverMap.put("name", server.getId());
            serverMap.put("description", server.getDescription());
            serverMap.put("version", server.getVersion());
            serverMap.put("usage", server.getDescription());
            // 获取服务类型
            String type = server.getEndpoint().type().name();
            serverMap.put("type", type);

            // 设置运行状态
            boolean running = !server.getTools().isEmpty();
            serverMap.put("running", running);
            serverMap.put("status", running ? "RUNNING" : "STOPPED");
            
            serverMap.put("sseEndpoint", server.getSseEndpoint());

            return serverMap;
        } catch (Exception e) {
            log.error("获取MCP Server异常", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 启动MCP Server
     *
     * @param serverId 服务ID
     * @return 是否成功
     */
    @Override
    public boolean startServer(String serverId) {
        try {
            // 启动服务不需要实现，因为服务是自动启动的
            return true;
        } catch (Exception e) {
            log.error("启动MCP Server异常", e);
            return false;
        }
    }

    /**
     * 停止MCP Server
     *
     * @param serverId 服务ID
     * @return 是否成功
     */
    @Override
    public boolean stopServer(String serverId) {
        try {
            // 停止服务不需要实现，因为服务是自动管理的
            return true;
        } catch (Exception e) {
            log.error("停止MCP Server异常", e);
            return false;
        }
    }

    /**
     * 执行MCP Server
     *
     * @param serverId 服务ID
     * @param params   参数
     * @return 执行结果
     */
    @Override
    public Map<String, Object> executeServer(String serverId, Map<String, Object> params) {
        log.info("开始执行服务: {}, 参数: {}", serverId, params);

        try {
            // 参数验证和预处理
            if (params == null) {
                params = new HashMap<>();
            }

            // 获取服务
            McpEndpointProvider server = mcpServiceManager.getServer(serverId);
            if (server == null) {
                log.error("执行 MCP Server 失败: 服务不存在 {}", serverId);
                return createErrorResponse("服务不存在: " + serverId);
            }

            // 获取合适的工具
            McpToolProvider tool = findSuitableTool(server, params);
            if (tool == null) {
                log.error("执行 MCP Server 失败: 服务没有可用的工具 {}", serverId);
                return createErrorResponse("服务没有可用的工具: " + serverId);
            }

            // 处理参数类型转换
            Map<String, Object> processedParams = preprocessParams(tool, params);

            // 执行工具方法
            log.debug("执行工具方法: {}.{}, 处理后的参数: {}",
                    tool.getTarget().getClass().getSimpleName(),
                    tool.getMethod().getName(),
                    processedParams);

            Object result;

            // 检查方法参数类型
            Class<?>[] paramTypes = tool.getMethod().getParameterTypes();
            log.debug("方法参数类型: {}, 参数数量: {}", Arrays.toString(paramTypes), paramTypes.length);

            if (paramTypes.length == 1) {
                Class<?> paramType = paramTypes[0];
                log.debug("参数类型: {}, 是否为Map: {}", paramType.getName(), Map.class.isAssignableFrom(paramType));

                // 如果参数类型不是 Map，尝试将 Map 转换为对应的类型
                if (!Map.class.isAssignableFrom(paramType)) {
                    log.debug("将参数转换为类型: {}", paramType.getName());
                    // 确保参数中包含了所有必要的字段
                    Map<String, Object> combinedParams = new HashMap<>(params);
                    // 移除tool参数，因为它不是目标对象的属性
                    combinedParams.remove("tool");
                    Object paramObj = convertMapToObject(combinedParams, paramType);
                    log.debug("转换后的参数对象: {}", paramObj);
                    result = tool.getMethod().invoke(tool.getTarget(), paramObj);
                } else {
                    // 参数类型是 Map，直接传递
                    log.debug("直接传递Map参数");
                    result = tool.getMethod().invoke(tool.getTarget(), processedParams);
                }
            } else {
                // 多个参数或无参数，直接调用
                log.debug("无参数调用");
                result = tool.getMethod().invoke(tool.getTarget(), (Object) null);
            }

            // 处理结果
            Map<String, Object> resultMap;
            if (result instanceof Map) {
                resultMap = (Map<String, Object>) result;
            } else {
                resultMap = new HashMap<>();
                resultMap.put("result", result);
            }

            log.info("服务 {} 执行成功, 结果: {}", serverId, resultMap);
            return resultMap;
        } catch (Exception e) {
            log.error("执行工具方法失败", e);
            return createErrorResponse("执行失败: " + e.getMessage());
        }
    }

    /**
     * 添加参数预处理方法
     * @param tool 工具
     * @param params 参数
     * @return 处理后的参数
     */
    private Map<String, Object> preprocessParams(McpToolProvider tool, Map<String, Object> params) {
        if (tool == null || tool.getParams().isEmpty()) {
            return params;
        }

        // 创建新的参数 Map
        Map<String, Object> processedParams = new HashMap<>();

        // 处理每个参数
        for (McpToolParamDesc paramDesc : tool.getParams()) {
            String paramName = paramDesc.getName();

            if (params.containsKey(paramName)) {
                Object paramValue = params.get(paramName);

                // 类型转换
                Object convertedValue = ParamProcessingUtils.convertParamValue(paramValue, paramDesc.getType());
                processedParams.put(paramName, convertedValue);
            } else if (paramDesc.getDefaultValue() != null && !paramDesc.getDefaultValue().isEmpty()) {
                // 使用默认值
                processedParams.put(paramName, ParamProcessingUtils.convertParamValue(
                        paramDesc.getDefaultValue(), paramDesc.getType()));
            } else if (paramDesc.isRequired()) {
                // 必填参数缺失
                log.warn("必填参数缺失: {}", paramName);
                throw new IllegalArgumentException("必填参数缺失: " + paramName);
            }
        }

        return processedParams;
    }

    /**
     * 查找合适的工具
     * @param server 服务
     * @param params 参数
     * @return 工具
     */
    private McpToolProvider findSuitableTool(McpEndpointProvider server, Map<String, Object> params) {
        // 如果只有一个工具，直接返回
        if (server.getTools().size() == 1) {
            return server.getTools().values().iterator().next();
        }

        // 尝试根据tool参数匹配工具
        if (params.containsKey("tool")) {
            String toolName = params.get("tool").toString();
            McpToolProvider tool = server.getTools().get(toolName);
            if (tool != null) {
                return tool;
            }
            log.warn("未找到指定的工具: {}, 将尝试使用默认工具", toolName);
        }

        // 默认返回第一个工具
        return server.getTools().values().stream().findFirst().orElse(null);
    }

    /**
     * 创建错误响应
     * @param message 错误消息
     * @return 错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        return response;
    }

    /**
     * 获取工具的使用说明
     *
     * @param tool 工具提供者
     * @return 使用说明
     */
    private String getToolUsage(McpToolProvider tool) {
        StringBuilder usage = new StringBuilder();
        usage.append(tool.getName()).append(": ");

        if (tool.getDescription() != null && !tool.getDescription().isEmpty()) {
            usage.append(tool.getDescription());
        }

        if (!tool.getParams().isEmpty()) {
            usage.append("\n参数：");
            tool.getParams().forEach(param -> {
                usage.append("\n- ").append(param.getName());
                if (param.getDescription() != null && !param.getDescription().isEmpty()) {
                    usage.append("(").append(param.getDescription()).append(")");
                }
                if (param.isRequired()) {
                    usage.append("[必填]");
                }
            });
        }

        return usage.toString();
    }

    /**
     * 将 Map 转换为指定类型的对象
     *
     * @param map Map 参数
     * @param targetType 目标类型
     * @return 转换后的对象
     */
    private Object convertMapToObject(Map<String, Object> map, Class<?> targetType) {
        try {
            // 创建目标类型的实例
            Object target = targetType.newInstance();

            log.debug("转换参数 Map 到对象类型: {}, 参数: {}", targetType.getName(), map);

            // 获取目标类型的所有字段
            for (Field field : targetType.getDeclaredFields()) {
                field.setAccessible(true);

                // 获取字段名称
                String fieldName = field.getName();

                // 检查是否有 @ToolParam 注解
                ToolParam toolParam = field.getAnnotation(ToolParam.class);
                if (toolParam != null && !toolParam.name().isEmpty()) {
                    fieldName = toolParam.name();
                    log.debug("字段 {} 有 @ToolParam 注解，使用注解名称: {}", field.getName(), fieldName);
                }

                log.debug("处理字段: {}, 查找参数: {}", fieldName, fieldName);

                // 如果 Map 中有对应的值，设置到字段中
                if (map.containsKey(fieldName)) {
                    Object value = map.get(fieldName);
                    log.debug("找到参数值: {} = {}", fieldName, value);

                    // 转换类型
                    Object convertedValue = ParamProcessingUtils.convertParamValue(value, field.getType());
                    log.debug("转换后的值: {} ({})", convertedValue, convertedValue != null ? convertedValue.getClass().getName() : "null");

                    // 设置字段值
                    field.set(target, convertedValue);
                    log.debug("设置字段 {} = {}", field.getName(), convertedValue);
                } else {
                    log.debug("未找到参数: {}", fieldName);
                }
            }

            return target;
        } catch (Exception e) {
            log.error("无法将 Map 转换为 " + targetType.getName(), e);
            throw new RuntimeException("无法将 Map 转换为 " + targetType.getName(), e);
        }
    }
}