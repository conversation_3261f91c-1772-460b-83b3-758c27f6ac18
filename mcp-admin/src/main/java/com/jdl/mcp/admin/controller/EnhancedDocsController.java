package com.jdl.mcp.admin.controller;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.models.OpenAPI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 增强版文档控制器
 * 提供更好的文档组织和展示功能
 */
@Controller
@Hidden
public class EnhancedDocsController {

    private static final Logger logger = LoggerFactory.getLogger(EnhancedDocsController.class);
    private static final Pattern SAFE_PATH_PATTERN = Pattern.compile("^[a-zA-Z0-9/_.-]+$");
    private static final List<String> ALLOWED_DIRECTORIES = Arrays.asList(
            "system", "development", "servers", "api-test"
    );

    @Value("${server.port:8081}")
    private String serverPort;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private Environment environment;

    @Autowired(required = false)
    private OpenAPI openAPI;

    /**
     * 增强版文档中心主页
     */
    @GetMapping({"/enhanced-docs", "/enhanced-docs/"})
    public String enhancedDocsPage(Model model) {
        logger.info("访问增强版文档中心页面");

        // 添加环境信息到模型
        model.addAttribute("activeProfile", activeProfile);
        model.addAttribute("serverPort", serverPort);
        model.addAttribute("baseUrl", getBaseUrl());

        return "docs";
    }

    /**
     * 其他文档路径重定向到增强版文档中心
     */
    @GetMapping({"/docs", "/docs/", "/docs-html", "/docs-html/"})
    public String redirectToEnhancedDocs() {
        logger.info("重定向到增强版文档中心");
        return "redirect:/enhanced-docs";
    }

    /**
     * 获取Markdown文档内容
     */
    @GetMapping(value = {"/enhanced-docs/**", "/docs/**", "/docs-html/**"}, produces = MediaType.TEXT_MARKDOWN_VALUE + ";charset=UTF-8")
    @ResponseBody
    public ResponseEntity<String> getMarkdownContent(HttpServletRequest request) {
        String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        logger.debug("处理文档请求: {}", path);

        // 验证路径
        if (path == null) {
            logger.warn("无效的文档请求路径: {}", path);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("无效的请求路径");
        }

        // 提取相对路径
        String relativePath;
        if (path.startsWith("/enhanced-docs/")) {
            relativePath = path.substring("/enhanced-docs/".length());
        } else if (path.startsWith("/docs/")) {
            relativePath = path.substring("/docs/".length());
        } else if (path.startsWith("/docs-html/")) {
            relativePath = path.substring("/docs-html/".length());
        } else {
            logger.warn("无效的文档请求路径前缀: {}", path);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("无效的请求路径");
        }

        // 检查是否是目录请求
        if (!relativePath.contains(".")) {
            try {
                String directoryPath = relativePath;
                if (!isPathSafe(directoryPath)) {
                    logger.warn("不安全的目录请求: {}", directoryPath);
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("不安全的请求路径");
                }
                return ResponseEntity.ok(listMarkdownFilesEnhanced(directoryPath));
            } catch (IOException e) {
                logger.error("生成目录列表失败: {}", relativePath, e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("生成目录列表失败");
            }
        }

        // 检查是否是 .md 文件
        if (!relativePath.toLowerCase().endsWith(".md")) {
            logger.warn("请求的不是Markdown文件: {}", path);
            return ResponseEntity.notFound().build();
        }

        // 验证路径安全性
        if (!isPathSafe(relativePath)) {
            logger.warn("不安全的文件请求: {}", relativePath);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("不安全的请求路径");
        }

        // 加载文档内容
        try {
            String content = loadMarkdownContentEnhanced("static/docs/" + relativePath);

            // 处理特殊标记，替换环境变量
            content = processEnvironmentVariables(content);

            return ResponseEntity.ok(content);
        } catch (IOException e) {
            logger.error("加载Markdown文档失败: {}", relativePath, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("加载文档失败");
        }
    }

    /**
     * 获取文档目录结构
     */
    @GetMapping("/api/docs/structure")
    @ResponseBody
    public Map<String, Object> getDocumentStructure() {
        Map<String, Object> structure = new HashMap<>();

        try {
            // 获取所有文档目录
            for (String directory : ALLOWED_DIRECTORIES) {
                List<Map<String, String>> docs = new ArrayList<>();

                PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
                Resource[] resources = resolver.getResources("classpath:static/docs/" + directory + "/*.md");

                for (Resource resource : resources) {
                    String filename = resource.getFilename();
                    if (filename != null) {
                        String displayName = formatDisplayName(filename);
                        String description = getDocumentDescription(resource);

                        Map<String, String> doc = new HashMap<>();
                        doc.put("id", filename.replace(".md", ""));
                        doc.put("name", displayName);
                        doc.put("path", directory + "/" + filename);
                        doc.put("description", description);

                        docs.add(doc);
                    }
                }

                // 按名称排序
                docs.sort(Comparator.comparing(doc -> doc.get("name")));

                structure.put(directory, docs);
            }

            // 添加Swagger API文档信息
            if (openAPI != null) {
                List<Map<String, String>> apiDocs = new ArrayList<>();

                // 添加API文档入口
                Map<String, String> swaggerDoc = new HashMap<>();
                swaggerDoc.put("id", "swagger-ui");
                swaggerDoc.put("name", "Swagger API文档");
                swaggerDoc.put("path", "swagger-ui");
                swaggerDoc.put("description", "交互式API文档，支持在线测试API");
                swaggerDoc.put("externalUrl", "/swagger-ui.html");

                apiDocs.add(swaggerDoc);

                // 合并到现有的API文档
                List<Map<String, String>> existingApiDocs = (List<Map<String, String>>) structure.getOrDefault("api", new ArrayList<>());
                existingApiDocs.addAll(apiDocs);

                structure.put("api", existingApiDocs);
            }

        } catch (Exception e) {
            logger.error("获取文档结构失败", e);
        }

        return structure;
    }

    /**
     * 获取服务文档内容
     */
    @GetMapping("/api/docs/server/{serverId}")
    @ResponseBody
    public ResponseEntity<String> getServerDocumentContent(@org.springframework.web.bind.annotation.PathVariable String serverId) {
        logger.info("获取服务文档内容: {}", serverId);

        // 尝试加载服务文档
        try {
            // 首先尝试从servers目录加载
            String content;
            try {
                content = loadMarkdownContentEnhanced("static/docs/servers/" + serverId + ".md");
                logger.info("从servers目录加载文档成功: {}", serverId);
            } catch (IOException e) {
                logger.warn("从servers目录加载文档失败: {}, 尝试从根目录加载", serverId);
                // 如果从servers目录加载失败，尝试从根目录加载
                try {
                    content = loadMarkdownContentEnhanced("static/docs/" + serverId + ".md");
                    logger.info("从根目录加载文档成功: {}", serverId);
                } catch (IOException ex) {
                    // 如果都加载失败，返回默认文档
                    logger.warn("从根目录加载文档失败: {}, 返回默认文档", serverId);
                    content = generateDefaultDocContent(serverId);
                }
            }

            // 处理特殊标记，替换环境变量
            content = processEnvironmentVariables(content);

            return ResponseEntity.ok(content);
        } catch (Exception e) {
            logger.error("加载服务文档失败: {}", serverId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("加载服务文档失败: " + e.getMessage());
        }
    }

    /**
     * 生成默认文档内容
     *
     * @param serverId 服务ID
     * @return 默认文档内容
     */
    private String generateDefaultDocContent(String serverId) {
        return "# " + serverId + " 服务\n\n" +
                "## 1. 服务概述\n\n" +
                "- **服务ID**: " + serverId + "\n" +
                "- **版本**: 1.0.0\n\n" +
                "这是一个MCP服务，提供了一系列功能。\n\n" +
                "## 2. 功能说明\n\n" +
                "该服务提供了以下功能：\n\n" +
                "- 功能1\n" +
                "- 功能2\n" +
                "- 功能3\n\n" +
                "## 3. 使用方法\n\n" +
                "### REST API\n\n" +
                "```\n" +
                "GET /api/v1/" + serverId + "?param1=value1&param2=value2\n" +
                "```\n\n" +
                "### MCP协议\n\n" +
                "```json\n" +
                "{\n" +
                "  \"jsonrpc\": \"2.0\",\n" +
                "  \"id\": \"request-id\",\n" +
                "  \"method\": \"" + serverId + "\",\n" +
                "  \"params\": {\n" +
                "    \"param1\": \"value1\",\n" +
                "    \"param2\": \"value2\"\n" +
                "  }\n" +
                "}\n" +
                "```\n\n" +
                "## 4. 更多信息\n\n" +
                "请参考MCP平台文档中心获取更多信息。\n";
    }

    /**
     * 获取服务API文档
     */
    @GetMapping("/api/docs/service-api")
    @ResponseBody
    public Map<String, Object> getServiceApiDocs(@RequestParam String serviceId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 加载统一服务文档
            String unifiedDocPath = "static/docs/" + serviceId + ".md";

            Map<String, String> docs = new HashMap<>();

            try {
                String content = loadMarkdownContentEnhanced(unifiedDocPath);
                // 处理环境变量
                content = processEnvironmentVariables(content);
                docs.put("unified", content);

                // 为了兼容旧版前端，同时填充service、api和mcp字段
                docs.put("service", content);
                docs.put("api", "");
                docs.put("mcp", "");
            } catch (IOException e) {
                logger.warn("统一服务文档不存在: {}", unifiedDocPath);

                // 尝试加载旧版文档（向后兼容）
                String servicePath = "static/docs/servers/" + serviceId + ".md";
                try {
                    docs.put("service", loadMarkdownContentEnhanced(servicePath));
                } catch (IOException ex) {
                    logger.warn("服务文档不存在: {}", servicePath);
                }

                String apiPath = "static/docs/api/" + serviceId + "-rest-api.md";
                try {
                    docs.put("api", loadMarkdownContentEnhanced(apiPath));
                } catch (IOException ex) {
                    logger.warn("API文档不存在: {}", apiPath);
                }

                String mcpPath = "static/docs/mcp/" + serviceId + "-mcp.md";
                try {
                    docs.put("mcp", loadMarkdownContentEnhanced(mcpPath));
                } catch (IOException ex) {
                    logger.warn("MCP协议文档不存在: {}", mcpPath);
                }
            }

            result.put("docs", docs);
            result.put("baseUrl", getBaseUrl());
            result.put("success", true);

        } catch (Exception e) {
            logger.error("获取服务API文档失败", e);
            result.put("success", false);
            result.put("message", "获取服务API文档失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证路径安全性
     */
    private boolean isPathSafe(String path) {
        if (path == null || path.isEmpty()) {
            return false;
        }

        // 检查路径格式
        if (!SAFE_PATH_PATTERN.matcher(path).matches()) {
            logger.warn("路径格式不符合要求: {}", path);
            return false;
        }

        // 获取根目录
        String rootDir = path.split("/")[0];
        if (!ALLOWED_DIRECTORIES.contains(rootDir)) {
            logger.warn("未授权的根目录访问: {}", rootDir);
            return false;
        }

        // 检查路径中是否包含 ..
        if (path.contains("..")) {
            logger.warn("路径中包含非法的父目录引用: {}", path);
            return false;
        }

        return true;
    }

    /**
     * 增强版列出指定目录下的Markdown文件
     */
    private String listMarkdownFilesEnhanced(String directory) throws IOException {
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources("classpath:static/docs/" + directory + "/*.md");

        // 如果没有找到文件，则返回空列表
        if (resources == null || resources.length == 0) {
            logger.warn("目录 {} 中没有找到Markdown文件", directory);
            return "<div class='alert alert-warning'>此目录下没有文档文件</div>";
        }

        // 按文件名排序
        Arrays.sort(resources, Comparator.comparing(r -> {
            try {
                return r.getFilename() != null ? r.getFilename() : "";
            } catch (Exception e) {
                return "";
            }
        }));

        StringBuilder html = new StringBuilder();
        html.append("<ul class=\"directory-listing\">\n");

        for (Resource resource : resources) {
            String filename = resource.getFilename();
            if (filename != null) {
                String displayName = formatDisplayName(filename);
                String description = getDocumentDescription(resource);

                html.append(String.format("<li><a href=\"%s\" data-filename=\"%s\" title=\"%s\">%s</a></li>\n",
                        filename, displayName, description, displayName));
            }
        }

        html.append("</ul>");
        return html.toString();
    }

    /**
     * 增强版加载Markdown文件内容
     */
    private String loadMarkdownContentEnhanced(String resourcePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(resourcePath);
        if (!resource.exists()) {
            throw new IOException("文档未找到: " + resourcePath);
        }
        return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
    }

    /**
     * 格式化显示名称
     */
    private String formatDisplayName(String filename) {
        String displayName = filename.replace(".md", "");
        displayName = displayName.replace("-", " ");

        // 首字母大写
        String[] words = displayName.split(" ");
        StringBuilder result = new StringBuilder();

        for (String word : words) {
            if (word.length() > 0) {
                result.append(word.substring(0, 1).toUpperCase())
                        .append(word.substring(1))
                        .append(" ");
            }
        }

        return result.toString().trim();
    }

    /**
     * 获取文档描述（从文档的第一行）
     */
    private String getDocumentDescription(Resource resource) {
        try {
            String content = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            String[] lines = content.split("\n");

            if (lines.length > 0) {
                String firstLine = lines[0].trim();
                if (firstLine.startsWith("# ")) {
                    return firstLine.substring(2);
                }
                return firstLine;
            }
        } catch (Exception e) {
            logger.warn("获取文档描述失败: {}", resource.getFilename(), e);
        }

        return "";
    }

    /**
     * 处理环境变量
     * 替换文档中的环境变量占位符
     */
    private String processEnvironmentVariables(String content) {
        // 替换服务器URL
        content = content.replace("${baseUrl}", getBaseUrl());
        content = content.replace("${serverPort}", serverPort);
        content = content.replace("${activeProfile}", activeProfile);

        // 替换其他环境变量
        for (String propertyName : environment.getActiveProfiles()) {
            String value = environment.getProperty(propertyName);
            if (value != null) {
                content = content.replace("${" + propertyName + "}", value);
            }
        }

        return content;
    }

    /**
     * 获取基础URL
     */
    private String getBaseUrl() {
        return "http://localhost:" + serverPort;
    }
}
