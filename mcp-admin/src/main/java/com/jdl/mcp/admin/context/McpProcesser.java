package com.jdl.mcp.admin.context;

import com.jdl.mcp.core.service.annotation.Tool;
import com.jdl.mcp.core.service.annotation.ToolParam;
import com.jdl.mcp.core.service.annotation.ToolParamBean;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;

@Component
public class McpProcesser extends RequestResponseBodyMethodProcessor {

    public McpProcesser(List<HttpMessageConverter<?>> converters) {
        super(converters);
    }

    @Override
    protected List<MediaType> getProducibleMediaTypes(
            HttpServletRequest request, Class<?> valueClass, @Nullable Type targetType) {
        return Arrays.asList(MediaType.APPLICATION_JSON);
    }

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(ToolParam.class) || parameter.hasParameterAnnotation(ToolParamBean.class);
    }

    @Override
    public boolean supportsReturnType(MethodParameter returnType) {
        return (AnnotatedElementUtils.hasAnnotation(returnType.getContainingClass(), Tool.class) ||
                returnType.hasMethodAnnotation(Tool.class));
    }
}
