package com.jdl.mcp.admin.controller;

import com.jdl.mcp.admin.client.McpServerClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

/**
 * 服务页面控制器
 */
@Controller
@RequestMapping("/servers")
public class ServerPageController {

    @Autowired
    private McpServerClient mcpServerClient;

    /**
     * 服务列表页面
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping
    public String serverList(Model model) {
        model.addAttribute("servers", mcpServerClient.getAllServers());
        return "servers/list";
    }

    /**
     * 服务详情页面
     * @param id 服务ID
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping("/{id}")
    public String serverDetail(@PathVariable String id, Model model) {
        Map<String, Object> server = mcpServerClient.getServer(id);
        if (server == null || server.isEmpty()) {
            return "redirect:/servers";
        }

        model.addAttribute("server", server);

        // 如果是joyspace服务，添加测试页面链接
        if ("joyspace".equals(id)) {
            model.addAttribute("testPageUrl", "/joyspace-test");
        }

        return "servers/" + id;
    }
}
