package com.jdl.mcp.admin.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

/**
 * 聊天服务配置类
 */
@Configuration
public class ChatServiceConfig {
    /**
     * 聊天服务配置属性
     */
    @Bean
    @Primary
    @ConfigurationProperties(prefix = "chat")
    public ChatServiceProperties chatServiceProperties() {
        return new ChatServiceProperties();
    }

    /**
     * 聊天服务配置属性类
     */
    public static class ChatServiceProperties {
        private ApiConfig api = new ApiConfig();
        private String systemPrompt;
        private McpConfig mcp = new McpConfig();

        // Getters and setters
        public ApiConfig getApi() {
            return api;
        }

        public void setApi(ApiConfig api) {
            this.api = api;
        }

        public String getSystemPrompt() {
            return systemPrompt;
        }

        public void setSystemPrompt(String systemPrompt) {
            this.systemPrompt = systemPrompt;
        }

        public McpConfig getMcp() {
            return mcp;
        }

        public void setMcp(McpConfig mcp) {
            this.mcp = mcp;
        }
    }

    /**
     * API配置
     */
    public static class ApiConfig {
        private String url;
        private String key;
        private String model;
        private String erp;
        private int timeout = 60000;

        // Getters and setters
        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public String getErp() {
            return erp;
        }

        public void setErp(String erp) {
            this.erp = erp;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }
    }

    /**
     * MCP配置
     */
    public static class McpConfig {
        private boolean enabled = true;
        private boolean autoDiscover = true;

        // Getters and setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isAutoDiscover() {
            return autoDiscover;
        }

        public void setAutoDiscover(boolean autoDiscover) {
            this.autoDiscover = autoDiscover;
        }
    }
}
