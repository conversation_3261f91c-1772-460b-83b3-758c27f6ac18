package com.jdl.mcp.admin.controller;

import com.jdl.mcp.admin.client.McpServerClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Timline控制器
 * 提供Timline服务的页面和API
 */
@Controller
@RequestMapping("/timline")
public class TimlineController {

    private static final Logger logger = LoggerFactory.getLogger(TimlineController.class);

    @Autowired
    private McpServerClient mcpServerClient;

    /**
     * Timline页面
     * @param model 模型
     * @return 视图名称
     */
    @GetMapping
    public String timlinePage(Model model) {
        logger.info("访问Timline页面");

        // 获取Timline服务信息
        Map<String, Object> server = mcpServerClient.getServer("timline");
        model.addAttribute("server", server);
        model.addAttribute("activeMenu", "timline");
        model.addAttribute("contentTitle", "Timline API 测试");
        model.addAttribute("breadcrumb", "Timline");

        return "timline-in-base";
    }

    /**
     * 发送消息
     * @param receiver 接收人
     * @param receiverType 接收人类型
     * @param messageType 消息类型
     * @param messageContent 消息内容
     * @param robotId 机器人ID
     * @return 执行结果
     */
    @PostMapping("/sendMessage")
    @ResponseBody
    public Map<String, Object> sendMessage(
            @RequestParam String receiver,
            @RequestParam(required = false) String receiverType,
            @RequestParam(required = false) String messageType,
            @RequestParam String messageContent,
            @RequestParam(required = false) String robotId) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("tool", "sendMessage");
        params.put("receiver", receiver);
        
        if (receiverType != null && !receiverType.isEmpty()) {
            params.put("receiverType", receiverType);
        }
        
        if (messageType != null && !messageType.isEmpty()) {
            params.put("messageType", messageType);
        }
        
        params.put("messageContent", messageContent);
        
        if (robotId != null && !robotId.isEmpty()) {
            params.put("robotId", robotId);
        }

        logger.info("调用sendMessage，参数：{}", params);
        return mcpServerClient.executeServer("timline", params);
    }
}
