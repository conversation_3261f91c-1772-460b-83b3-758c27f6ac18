package com.jdl.mcp.admin.client;

import java.util.List;
import java.util.Map;

/**
 * MCP Server客户端接口
 * 用于调用MCP Server的功能
 */
public interface McpServerClient {

    /**
     * 获取所有MCP Server
     *
     * @return MCP Server列表
     */
    List<Map<String, Object>> getAllServers();

    /**
     * 获取MCP Server
     *
     * @param serverId 服务ID
     * @return MCP Server
     */
    Map<String, Object> getServer(String serverId);

    /**
     * 启动MCP Server
     *
     * @param serverId 服务ID
     * @return 是否成功
     */
    boolean startServer(String serverId);

    /**
     * 停止MCP Server
     *
     * @param serverId 服务ID
     * @return 是否成功
     */
    boolean stopServer(String serverId);

    /**
     * 执行MCP Server
     *
     * @param serverId 服务ID
     * @param params   参数
     * @return 执行结果
     */
    Map<String, Object> executeServer(String serverId, Map<String, Object> params);
}
