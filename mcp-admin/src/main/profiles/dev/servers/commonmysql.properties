# 多集群MySQL数据库服务 业务配置
# 生成时间: 2025-04-26 17:48:09

# 服务基本信息
commonmysql.name=多集群MySQL数据库服务
commonmysql.description=提供多集群MySQL数据库操作功能，支持连接不同服务器上的数据库，仅允许执行SELECT、SHOW、DESCRIBE等查询语句
commonmysql.version=1.0.0
commonmysql.type=DATABASE
commonmysql.usage=多集群MySQL数据库服务，参数：cluster - 集群名称，sql - SQL语句，database - 数据库名称

# 集群列表
mysql.clusters=main,analytics,reporting

# 允许的SQL命令
mysql.allowed-commands=SELECT,SHOW,DESCRIBE,DESC

# main集群配置
mysql.main.alias=主数据库集群
mysql.main.url=*********************************************************************************************************************************************************************
mysql.main.username=ofw_ddl
mysql.main.password=7UryIGIDlw6h4UgsxRABzZmJw70H5ZSx
mysql.main.driver-class-name=com.mysql.cj.jdbc.Driver
mysql.main.default-database=t_oms
mysql.main.max-active=10
mysql.main.max-idle=5
mysql.main.min-idle=2
mysql.main.initial-size=5
mysql.main.max-wait=10000
mysql.main.validation-query=SELECT 1
mysql.main.test-on-borrow=true
mysql.main.test-while-idle=true

# analytics集群配置
mysql.analytics.alias=数据分析集群
mysql.analytics.url=**********************************************************************************************************************************************************************
mysql.analytics.username=ofw_ddl
mysql.analytics.password=7UryIGIDlw6h4UgsxRABzZmJw70H5ZSx
mysql.analytics.driver-class-name=com.mysql.cj.jdbc.Driver
mysql.analytics.default-database=mcp_db
mysql.analytics.max-active=20
mysql.analytics.max-idle=10
mysql.analytics.min-idle=5
mysql.analytics.initial-size=10
mysql.analytics.max-wait=10000
mysql.analytics.validation-query=SELECT 1
mysql.analytics.test-on-borrow=true
mysql.analytics.test-while-idle=true

# reporting集群配置
mysql.reporting.alias=报表数据集群
mysql.reporting.url=*******************************************************************************************************************************************************************
mysql.reporting.username=ofw_ddl
mysql.reporting.password=7UryIGIDlw6h4UgsxRABzZmJw70H5ZSx
mysql.reporting.driver-class-name=com.mysql.cj.jdbc.Driver
mysql.reporting.default-database=pms
mysql.reporting.max-active=15
mysql.reporting.max-idle=8
mysql.reporting.min-idle=3
mysql.reporting.initial-size=8
mysql.reporting.max-wait=10000
mysql.reporting.validation-query=SELECT 1
mysql.reporting.test-on-borrow=true
mysql.reporting.test-while-idle=true
