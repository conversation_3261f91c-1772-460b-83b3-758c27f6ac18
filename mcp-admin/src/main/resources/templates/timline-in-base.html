<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html(
    title='Timline API 测试 - MCP Platform',
    activeMenu='timline',
    contentTitle='Timline API 测试',
    breadcrumb='Timline',
    content=~{::content},
    headContent=~{::headContent},
    scriptContent=~{::scriptContent}
)}">
<head>
    <title>Timline API 测试</title>
    <th:block th:fragment="headContent">
        <link rel="stylesheet" href="/highlight/styles/github.min.css">
        <link rel="stylesheet" href="/css/timline-test.css">
    </th:block>
</head>
<body>
<th:block th:fragment="content">
    <div class="container-fluid">
        <div class="alert alert-info" th:if="${server != null}">
            <h4 th:text="${server.name}">Timline</h4>
            <p th:text="${server.description}">描述</p>
            <p><strong>版本:</strong> <span th:text="${server.version}">1.0.0</span></p>
            <p><strong>状态:</strong> <span th:text="${server.status}">RUNNING</span></p>
        </div>

        <div class="alert alert-danger" th:if="${server == null}">
            无法获取Timline服务信息，请确保服务已启动。
        </div>

        <ul class="nav nav-tabs" id="apiTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" id="send-message-tab" data-toggle="tab" href="#send-message" role="tab" aria-controls="send-message" aria-selected="true">发送消息</a>
            </li>
        </ul>

        <div class="tab-content" id="apiTabsContent">
            <!-- 发送消息 -->
            <div class="tab-pane fade show active" id="send-message" role="tabpanel" aria-labelledby="send-message-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>发送消息 (sendMessage)</h5>
                    </div>
                    <div class="card-body">
                        <form id="sendMessageForm">
                            <div class="form-group">
                                <label for="sendMessageReceiver">接收人 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="sendMessageReceiver" name="receiver" required placeholder="输入接收人ERP或群组ID">
                                <small class="form-text text-muted">例如: zhangsan 或 group123</small>
                            </div>
                            <div class="form-group">
                                <label for="sendMessageReceiverType">接收人类型</label>
                                <select class="form-control" id="sendMessageReceiverType" name="receiverType">
                                    <option value="1">用户 (1)</option>
                                    <option value="2">群组 (2)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="sendMessageType">消息类型</label>
                                <select class="form-control" id="sendMessageType" name="messageType">
                                    <option value="1">普通消息 (1)</option>
                                    <option value="2">卡片消息 (2)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="sendMessageContent">消息内容 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="sendMessageContent" name="messageContent" rows="10" required placeholder="输入JSON格式的消息内容"></textarea>
                                <small class="form-text text-muted">请输入JSON格式的消息内容</small>
                            </div>
                            <div class="form-group">
                                <label for="sendMessageRobotId">机器人ID</label>
                                <input type="text" class="form-control" id="sendMessageRobotId" name="robotId" placeholder="输入机器人ID">
                                <small class="form-text text-muted">例如: 00_55e779bf635b4f14 (默认为物流交易小蜜)</small>
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="mt-3">
                            <h6>示例消息内容:</h6>
                            <div class="example-category">
                                <h6 class="example-category-title">文本消息示例</h6>
                                <div class="example-buttons">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="fillTextMessage()">基础文本消息</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="fillPersonalMessage()">个人消息</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="fillGroupAtAllMessage()">群组消息@全员</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="fillGroupAtUserMessage()">群组消息@用户</button>
                                </div>
                            </div>
                            <div class="example-category mt-2">
                                <h6 class="example-category-title">卡片消息示例</h6>
                                <div class="example-buttons">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="fillCardMessage()">基础卡片消息</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="fillImageCardMessage()">图片卡片消息</button>
                                </div>
                            </div>
                        </div>

                        <script>
                            function fillTextMessage() {
                                const example = {
                                    "body": {
                                        "type": "text",
                                        "content": "这是一条测试消息"
                                    }
                                };
                                document.getElementById('sendMessageType').value = '1';
                                document.getElementById('sendMessageContent').value = JSON.stringify(example, null, 2);
                            }

                            function fillPersonalMessage() {
                                const example = {
                                    "body": {
                                        "type": "text",
                                        "content": "这是一条普通的个人测试消息"
                                    }
                                };
                                document.getElementById('sendMessageType').value = '1';
                                document.getElementById('sendMessageContent').value = JSON.stringify(example, null, 2);
                            }

                            function fillGroupAtAllMessage() {
                                const example = {
                                    "body": {
                                        "type": "text",
                                        "content": "@全体成员 这是一条全体成员通知",
                                        "atUsers": [{
                                            "app": "ee",
                                            "pin": "all",
                                            "nickname": "全体成员"
                                        }]
                                    }
                                };
                                document.getElementById('sendMessageType').value = '1';
                                document.getElementById('sendMessageContent').value = JSON.stringify(example, null, 2);
                            }

                            function fillGroupAtUserMessage() {
                                const example = {
                                    "body": {
                                        "type": "text",
                                        "content": "@连大湖 这是一条@消息测试",
                                        "atUsers": [{
                                            "app": "ee",
                                            "pin": "bjliandahu",
                                            "nickname": "连大湖"
                                        }]
                                    }
                                };
                                document.getElementById('sendMessageType').value = '1';
                                document.getElementById('sendMessageContent').value = JSON.stringify(example, null, 2);
                            }

                            function fillCardMessage() {
                                const example = {
                                    "data": {
                                        "templateId": "jdl_card_template",
                                        "sessionType": 1,
                                        "templateType": 1,
                                        "width_mode": "wide",
                                        "reload": false,
                                        "summary": "这是一条卡片消息",
                                        "cardData": {
                                            "title": "测试卡片标题",
                                            "content": "这是卡片内容",
                                            "buttons": [
                                                {
                                                    "text": "查看详情",
                                                    "url": "https://jd.com"
                                                }
                                            ]
                                        }
                                    }
                                };
                                document.getElementById('sendMessageType').value = '2';
                                document.getElementById('sendMessageContent').value = JSON.stringify(example, null, 2);
                            }

                            function fillImageCardMessage() {
                                const example = {
                                  "data": {
                                    "templateId": "templateMsgCard",
                                    "sessionType": 1,
                                    "templateType": 1,
                                    "width_mode": "wide",
                                    "reload": false,
                                    "summary": "",
                                    "cardData": {
                                      "elements": [
                                        {
                                          "preview": true,
                                          "img_url": "https://apijoyspace.jd.com/v1/files/IrFPuO2K5qBbUjGA0LiP/link",
                                          "scale": 2.3,
                                          "tag": "img"
                                        },
                                        {
                                          "tag": "hr"
                                        },
                                        {
                                          "tag": "footer",
                                          "content": "来自MCP 平台"
                                        }
                                      ],
                                      "header": {
                                        "theme": "red",
                                        "title": {
                                          "tag": "plain_text",
                                          "content": "京东价值观"
                                        }
                                      }
                                    },
                                    "forward": {
                                      "reload": false,
                                      "cardData": {
                                        "key": "value"
                                      }
                                    },
                                    "callbackData": {
                                      "key": "value"
                                    },
                                    "at": null
                                  }
                                };
                                document.getElementById('sendMessageType').value = '2';
                                document.getElementById('sendMessageContent').value = JSON.stringify(example, null, 2);
                            }
                        </script>

                        <div class="loading" id="sendMessageLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="sendMessageResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="sendMessageResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="sendMessageJsonTab" data-toggle="tab" href="#sendMessageJsonResult" role="tab" aria-controls="sendMessageJsonResult" aria-selected="true">JSON</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="sendMessageResultTabsContent">
                                <div class="tab-pane fade show active" id="sendMessageJsonResult" role="tabpanel" aria-labelledby="sendMessageJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="sendMessageJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>

<th:block th:fragment="scriptContent">
    <!-- 使用defer属性确保脚本按顺序加载和执行 -->
    <script>
        // 定义全局变量，用于跟踪库的加载状态
        window.librariesLoaded = {
            hljs: false
        };

        // 检查库是否已加载
        function checkLibraries() {
            return window.librariesLoaded.hljs;
        }

        // 初始化库
        function initLibraries() {
            console.log('初始化库...');
            if (typeof hljs !== 'undefined') {
                console.log('highlight.js已加载');
                hljs.configure({
                    ignoreUnescapedHTML: true
                });
                window.librariesLoaded.hljs = true;
            } else {
                console.error('highlight.js未能加载');
            }

            // 触发自定义事件，通知库已加载
            if (checkLibraries()) {
                document.dispatchEvent(new Event('librariesLoaded'));
            }
        }
    </script>

    <!-- 加载本地highlight.js -->
    <script src="/highlight/highlight.min.js"
            onload="window.librariesLoaded.hljs = true; initLibraries();"
            onerror="console.error('本地highlight.js加载失败')"></script>

    <!-- 备用加载脚本 -->
    <script>
        function loadFallbackHighlightJs() {
            console.log('尝试从本地加载highlight.js...');
            var script = document.createElement('script');
            script.src = "/highlight/highlight.min.js";
            script.onload = function() {
                window.librariesLoaded.hljs = true;
                initLibraries();
            };
            script.onerror = function() {
                console.error('本地highlight.js加载失败，尝试使用简单替代方案');
                // 提供一个简单的替代方案
                window.hljs = {
                    highlight: function(code, opts) { return { value: code }; },
                    highlightElement: function() {},
                    highlightAuto: function(code) { return { value: code }; },
                    getLanguage: function() { return null; },
                    configure: function() {}
                };
                window.librariesLoaded.hljs = true;
                initLibraries();
            };
            document.head.appendChild(script);
        }
    </script>

    <!-- 直接加载应用脚本 -->
    <script src="/js/timline-in-base.js"></script>

    <!-- 调试脚本 -->
    <script>
        console.log('页面加载完成，检查按钮状态...');
        setTimeout(function() {
            console.log('延迟检查按钮：', {
                'exampleTextMessage': $('#exampleTextMessage').length,
                'examplePersonalMessage': $('#examplePersonalMessage').length,
                'exampleGroupAtAllMessage': $('#exampleGroupAtAllMessage').length,
                'exampleGroupAtUserMessage': $('#exampleGroupAtUserMessage').length,
                'exampleCardMessage': $('#exampleCardMessage').length
            });

            // 手动绑定点击事件
            $('#exampleTextMessage').on('click', function() {
                console.log('直接绑定的点击事件触发：基础文本消息');
                alert('点击了基础文本消息按钮');
            });
        }, 1000);
    </script>
</th:block>
</body>
</html>
