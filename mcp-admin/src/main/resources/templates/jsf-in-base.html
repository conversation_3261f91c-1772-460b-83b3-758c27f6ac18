<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html(
    title='JSF API 测试 - MCP Platform',
    activeMenu='jsf',
    contentTitle='JSF API 测试',
    breadcrumb='JSF',
    content=~{::content},
    headContent=~{::headContent},
    scriptContent=~{::scriptContent}
)}">
<head>
    <title>JSF API 测试</title>
    <th:block th:fragment="headContent">
        <link rel="stylesheet" href="/highlight/styles/github.min.css">
        <link rel="stylesheet" href="/css/jsf-in-base.css">
    </th:block>
</head>
<body>
<th:block th:fragment="content">
    <div class="container-fluid">
        <div class="alert alert-info" th:if="${server != null and !server.isEmpty()}">
            <h4 th:text="${server.name}">JSF</h4>
            <p th:text="${server.description}">描述</p>
            <p><strong>版本:</strong> <span th:text="${server.version}">1.0.0</span></p>
            <p><strong>状态:</strong> <span th:text="${server.status}">RUNNING</span></p>
        </div>

        <div class="alert alert-warning" th:if="${server == null or server.isEmpty()}">
            <h4>JSF开放平台服务</h4>
            <p>JSF开放平台MCP服务，提供接口查询、方法信息获取、别名查询等功能</p>
            <p><strong>版本:</strong> 1.0.0</p>
            <p><strong>状态:</strong> <span class="text-warning">服务正在初始化中...</span></p>
            <small class="text-muted">如果服务长时间未启动，请检查JSF配置是否正确。</small>
        </div>

        <ul class="nav nav-tabs" id="apiTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" id="interface-info-tab" data-toggle="tab" href="#interface-info" role="tab" aria-controls="interface-info" aria-selected="true">获取接口信息</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="method-info-tab" data-toggle="tab" href="#method-info" role="tab" aria-controls="method-info" aria-selected="false">获取方法信息</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="method-list-tab" data-toggle="tab" href="#method-list" role="tab" aria-controls="method-list" aria-selected="false">获取方法列表</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="alias-list-tab" data-toggle="tab" href="#alias-list" role="tab" aria-controls="alias-list" aria-selected="false">获取别名列表</a>
            </li>
        </ul>

        <div class="tab-content" id="apiTabsContent">
            <!-- 获取接口信息 -->
            <div class="tab-pane fade show active" id="interface-info" role="tabpanel" aria-labelledby="interface-info-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取接口信息 (getInterfaceInfo)</h5>
                    </div>
                    <div class="card-body">
                        <form id="interfaceInfoForm">
                            <div class="form-group">
                                <label for="interfaceInfoInterfaceName">接口名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="interfaceInfoInterfaceName" name="interfaceName" required placeholder="输入JSF接口的完整类名">
                                <small class="form-text text-muted">例如: erp.ql.station.api.service.gangao.CustomsClearanceApi</small>
                            </div>
                            <div class="form-group">
                                <label for="interfaceInfoOperator">操作人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="interfaceInfoOperator" name="operator" required placeholder="输入操作人的ERP账号">
                                <small class="form-text text-muted">例如: testuser</small>
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="interfaceInfoLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="interfaceInfoResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="interfaceInfoResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="interfaceInfoJsonTab" data-toggle="tab" href="#interfaceInfoJsonResult" role="tab" aria-controls="interfaceInfoJsonResult" aria-selected="true">JSON</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="interfaceInfoResultTabsContent">
                                <div class="tab-pane fade show active" id="interfaceInfoJsonResult" role="tabpanel" aria-labelledby="interfaceInfoJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="interfaceInfoJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 获取方法信息 -->
            <div class="tab-pane fade" id="method-info" role="tabpanel" aria-labelledby="method-info-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取方法信息 (getMethodInfo)</h5>
                    </div>
                    <div class="card-body">
                        <form id="methodInfoForm">
                            <div class="form-group">
                                <label for="methodInfoInterfaceName">接口名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="methodInfoInterfaceName" name="interfaceName" required placeholder="输入JSF接口的完整类名">
                                <small class="form-text text-muted">例如: erp.ql.station.api.service.gangao.CustomsClearanceApi</small>
                            </div>
                            <div class="form-group">
                                <label for="methodInfoMethodName">方法名称</label>
                                <input type="text" class="form-control" id="methodInfoMethodName" name="methodName" placeholder="输入方法名称（可选）">
                                <small class="form-text text-muted">如果为空则返回所有方法信息</small>
                            </div>
                            <div class="form-group">
                                <label for="methodInfoAlias">别名</label>
                                <input type="text" class="form-control" id="methodInfoAlias" name="alias" placeholder="输入别名（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodInfoIp">IP地址</label>
                                <input type="text" class="form-control" id="methodInfoIp" name="ip" placeholder="输入Provider的IP地址（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodInfoPort">端口号</label>
                                <input type="number" class="form-control" id="methodInfoPort" name="port" placeholder="输入Provider的端口号（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodInfoOperator">操作人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="methodInfoOperator" name="operator" required placeholder="输入操作人的ERP账号">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="methodInfoLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="methodInfoResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="methodInfoResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="methodInfoJsonTab" data-toggle="tab" href="#methodInfoJsonResult" role="tab" aria-controls="methodInfoJsonResult" aria-selected="true">JSON</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="methodInfoResultTabsContent">
                                <div class="tab-pane fade show active" id="methodInfoJsonResult" role="tabpanel" aria-labelledby="methodInfoJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="methodInfoJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 获取方法列表 -->
            <div class="tab-pane fade" id="method-list" role="tabpanel" aria-labelledby="method-list-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取方法列表 (getMethodList)</h5>
                    </div>
                    <div class="card-body">
                        <form id="methodListForm">
                            <div class="form-group">
                                <label for="methodListInterfaceName">接口名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="methodListInterfaceName" name="interfaceName" required placeholder="输入JSF接口的完整类名">
                                <small class="form-text text-muted">例如: erp.ql.station.api.service.gangao.CustomsClearanceApi</small>
                            </div>
                            <div class="form-group">
                                <label for="methodListAlias">别名</label>
                                <input type="text" class="form-control" id="methodListAlias" name="alias" placeholder="输入别名（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodListIp">IP地址</label>
                                <input type="text" class="form-control" id="methodListIp" name="ip" placeholder="输入Provider的IP地址（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodListPort">端口号</label>
                                <input type="number" class="form-control" id="methodListPort" name="port" placeholder="输入Provider的端口号（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodListOperator">操作人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="methodListOperator" name="operator" required placeholder="输入操作人的ERP账号">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="methodListLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="methodListResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="methodListResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="methodListJsonTab" data-toggle="tab" href="#methodListJsonResult" role="tab" aria-controls="methodListJsonResult" aria-selected="true">JSON</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="methodListResultTabsContent">
                                <div class="tab-pane fade show active" id="methodListJsonResult" role="tabpanel" aria-labelledby="methodListJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="methodListJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 获取别名列表 -->
            <div class="tab-pane fade" id="alias-list" role="tabpanel" aria-labelledby="alias-list-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取别名列表 (getAliasByInterfaceName)</h5>
                    </div>
                    <div class="card-body">
                        <form id="aliasListForm">
                            <div class="form-group">
                                <label for="aliasListInterfaceName">接口名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="aliasListInterfaceName" name="interfaceName" required placeholder="输入JSF接口的完整类名">
                                <small class="form-text text-muted">例如: erp.ql.station.api.service.gangao.CustomsClearanceApi</small>
                            </div>
                            <div class="form-group">
                                <label for="aliasListOperator">操作人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="aliasListOperator" name="operator" required placeholder="输入操作人的ERP账号">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="aliasListLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="aliasListResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="aliasListResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="aliasListJsonTab" data-toggle="tab" href="#aliasListJsonResult" role="tab" aria-controls="aliasListJsonResult" aria-selected="true">JSON</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="aliasListResultTabsContent">
                                <div class="tab-pane fade show active" id="aliasListJsonResult" role="tabpanel" aria-labelledby="aliasListJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="aliasListJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>

<th:block th:fragment="scriptContent">
    <!-- 使用defer属性确保脚本按顺序加载和执行 -->
    <script>
        // 定义全局变量，用于跟踪库的加载状态
        window.librariesLoaded = {
            hljs: false
        };

        // 检查库是否已加载
        function checkLibraries() {
            return window.librariesLoaded.hljs;
        }

        // 初始化库
        function initLibraries() {
            console.log('初始化库...');
            if (typeof hljs !== 'undefined') {
                console.log('highlight.js已加载');
                hljs.configure({
                    ignoreUnescapedHTML: true
                });
                window.librariesLoaded.hljs = true;
            } else {
                console.error('highlight.js未能加载');
            }

            // 触发自定义事件，通知库已加载
            if (checkLibraries()) {
                document.dispatchEvent(new Event('librariesLoaded'));
            }
        }
    </script>

    <!-- 加载本地highlight.js -->
    <script src="/highlight/highlight.min.js"
            onload="window.librariesLoaded.hljs = true; initLibraries();"
            onerror="console.error('本地highlight.js加载失败')"></script>

    <!-- 等待库加载完成后再加载应用脚本 -->
    <script>
        // 如果库已加载，直接加载应用脚本
        if (checkLibraries()) {
            loadAppScript();
        } else {
            // 否则等待库加载完成
            document.addEventListener('librariesLoaded', loadAppScript);

            // 设置超时，防止无限等待
            setTimeout(function() {
                if (!checkLibraries()) {
                    console.warn('库加载超时，尝试继续加载应用脚本');
                    loadAppScript();
                }
            }, 2000);
        }

        function loadAppScript() {
            console.log('加载应用脚本...');
            var script = document.createElement('script');
            script.src = '/js/jsf-in-base.js';
            document.body.appendChild(script);
        }
    </script>
</th:block>
</body>
</html>
