<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html(
    title='JSF API 测试 - MCP Platform',
    activeMenu='jsf',
    contentTitle='JSF API 测试',
    breadcrumb='JSF',
    content=~{::content},
    headContent=~{::headContent},
    scriptContent=~{::scriptContent}
)}">
<head>
    <title>JSF API 测试</title>
    <th:block th:fragment="headContent">
        <link rel="stylesheet" href="/highlight/styles/github.min.css">
        <link rel="stylesheet" href="/css/jsf-in-base.css">
    </th:block>
</head>
<body>
<th:block th:fragment="content">
    <div class="container-fluid">
        <div class="alert alert-info" th:if="${server != null and !server.isEmpty()}">
            <h4 th:text="${server.name}">JSF</h4>
            <p th:text="${server.description}">描述</p>
            <p><strong>版本:</strong> <span th:text="${server.version}">1.0.0</span></p>
            <p><strong>状态:</strong> <span th:text="${server.status}">RUNNING</span></p>
        </div>

        <div class="alert alert-warning" th:if="${server == null or server.isEmpty()}">
            <h4>JSF开放平台服务</h4>
            <p>JSF开放平台MCP服务，提供接口查询、方法信息获取、别名查询等功能</p>
            <p><strong>版本:</strong> 1.0.0</p>
            <p><strong>状态:</strong> <span class="text-warning">服务正在初始化中...</span></p>
            <small class="text-muted">如果服务长时间未启动，请检查JSF配置是否正确。</small>
        </div>

        <ul class="nav nav-tabs" id="apiTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" id="interface-info-tab" data-toggle="tab" href="#interface-info" role="tab" aria-controls="interface-info" aria-selected="true">获取接口信息</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="method-info-tab" data-toggle="tab" href="#method-info" role="tab" aria-controls="method-info" aria-selected="false">获取方法信息</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="method-list-tab" data-toggle="tab" href="#method-list" role="tab" aria-controls="method-list" aria-selected="false">获取方法列表</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="alias-list-tab" data-toggle="tab" href="#alias-list" role="tab" aria-controls="alias-list" aria-selected="false">获取别名列表</a>
            </li>
        </ul>

        <div class="tab-content" id="apiTabsContent">
            <!-- 获取接口信息 -->
            <div class="tab-pane fade show active" id="interface-info" role="tabpanel" aria-labelledby="interface-info-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取接口信息 (getInterfaceInfo)</h5>
                    </div>
                    <div class="card-body">
                        <form id="interfaceInfoForm">
                            <div class="form-group">
                                <label for="interfaceInfoInterfaceName">接口名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="interfaceInfoInterfaceName" name="interfaceName" required placeholder="输入JSF接口的完整类名">
                                <small class="form-text text-muted">例如: erp.ql.station.api.service.gangao.CustomsClearanceApi</small>
                            </div>
                            <div class="form-group">
                                <label for="interfaceInfoOperator">操作人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="interfaceInfoOperator" name="operator" required placeholder="输入操作人的ERP账号">
                                <small class="form-text text-muted">例如: testuser</small>
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="interfaceInfoLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="interfaceInfoResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="interfaceInfoResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="interfaceInfoJsonTab" data-toggle="tab" href="#interfaceInfoJsonResult" role="tab" aria-controls="interfaceInfoJsonResult" aria-selected="true">JSON</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="interfaceInfoResultTabsContent">
                                <div class="tab-pane fade show active" id="interfaceInfoJsonResult" role="tabpanel" aria-labelledby="interfaceInfoJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="interfaceInfoJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 获取方法信息 -->
            <div class="tab-pane fade" id="method-info" role="tabpanel" aria-labelledby="method-info-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取方法信息 (getMethodInfo)</h5>
                    </div>
                    <div class="card-body">
                        <form id="methodInfoForm">
                            <div class="form-group">
                                <label for="methodInfoInterfaceName">接口名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="methodInfoInterfaceName" name="interfaceName" required placeholder="输入JSF接口的完整类名">
                                <small class="form-text text-muted">例如: erp.ql.station.api.service.gangao.CustomsClearanceApi</small>
                            </div>
                            <div class="form-group">
                                <label for="methodInfoMethodName">方法名称</label>
                                <input type="text" class="form-control" id="methodInfoMethodName" name="methodName" placeholder="输入方法名称（可选）">
                                <small class="form-text text-muted">如果为空则返回所有方法信息</small>
                            </div>
                            <div class="form-group">
                                <label for="methodInfoAlias">别名</label>
                                <input type="text" class="form-control" id="methodInfoAlias" name="alias" placeholder="输入别名（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodInfoIp">IP地址</label>
                                <input type="text" class="form-control" id="methodInfoIp" name="ip" placeholder="输入Provider的IP地址（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodInfoPort">端口号</label>
                                <input type="number" class="form-control" id="methodInfoPort" name="port" placeholder="输入Provider的端口号（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodInfoOperator">操作人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="methodInfoOperator" name="operator" required placeholder="输入操作人的ERP账号">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="methodInfoLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="methodInfoResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="methodInfoResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="methodInfoJsonTab" data-toggle="tab" href="#methodInfoJsonResult" role="tab" aria-controls="methodInfoJsonResult" aria-selected="true">JSON</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="methodInfoResultTabsContent">
                                <div class="tab-pane fade show active" id="methodInfoJsonResult" role="tabpanel" aria-labelledby="methodInfoJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="methodInfoJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 获取方法列表 -->
            <div class="tab-pane fade" id="method-list" role="tabpanel" aria-labelledby="method-list-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取方法列表 (getMethodList)</h5>
                    </div>
                    <div class="card-body">
                        <form id="methodListForm">
                            <div class="form-group">
                                <label for="methodListInterfaceName">接口名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="methodListInterfaceName" name="interfaceName" required placeholder="输入JSF接口的完整类名">
                                <small class="form-text text-muted">例如: erp.ql.station.api.service.gangao.CustomsClearanceApi</small>
                            </div>
                            <div class="form-group">
                                <label for="methodListAlias">别名</label>
                                <input type="text" class="form-control" id="methodListAlias" name="alias" placeholder="输入别名（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodListIp">IP地址</label>
                                <input type="text" class="form-control" id="methodListIp" name="ip" placeholder="输入Provider的IP地址（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodListPort">端口号</label>
                                <input type="number" class="form-control" id="methodListPort" name="port" placeholder="输入Provider的端口号（可选）">
                            </div>
                            <div class="form-group">
                                <label for="methodListOperator">操作人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="methodListOperator" name="operator" required placeholder="输入操作人的ERP账号">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="methodListLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="methodListResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="methodListResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="methodListJsonTab" data-toggle="tab" href="#methodListJsonResult" role="tab" aria-controls="methodListJsonResult" aria-selected="true">JSON</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="methodListResultTabsContent">
                                <div class="tab-pane fade show active" id="methodListJsonResult" role="tabpanel" aria-labelledby="methodListJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="methodListJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 获取别名列表 -->
            <div class="tab-pane fade" id="alias-list" role="tabpanel" aria-labelledby="alias-list-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取别名列表 (getAliasByInterfaceName)</h5>
                    </div>
                    <div class="card-body">
                        <form id="aliasListForm">
                            <div class="form-group">
                                <label for="aliasListInterfaceName">接口名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="aliasListInterfaceName" name="interfaceName" required placeholder="输入JSF接口的完整类名">
                                <small class="form-text text-muted">例如: erp.ql.station.api.service.gangao.CustomsClearanceApi</small>
                            </div>
                            <div class="form-group">
                                <label for="aliasListOperator">操作人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="aliasListOperator" name="operator" required placeholder="输入操作人的ERP账号">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="aliasListLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="aliasListResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="aliasListResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="aliasListJsonTab" data-toggle="tab" href="#aliasListJsonResult" role="tab" aria-controls="aliasListJsonResult" aria-selected="true">JSON</a>
                                </li>
                            </ul>
                            <div class="tab-content" id="aliasListResultTabsContent">
                                <div class="tab-pane fade show active" id="aliasListJsonResult" role="tabpanel" aria-labelledby="aliasListJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="aliasListJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>

<th:block th:fragment="scriptContent">
    <!-- 使用defer属性确保脚本按顺序加载和执行 -->
    <script>
        // 定义全局变量，用于跟踪库的加载状态
        window.librariesLoaded = {
            hljs: false
        };

        // 检查库是否已加载
        function checkLibraries() {
            return window.librariesLoaded.hljs;
        }

        // 初始化库
        function initLibraries() {
            console.log('初始化库...');
            console.log('页面脚本开始执行');
            if (typeof hljs !== 'undefined') {
                console.log('highlight.js已加载');
                hljs.configure({
                    ignoreUnescapedHTML: true
                });
                window.librariesLoaded.hljs = true;
            } else {
                console.error('highlight.js未能加载');
            }

            // 触发自定义事件，通知库已加载
            if (checkLibraries()) {
                document.dispatchEvent(new Event('librariesLoaded'));
            }
        }
    </script>

    <!-- 加载本地highlight.js -->
    <script src="/highlight/highlight.min.js"
            onload="window.librariesLoaded.hljs = true; initLibraries();"
            onerror="console.error('本地highlight.js加载失败')"></script>

    <!-- 等待库加载完成后再加载应用脚本 -->
    <script>
        // 如果库已加载，直接加载应用脚本
        if (checkLibraries()) {
            loadAppScript();
        } else {
            // 否则等待库加载完成
            document.addEventListener('librariesLoaded', loadAppScript);

            // 设置超时，防止无限等待
            setTimeout(function() {
                if (!checkLibraries()) {
                    console.warn('库加载超时，尝试继续加载应用脚本');
                    loadAppScript();
                }
            }, 2000);
        }

        function loadAppScript() {
            console.log('加载应用脚本...');

            // 直接在页面中嵌入JavaScript代码进行测试
            console.log('开始初始化JSF页面功能');

            // 测试jQuery是否可用
            if (typeof $ !== 'undefined') {
                console.log('jQuery可用');

                // 直接在这里实现方法选择功能
                $(document).ready(function() {
                    console.log('jQuery ready事件触发');

                    // 获取方法列表的处理
                    $('#methodListForm').submit(function(e) {
                        e.preventDefault();
                        console.log('方法列表表单提交');
                        const formData = $(this).serialize();

                        $('#methodListLoading').show();
                        $('#methodListResult').hide();

                        $.ajax({
                            url: '/jsf/getMethodList',
                            type: 'POST',
                            data: formData,
                            success: function(response) {
                                console.log('方法列表响应:', response);
                                displayResult(response, '#methodListJsonContent', '#methodListResult');

                                // 如果成功获取方法列表，创建下拉菜单
                                if (response.success && response.data) {
                                    console.log('开始处理方法列表数据');
                                    try {
                                        const formattedResponse = formatJsonData(response);
                                        console.log('格式化后的响应:', formattedResponse);
                                        if (formattedResponse.data && Array.isArray(formattedResponse.data)) {
                                            console.log('创建方法下拉菜单，数据:', formattedResponse.data);
                                            createMethodDropdown(formattedResponse.data);
                                        }
                                    } catch (e) {
                                        console.error('处理方法列表失败:', e);
                                    }
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('方法列表请求失败:', error);
                                $('#methodListJsonContent').text('Error: ' + error);
                                $('#methodListResult').show();
                            },
                            complete: function() {
                                $('#methodListLoading').hide();
                            }
                        });
                    });

                    // 创建方法下拉菜单函数
                    function createMethodDropdown(methods) {
                        console.log('createMethodDropdown被调用，方法数量:', methods.length);

                        // 移除已存在的下拉菜单
                        $('.method-dropdown').remove();

                        // 创建下拉菜单
                        const dropdown = $('<div class="mt-3 method-dropdown"><label>选择方法:</label><select class="form-control method-select"></select></div>');
                        const select = dropdown.find('select');

                        // 添加空选项
                        select.append('<option value="">请选择方法</option>');

                        // 添加选项
                        methods.forEach(method => {
                            console.log('添加方法选项:', method);
                            select.append(`<option value="${method}">${method}</option>`);
                        });

                        // 添加事件监听
                        select.on('change', function() {
                            console.log('方法选择发生变化');
                            const methodSignature = $(this).val();
                            console.log('选择的方法签名:', methodSignature);
                            if (methodSignature) {
                                // 从完整的方法签名中提取方法名
                                const methodName = extractMethodName(methodSignature);
                                console.log('提取的方法名:', methodName);
                                // 自动填充到方法信息表单
                                $('#methodInfoMethodName').val(methodName);
                                console.log('已填充到输入框');
                            }
                        });

                        // 添加到页面
                        $('#methodListResult').after(dropdown);
                        console.log('下拉菜单已添加到页面');
                    }

                    // 从完整的方法签名中提取方法名
                    function extractMethodName(methodSignature) {
                        console.log('提取方法名，输入:', methodSignature);
                        if (!methodSignature) return '';

                        // 找到最后一个空格和第一个左括号之间的内容
                        const lastSpaceIndex = methodSignature.lastIndexOf(' ');
                        const firstParenIndex = methodSignature.indexOf('(');

                        console.log('最后一个空格位置:', lastSpaceIndex);
                        console.log('第一个左括号位置:', firstParenIndex);

                        if (lastSpaceIndex !== -1 && firstParenIndex !== -1 && lastSpaceIndex < firstParenIndex) {
                            const methodName = methodSignature.substring(lastSpaceIndex + 1, firstParenIndex);
                            console.log('提取的方法名:', methodName);
                            return methodName;
                        }

                        // 如果没有找到空格，可能是简单的方法名
                        if (firstParenIndex !== -1) {
                            const methodName = methodSignature.substring(0, firstParenIndex);
                            console.log('简单方法名:', methodName);
                            return methodName;
                        }

                        // 兜底返回原字符串
                        console.log('兜底返回:', methodSignature);
                        return methodSignature;
                    }
                });

            } else {
                console.error('jQuery不可用');
            }
        }
    </script>
</th:block>
</body>
</html>
