<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Joyspace 服务详情</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        .card {
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .tool-item {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .tool-item h5 {
            margin-top: 0;
            margin-bottom: 10px;
        }
        .tool-item p {
            margin-bottom: 10px;
        }
        .tool-item .badge {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/servers">服务列表</a></li>
                <li class="breadcrumb-item active" aria-current="page">Joyspace 服务</li>
            </ol>
        </nav>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0" th:text="${server.name}">Joyspace</h3>
                        <span class="badge bg-success" th:if="${server.status == 'RUNNING'}">运行中</span>
                        <span class="badge bg-warning" th:if="${server.status == 'STARTING'}">启动中</span>
                        <span class="badge bg-danger" th:if="${server.status == 'STOPPED'}">已停止</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text" th:text="${server.description}">描述</p>
                        <p><strong>版本:</strong> <span th:text="${server.version}">1.0.0</span></p>

                        <div class="mt-4" th:if="${testPageUrl != null}">
                            <a th:href="${testPageUrl}" class="btn btn-primary">
                                <i class="bi bi-tools"></i> 打开测试页面
                            </a>
                            <p class="text-muted mt-2">
                                测试页面提供了友好的界面，用于测试 Joyspace 服务的各种功能，包括获取文件夹列表、文件列表、页面信息和页面内容等。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4>可用工具</h4>
                    </div>
                    <div class="card-body">
                        <div class="tool-item">
                            <h5>获取文件夹列表 (getFolderList)</h5>
                            <p>获取指定文件夹下的所有子文件夹列表</p>
                            <div>
                                <span class="badge bg-primary">参数: folderurl</span>
                                <span class="badge bg-secondary">可选参数: sort</span>
                            </div>
                        </div>

                        <div class="tool-item">
                            <h5>获取文件列表 (getFileList)</h5>
                            <p>获取指定文件夹下的所有文件列表</p>
                            <div>
                                <span class="badge bg-primary">参数: folderurl</span>
                                <span class="badge bg-secondary">可选参数: sort, start, length</span>
                            </div>
                        </div>

                        <div class="tool-item">
                            <h5>获取页面信息 (getPageInfo)</h5>
                            <p>获取指定页面的基本信息</p>
                            <div>
                                <span class="badge bg-primary">参数: pageurl</span>
                            </div>
                        </div>

                        <div class="tool-item">
                            <h5>获取页面内容 (getPageContent)</h5>
                            <p>获取指定页面的详细内容，包括JSON和Markdown格式</p>
                            <div>
                                <span class="badge bg-primary">参数: pageurl</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4>API 使用示例</h4>
                    </div>
                    <div class="card-body">
                        <pre><code class="language-bash">
# 获取文件夹列表
curl -X 'POST' \
  'http://localhost:8080/api/v1/joyspace/getFolderList' \
  -H 'Content-Type: application/json' \
  -d '{
  "folderurl": "b10vXr-LbPQ60n1pb7D3"
}'

# 获取文件列表
curl -X 'POST' \
  'http://localhost:8080/api/v1/joyspace/getFileList' \
  -H 'Content-Type: application/json' \
  -d '{
  "folderurl": "b10vXr-LbPQ60n1pb7D3",
  "sort": "-updated_at"
}'

# 获取页面信息
curl -X 'POST' \
  'http://localhost:8080/api/v1/joyspace/getPageInfo' \
  -H 'Content-Type: application/json' \
  -d '{
  "pageurl": "mabzQBOJoTpWbiQy3L3O"
}'

# 获取页面内容
curl -X 'POST' \
  'http://localhost:8080/api/v1/joyspace/getPageContent' \
  -H 'Content-Type: application/json' \
  -d '{
  "pageurl": "mabzQBOJoTpWbiQy3L3O"
}'
                        </code></pre>
                        <p class="text-muted mt-2">
                            注意: 请将示例中的 URL 参数替换为实际的文件夹 ID 或页面 ID。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/highlight/highlight.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });
        });
    </script>
</body>
</html>
