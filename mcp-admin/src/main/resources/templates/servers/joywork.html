<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Joywork 服务详情</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        .card { margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .tool-item { margin-bottom: 15px; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; background-color: #f8f9fa; }
        .tool-item h5 { margin-top: 0; margin-bottom: 10px; }
        .tool-item p { margin-bottom: 10px; }
        .tool-item .badge { margin-right: 5px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/servers">服务列表</a></li>
                <li class="breadcrumb-item active" aria-current="page">Joywork 服务</li>
            </ol>
        </nav>
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0" th:text="${server.name}">Joywork</h3>
                        <span class="badge bg-success" th:if="${server.status == 'RUNNING'}">运行中</span>
                        <span class="badge bg-warning" th:if="${server.status == 'STARTING'}">启动中</span>
                        <span class="badge bg-danger" th:if="${server.status == 'STOPPED'}">已停止</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text" th:text="${server.description}">通过待办服务，管理待办任务</p>
                        <p><strong>版本:</strong> <span th:text="${server.version}">1.0.0</span></p>
                        <div class="mt-4" th:if="${testPageUrl != null}">
                            <a th:href="${testPageUrl}" class="btn btn-primary">
                                <i class="bi bi-tools"></i> 打开测试页面
                            </a>
                            <p class="text-muted mt-2">测试页面提供了友好的界面，用于测试 Joywork 服务的各种功能，包括创建、更新、催办等。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header"><h4>可用工具</h4></div>
                    <div class="card-body">
                        <div class="tool-item">
                            <h5>创建待办任务 (createTask)</h5>
                            <p>创建新的待办任务</p>
                            <div>
                                <span class="badge bg-primary">参数: title, remark, starttime, endtime, executor</span>
                            </div>
                        </div>
                        <div class="tool-item">
                            <h5>更新待办任务 (updateTask)</h5>
                            <p>更新已存在的待办任务</p>
                            <div>
                                <span class="badge bg-primary">参数: taskid, title, remark, starttime, endtime</span>
                            </div>
                        </div>
                        <div class="tool-item">
                            <h5>更新任务状态 (updateTaskStatus)</h5>
                            <p>更新待办任务的完成状态</p>
                            <div>
                                <span class="badge bg-primary">参数: taskid, taskstatus</span>
                            </div>
                        </div>
                        <div class="tool-item">
                            <h5>催办任务 (urgeTask)</h5>
                            <p>对指定任务进行催办</p>
                            <div>
                                <span class="badge bg-primary">参数: taskid, urgecontent, taskusers</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header"><h4>API 使用示例</h4></div>
                    <div class="card-body">
                        <pre><code class="language-bash">
# 创建待办任务
curl -X POST 'http://localhost:8080/joywork/createTask' -d 'title=交易618备战-硬件扩容&remark=请在5月10日廊坊第一次军演之前完成硬件扩容&starttime=2025-05-10 10:00:00&endtime=2025-05-11 10:00:00&executor=bjliandahu,wangwangang'

# 更新待办任务
curl -X POST 'http://localhost:8080/joywork/updateTask' -d 'taskid=1234567890&title=交易618备战-硬件扩容&remark=请在5月10日廊坊第一次军演之前完成硬件扩容&starttime=2025-05-10 10:00:00&endtime=2025-05-11 10:00:00'

# 更新任务状态
curl -X POST 'http://localhost:8080/joywork/updateTaskStatus' -d 'taskid=1234567890&taskstatus=2'

# 催办任务
curl -X POST 'http://localhost:8080/joywork/urgeTask' -d 'taskid=1234567890&urgecontent=请尽快完成任务&taskusers=bjliandahu,wangwangang'
                        </code></pre>
                        <p class="text-muted mt-2">注意: 日期时间参数格式为 yyyy-MM-dd HH:mm:ss，所有参数均为字符串。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/highlight/highlight.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });
        });
    </script>
</body>
</html> 