<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Joyspace API 测试页面</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="/highlight/styles/github.min.css">
    <link rel="stylesheet" href="/css/joyspace-test.css">
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Joyspace API 测试页面</h1>

        <div class="alert alert-info" th:if="${server != null}">
            <h4 th:text="${server.name}">Joyspace</h4>
            <p th:text="${server.description}">描述</p>
            <p><strong>版本:</strong> <span th:text="${server.version}">1.0.0</span></p>
            <p><strong>状态:</strong> <span th:text="${server.status}">RUNNING</span></p>
        </div>

        <div class="alert alert-danger" th:if="${server == null}">
            无法获取Joyspace服务信息，请确保服务已启动。
        </div>

        <ul class="nav nav-tabs" id="apiTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="folder-list-tab" data-bs-toggle="tab" data-bs-target="#folder-list" type="button" role="tab" aria-controls="folder-list" aria-selected="true">获取文件夹列表</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="file-list-tab" data-bs-toggle="tab" data-bs-target="#file-list" type="button" role="tab" aria-controls="file-list" aria-selected="false">获取文件列表</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="page-info-tab" data-bs-toggle="tab" data-bs-target="#page-info" type="button" role="tab" aria-controls="page-info" aria-selected="false">获取页面信息</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="page-content-tab" data-bs-toggle="tab" data-bs-target="#page-content" type="button" role="tab" aria-controls="page-content" aria-selected="false">获取页面内容</button>
            </li>
        </ul>

        <div class="tab-content" id="apiTabsContent">
            <!-- 获取文件夹列表 -->
            <div class="tab-pane fade show active" id="folder-list" role="tabpanel" aria-labelledby="folder-list-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取文件夹列表 (getFolderList)</h5>
                    </div>
                    <div class="card-body">
                        <form id="folderListForm">
                            <div class="form-group">
                                <label for="folderListFolderUrl">文件夹URL <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="folderListFolderUrl" name="folderurl" required placeholder="输入文件夹URL">
                                <small class="form-text text-muted">例如: root</small>
                            </div>
                            <div class="form-group">
                                <label for="folderListSort">排序方式</label>
                                <select class="form-control" id="folderListSort" name="sort">
                                    <option value="">默认排序</option>
                                    <option value="updated_at">更新时间升序</option>
                                    <option value="-updated_at">更新时间降序</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="folderListLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="folderListResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="folderListResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="folderListJsonTab" data-bs-toggle="tab" data-bs-target="#folderListJsonResult" type="button" role="tab" aria-controls="folderListJsonResult" aria-selected="true">JSON</button>
                                </li>
                            </ul>
                            <div class="tab-content" id="folderListResultTabsContent">
                                <div class="tab-pane fade show active" id="folderListJsonResult" role="tabpanel" aria-labelledby="folderListJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="folderListJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 获取文件列表 -->
            <div class="tab-pane fade" id="file-list" role="tabpanel" aria-labelledby="file-list-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取文件列表 (getFileList)</h5>
                    </div>
                    <div class="card-body">
                        <form id="fileListForm">
                            <div class="form-group">
                                <label for="fileListFolderUrl">文件夹URL <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="fileListFolderUrl" name="folderurl" required placeholder="输入文件夹URL">
                                <small class="form-text text-muted">例如: 5Tx3IbXgzSGhwEpnG7kK</small>
                            </div>
                            <div class="form-group">
                                <label for="fileListSort">排序方式</label>
                                <select class="form-control" id="fileListSort" name="sort">
                                    <option value="">默认排序</option>
                                    <option value="updated_at">更新时间升序</option>
                                    <option value="-updated_at">更新时间降序</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="fileListStart">起始位置</label>
                                <input type="number" class="form-control" id="fileListStart" name="start" placeholder="默认为0">
                            </div>
                            <div class="form-group">
                                <label for="fileListLength">长度</label>
                                <input type="number" class="form-control" id="fileListLength" name="length" placeholder="默认为100">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="fileListLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="fileListResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="fileListResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="fileListJsonTab" data-bs-toggle="tab" data-bs-target="#fileListJsonResult" type="button" role="tab" aria-controls="fileListJsonResult" aria-selected="true">JSON</button>
                                </li>
                            </ul>
                            <div class="tab-content" id="fileListResultTabsContent">
                                <div class="tab-pane fade show active" id="fileListJsonResult" role="tabpanel" aria-labelledby="fileListJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="fileListJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 获取页面信息 -->
            <div class="tab-pane fade" id="page-info" role="tabpanel" aria-labelledby="page-info-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取页面信息 (getPageInfo)</h5>
                    </div>
                    <div class="card-body">
                        <form id="pageInfoForm">
                            <div class="form-group">
                                <label for="pageInfoPageUrl">页面URL <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="pageInfoPageUrl" name="pageurl" required placeholder="输入页面URL">
                                <small class="form-text text-muted">例如: "mabzQBOJoTpWbiQy3L3O", "0suFsDfjSeJGlxYIPMIH"</small>
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="pageInfoLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="pageInfoResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="pageInfoResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="pageInfoJsonTab" data-bs-toggle="tab" data-bs-target="#pageInfoJsonResult" type="button" role="tab" aria-controls="pageInfoJsonResult" aria-selected="true">JSON</button>
                                </li>
                            </ul>
                            <div class="tab-content" id="pageInfoResultTabsContent">
                                <div class="tab-pane fade show active" id="pageInfoJsonResult" role="tabpanel" aria-labelledby="pageInfoJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="pageInfoJsonContent"></code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 获取页面内容 -->
            <div class="tab-pane fade" id="page-content" role="tabpanel" aria-labelledby="page-content-tab">
                <div class="card">
                    <div class="card-header">
                        <h5>获取页面内容 (getPageContent)</h5>
                    </div>
                    <div class="card-body">
                        <form id="pageContentForm">
                            <div class="form-group">
                                <label for="pageContentPageUrl">页面URL <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="pageContentPageUrl" name="pageurl" required placeholder="输入页面URL">
                                <small class="form-text text-muted">例如: "mabzQBOJoTpWbiQy3L3O", "0suFsDfjSeJGlxYIPMIH"</small>
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>

                        <div class="loading" id="pageContentLoading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">请求处理中...</p>
                        </div>

                        <div class="mt-4" id="pageContentResult" style="display: none;">
                            <h5>执行结果</h5>
                            <ul class="nav nav-tabs" id="pageContentResultTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="pageContentJsonTab" data-bs-toggle="tab" data-bs-target="#pageContentJsonResult" type="button" role="tab" aria-controls="pageContentJsonResult" aria-selected="true">JSON</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="pageContentMarkdownTab" data-bs-toggle="tab" data-bs-target="#pageContentMarkdownResult" type="button" role="tab" aria-controls="pageContentMarkdownResult" aria-selected="false">Markdown</button>
                                </li>
                            </ul>
                            <div class="tab-content" id="pageContentResultTabsContent">
                                <div class="tab-pane fade show active" id="pageContentJsonResult" role="tabpanel" aria-labelledby="pageContentJsonTab">
                                    <div class="result-container">
                                        <pre><code class="language-json" id="pageContentJsonContent"></code></pre>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="pageContentMarkdownResult" role="tabpanel" aria-labelledby="pageContentMarkdownTab">
                                    <div class="markdown-content" id="pageContentMarkdownContent"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 使用defer属性确保脚本按顺序加载和执行 -->
    <script>
        // 定义全局变量，用于跟踪库的加载状态
        window.librariesLoaded = {
            hljs: false,
            marked: false
        };

        // 检查库是否已加载
        function checkLibraries() {
            return window.librariesLoaded.hljs && window.librariesLoaded.marked;
        }

        // 初始化库
        function initLibraries() {
            console.log('初始化库...');
            if (typeof hljs !== 'undefined') {
                console.log('highlight.js已加载');
                hljs.configure({
                    ignoreUnescapedHTML: true
                });
                window.librariesLoaded.hljs = true;
            } else {
                console.error('highlight.js未能加载');
            }

            if (typeof marked !== 'undefined') {
                console.log('marked已加载');
                window.librariesLoaded.marked = true;
            } else {
                console.error('marked未能加载');
            }

            // 触发自定义事件，通知库已加载
            if (checkLibraries()) {
                document.dispatchEvent(new Event('librariesLoaded'));
            }
        }
    </script>

    <!-- 加载本地highlight.js -->
    <script src="/highlight/highlight.min.js"
            onload="window.librariesLoaded.hljs = true; initLibraries();"
            onerror="console.error('本地highlight.js加载失败')"></script>

    <!-- 加载marked (使用unpkg作为备用CDN) -->
    <script src="https://unpkg.com/marked@4.0.0/marked.min.js"
            onload="window.librariesLoaded.marked = true; initLibraries();"
            onerror="loadFallbackMarked()"></script>

    <!-- 备用加载脚本 -->
    <script>
        function loadFallbackHighlightJs() {
            console.log('尝试从本地加载highlight.js...');
            var script = document.createElement('script');
            script.src = "/highlight/highlight.min.js";
            script.onload = function() {
                window.librariesLoaded.hljs = true;
                initLibraries();
            };
            script.onerror = function() {
                console.error('本地highlight.js加载失败，尝试使用简单替代方案');
                // 提供一个简单的替代方案
                window.hljs = {
                    highlight: function(code, opts) { return { value: code }; },
                    highlightElement: function() {},
                    highlightAuto: function(code) { return { value: code }; },
                    getLanguage: function() { return null; },
                    configure: function() {}
                };
                window.librariesLoaded.hljs = true;
                initLibraries();
            };
            document.head.appendChild(script);
        }

        function loadFallbackMarked() {
            console.log('尝试从备用CDN加载marked...');
            var script = document.createElement('script');
            script.src = "https://cdnjs.cloudflare.com/ajax/libs/marked/4.0.0/marked.min.js";
            script.onload = function() {
                window.librariesLoaded.marked = true;
                initLibraries();
            };
            script.onerror = function() {
                console.error('所有marked CDN加载失败，尝试使用简单替代方案');
                // 提供一个简单的替代方案
                window.marked = {
                    parse: function(text) { return '<pre>' + text + '</pre>'; },
                    setOptions: function() {},
                    Renderer: function() {}
                };
                window.librariesLoaded.marked = true;
                initLibraries();
            };
            document.head.appendChild(script);
        }
    </script>

    <!-- 等待库加载完成后再加载应用脚本 -->
    <script>
        // 如果库已加载，直接加载应用脚本
        if (checkLibraries()) {
            loadAppScript();
        } else {
            // 否则等待库加载完成
            document.addEventListener('librariesLoaded', loadAppScript);

            // 设置超时，防止无限等待
            setTimeout(function() {
                if (!checkLibraries()) {
                    console.warn('库加载超时，尝试继续加载应用脚本');
                    loadAppScript();
                }
            }, 2000);
        }

        function loadAppScript() {
            console.log('加载应用脚本...');
            var script = document.createElement('script');
            script.src = '/js/joyspace-test.js';
            document.body.appendChild(script);
        }
    </script>
</body>
</html>
