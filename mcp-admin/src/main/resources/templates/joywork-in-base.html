<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html(
    title='Joywork API 测试 - MCP Platform',
    activeMenu='joywork',
    contentTitle='Joywork API 测试',
    breadcrumb='joywork',
    content=~{::content},
    headContent=~{::headContent},
    scriptContent=~{::scriptContent}
)}">
<head>
    <title>Joywork API 测试</title>
    <th:block th:fragment="headContent">
        <link rel="stylesheet" href="/highlight/styles/github.min.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-datetime-picker/css/bootstrap-datetimepicker.min.css">
        <style>
            .result-container { background: #f8f9fa; border-radius: 5px; padding: 10px; }
        </style>
    </th:block>
</head>
<body>
<th:block th:fragment="content">
    <div class="container-fluid">
        <div class="alert alert-info" th:if="${server != null}">
            <h4 th:text="${server.name}">Joywork</h4>
            <p th:text="${server.description}">描述</p>
            <p><strong>版本:</strong> <span th:text="${server.version}">1.0.0</span></p>
            <p><strong>状态:</strong> <span th:text="${server.status}">RUNNING</span></p>
        </div>
        <div class="alert alert-danger" th:if="${server == null}">
            无法获取Joywork服务信息，请确保服务已启动。
        </div>
        <ul class="nav nav-tabs" id="apiTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" id="create-task-tab" data-toggle="tab" href="#create-task" role="tab" aria-controls="create-task" aria-selected="true">创建待办任务</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="update-task-tab" data-toggle="tab" href="#update-task" role="tab" aria-controls="update-task" aria-selected="false">更新待办任务</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="update-task-status-tab" data-toggle="tab" href="#update-task-status" role="tab" aria-controls="update-task-status" aria-selected="false">更新任务状态</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="urge-task-tab" data-toggle="tab" href="#urge-task" role="tab" aria-controls="urge-task" aria-selected="false">催办任务</a>
            </li>
        </ul>
        <div class="tab-content" id="apiTabsContent">
            <!-- 创建待办任务 -->
            <div class="tab-pane fade show active" id="create-task" role="tabpanel" aria-labelledby="create-task-tab">
                <div class="card mt-3">
                    <div class="card-header"><h5>创建待办任务 (createTask)</h5></div>
                    <div class="card-body">
                        <form id="createTaskForm">
                            <div class="form-group">
                                <label>任务标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="title" required placeholder="请输入任务标题">
                            </div>
                            <div class="form-group">
                                <label>任务说明 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="remark" required placeholder="请输入任务说明">
                            </div>
                            <div class="form-group">
                                <label>任务开始时间 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control datetimepicker" name="starttime" required placeholder="yyyy-MM-dd HH:mm:ss">
                            </div>
                            <div class="form-group">
                                <label>任务结束时间 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control datetimepicker" name="endtime" required placeholder="yyyy-MM-dd HH:mm:ss">
                            </div>
                            <div class="form-group">
                                <label>任务执行人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="executor" required placeholder="多个用逗号分隔">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>
                        <div class="loading mt-3" id="createTaskLoading" style="display:none;">
                            <div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>
                            <p class="mt-2">请求处理中...</p>
                        </div>
                        <div class="mt-4" id="createTaskResult" style="display: none;">
                            <h5>执行结果</h5>
                            <div class="result-container"><pre><code class="language-json" id="createTaskJsonContent"></code></pre></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 更新待办任务 -->
            <div class="tab-pane fade" id="update-task" role="tabpanel" aria-labelledby="update-task-tab">
                <div class="card mt-3">
                    <div class="card-header"><h5>更新待办任务 (updateTask)</h5></div>
                    <div class="card-body">
                        <form id="updateTaskForm">
                            <div class="form-group">
                                <label>任务ID <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="taskid" required placeholder="请输入任务ID">
                            </div>
                            <div class="form-group">
                                <label>任务标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="title" required placeholder="请输入任务标题">
                            </div>
                            <div class="form-group">
                                <label>任务说明 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="remark" required placeholder="请输入任务说明">
                            </div>
                            <div class="form-group">
                                <label>任务开始时间 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control datetimepicker" name="starttime" required placeholder="yyyy-MM-dd HH:mm:ss">
                            </div>
                            <div class="form-group">
                                <label>任务结束时间 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control datetimepicker" name="endtime" required placeholder="yyyy-MM-dd HH:mm:ss">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>
                        <div class="loading mt-3" id="updateTaskLoading" style="display:none;">
                            <div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>
                            <p class="mt-2">请求处理中...</p>
                        </div>
                        <div class="mt-4" id="updateTaskResult" style="display: none;">
                            <h5>执行结果</h5>
                            <div class="result-container"><pre><code class="language-json" id="updateTaskJsonContent"></code></pre></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 更新任务状态 -->
            <div class="tab-pane fade" id="update-task-status" role="tabpanel" aria-labelledby="update-task-status-tab">
                <div class="card mt-3">
                    <div class="card-header"><h5>更新任务状态 (updateTaskStatus)</h5></div>
                    <div class="card-body">
                        <form id="updateTaskStatusForm">
                            <div class="form-group">
                                <label>任务ID <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="taskid" required placeholder="请输入任务ID">
                            </div>
                            <div class="form-group">
                                <label>任务状态 <span class="text-danger">*</span></label>
                                <select class="form-control" name="taskstatus" required>
                                    <option value="1">未完成</option>
                                    <option value="2">完成</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>
                        <div class="loading mt-3" id="updateTaskStatusLoading" style="display:none;">
                            <div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>
                            <p class="mt-2">请求处理中...</p>
                        </div>
                        <div class="mt-4" id="updateTaskStatusResult" style="display: none;">
                            <h5>执行结果</h5>
                            <div class="result-container"><pre><code class="language-json" id="updateTaskStatusJsonContent"></code></pre></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 催办任务 -->
            <div class="tab-pane fade" id="urge-task" role="tabpanel" aria-labelledby="urge-task-tab">
                <div class="card mt-3">
                    <div class="card-header"><h5>催办任务 (urgeTask)</h5></div>
                    <div class="card-body">
                        <form id="urgeTaskForm">
                            <div class="form-group">
                                <label>任务ID <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="taskid" required placeholder="请输入任务ID">
                            </div>
                            <div class="form-group">
                                <label>催办内容 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="urgecontent" required placeholder="请输入催办内容">
                            </div>
                            <div class="form-group">
                                <label>抄送人ERP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="taskusers" required placeholder="多个用逗号分隔">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>
                        <div class="loading mt-3" id="urgeTaskLoading" style="display:none;">
                            <div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>
                            <p class="mt-2">请求处理中...</p>
                        </div>
                        <div class="mt-4" id="urgeTaskResult" style="display: none;">
                            <h5>执行结果</h5>
                            <div class="result-container"><pre><code class="language-json" id="urgeTaskJsonContent"></code></pre></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>
<th:block th:fragment="scriptContent">
    <script src="/highlight/highlight.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.1/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-datetime-picker/js/bootstrap-datetimepicker.min.js"></script>
    <script>
        $(function() {
            // 初始化日期时间选择器
            $('.datetimepicker').datetimepicker({
                format: 'yyyy-mm-dd hh:ii:ss',
                autoclose: true,
                todayBtn: true,
                language: 'zh-CN',
                minuteStep: 1
            });
            // 高亮JSON
            function highlightJson(id, data) {
                $(id).text(JSON.stringify(data, null, 2));
                if (window.hljs) hljs.highlightElement($(id)[0]);
            }
            // 通用表单提交
            function handleForm(formId, url, loadingId, resultId, jsonId) {
                $(formId).on('submit', function(e) {
                    e.preventDefault();
                    $(loadingId).show();
                    $(resultId).hide();
                    $.post(url, $(this).serialize(), function(res) {
                        $(loadingId).hide();
                        $(resultId).show();
                        highlightJson(jsonId, res);
                    }).fail(function(xhr) {
                        $(loadingId).hide();
                        $(resultId).show();
                        highlightJson(jsonId, {success: false, error: xhr.responseText});
                    });
                });
            }
            handleForm('#createTaskForm', '/joywork/createTask', '#createTaskLoading', '#createTaskResult', '#createTaskJsonContent');
            handleForm('#updateTaskForm', '/joywork/updateTask', '#updateTaskLoading', '#updateTaskResult', '#updateTaskJsonContent');
            handleForm('#updateTaskStatusForm', '/joywork/updateTaskStatus', '#updateTaskStatusLoading', '#updateTaskStatusResult', '#updateTaskStatusJsonContent');
            handleForm('#urgeTaskForm', '/joywork/urgeTask', '#urgeTaskLoading', '#urgeTaskResult', '#urgeTaskJsonContent');
        });
    </script>
</th:block>
</body>
</html> 