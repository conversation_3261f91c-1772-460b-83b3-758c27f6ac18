<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title th:text="${title} ?: 'MCP Platform'">MCP Platform</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- AdminLTE -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/css/adminlte.min.css">

    <!-- 自定义样式 -->
    <style>
        .server-status-running {
            color: #28a745;
        }
        .server-status-stopped {
            color: #dc3545;
        }
        .server-status-error {
            color: #ffc107;
        }
    </style>

    <!-- 额外的头部内容 -->
    <th:block th:replace="${headContent} ?: ~{}"></th:block>
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">

    <!-- 导航栏 -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- 左侧导航栏链接 -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="/dashboard" class="nav-link">首页</a>
            </li>
        </ul>

        <!-- 右侧导航栏链接 -->
        <ul class="navbar-nav ml-auto">
            <li class="nav-item">
                <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                    <i class="fas fa-expand-arrows-alt"></i>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主侧边栏容器 -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
        <!-- 品牌Logo -->
        <a href="/dashboard" class="brand-link">
            <span class="brand-text font-weight-light">MCP Platform</span>
        </a>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 侧边栏菜单 -->
            <nav class="mt-2">
                <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                    <li class="nav-item">
                        <a href="/dashboard" class="nav-link" th:classappend="${activeMenu == 'dashboard'} ? 'active' : ''">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <p>仪表盘</p>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/market" class="nav-link" th:classappend="${activeMenu == 'market'} ? 'active' : ''">
                            <i class="nav-icon fas fa-store"></i>
                            <p>MCP市场</p>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/chat" class="nav-link" th:classappend="${activeMenu == 'chat'} ? 'active' : ''">
                            <i class="nav-icon fas fa-comments"></i>
                            <p>AI聊天</p>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/joyspace" class="nav-link" th:classappend="${activeMenu == 'joyspace'} ? 'active' : ''">
                            <i class="nav-icon fas fa-file-alt"></i>
                            <p>Joyspace</p>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/joywork" class="nav-link" th:classappend="${activeMenu == 'joywork'} ? 'active' : ''">
                            <i class="nav-icon fas fa-tasks"></i>
                            <p>joywork</p>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/timline" class="nav-link" th:classappend="${activeMenu == 'timline'} ? 'active' : ''">
                            <i class="nav-icon fas fa-comment-alt"></i>
                            <p>Timline</p>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/jsf" class="nav-link" th:classappend="${activeMenu == 'jsf'} ? 'active' : ''">
                            <i class="nav-icon fas fa-cogs"></i>
                            <p>JSF</p>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/enhanced-docs" class="nav-link" target="_blank">
                            <i class="nav-icon fas fa-book-open"></i>
                            <p>文档中心</p>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/swagger-ui.html" class="nav-link" target="_blank">
                            <i class="nav-icon fas fa-book"></i>
                            <p>API调用</p>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </aside>

    <!-- 内容包装器 -->
    <div class="content-wrapper">
        <!-- 内容标题 -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0" th:text="${contentTitle} ?: 'MCP Platform'">MCP Platform</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="/dashboard">首页</a></li>
                            <li class="breadcrumb-item active" th:text="${breadcrumb} ?: ''">当前页面</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="content">
            <div class="container-fluid">
                <!-- 文档侧边栏 -->
                <div class="row" th:if="${activeMenu == 'docs'}">
                    <div class="col-md-3">
                        <th:block th:replace="docs/sidebar :: sidebar"></th:block>
                    </div>
                    <div class="col-md-9">
                        <th:block th:replace="${content} ?: ~{}"></th:block>
                    </div>
                </div>
                <!-- 非文档页面 -->
                <div class="row" th:unless="${activeMenu == 'docs'}">
                    <div class="col-md-12">
                        <th:block th:replace="${content} ?: ~{}"></th:block>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主页脚 -->
    <footer class="main-footer">
        <div class="float-right d-none d-sm-inline">
            <a href="timline://chat/?topin=wangwangang">王万刚</a>Powered by Spring Boot & Solon
        </div>
        <strong>Copyright &copy; 2024 <a href="https://github.com/your-username/mcp-platform">MCP Platform</a>.</strong> All rights reserved.
    </footer>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Bootstrap 4 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2.0/dist/js/adminlte.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- 额外的脚本内容 -->
<th:block th:replace="${scriptContent} ?: ~{}"></th:block>
</body>
</html>
