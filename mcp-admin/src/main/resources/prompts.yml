需求分析: |
  需求分析和项目分析
  - 需求背景不是改动的内容，只是陈述需求的原因，不要写到改动点中
  - 不能遗漏任何任务点
  - 不能凭空增加任务点，任何拆分出的任务点需要有原有任务支持
  - 不能太过宽泛，需要开发点细致明确
  - 不做任何代码改动，不做现有代码检查。仅输出开发任务点
  - 调用get_user_question工具询问用户本次需求是纯前端开发需求还是纯后端开发需求，还是全栈开发需求。
  - 调用get_prompt("追踪文档")工具获取需求追踪文档的创建规则，创建需求追踪文档
  - 分析需求文档或用户描述，分析当前工作区的前端和后端工程（如果存在），在"需求追踪"模块记录分析结果
  - 注意：可以询问用户一些问题来澄清需求本身的问题，如果涉及前端页面开发，不需要此步骤询问设计稿或者参考相关，后续有专门步骤处理设计稿还原
  - 需求分析完成后，调用get_prompt("工程分析")工具

追踪文档: |
  请按照以下步骤创建需求追踪文档:
  1. 在当前工作区根目录下依据'【需求追踪】需求名称.md'的命名格式创建文件
  2. 使用以下模板内容:
    ```markdown
  
  # 需求自动化开发跟踪文档
  ## 需求内容
    *在此处详细描述本次需求内容*
  ## 前端项目情况
    *在此处添加前端项目的总体情况与项目现有技术栈及实现规范*
  ## 后端项目情况
    *在此处添加后端项目的总体情况与项目现有技术栈及实现规范*
  ## 数据库变更
    *在此处添加数据库变更信息*
  ## 联调对齐
    *前后端接口定义与规范对齐信息*
  ## 进度记录
    *在此处记录当前的开发进度和完成情况*
  ## 问题跟踪
    *在此处记录开发过程中遇到的问题和解决方案*
  ## 备注
    *在此处添加其他相关信息*
  ---
  最后更新时间: 2025-05-09T03:14:54.086Z
  
  必须遵守：后续每完成一个步骤，都需要更新该文档内容, 确保进度的实时更新和问题的及时记录。


工程分析: |
  1. 如有任何不确定或信心不足，**必做其一**：
  
  - 向用户提问以澄清
  - 使用 `query_task`、`read_file`、`codebase_search`或其他类似的工具查询现有程序／架构
  - 使用 `web_search` 或其他网络搜索工具查询不熟悉的概念或技术  
  禁止凭空臆测，所有信息必可追溯来源。
  2. **检查现有程序与结构**
  
  - 使用精确的搜索策略：
    - 利用 `read_file`、`codebase_search`或其他类似的工具查询与任务相关的现有实现方式
    - 寻找与当前任务功能类似的现有代码
    - 分析目录结构寻找相似功能模块
  - 分析代码风格与约定：
    - 检查现有组件的命名规则（驼峰式、蛇形等）
    - 确认注释风格和格式约定
    - 分析错误处理模式和日志记录方式
  - 记录并遵循发现的模式：
    - 详细记录代码模式和组织结构
    - 计划如何沿用这些模式进行设计
  - 判断是否与现有功能重叠，并决定"重用"或"抽象重构"
  - **不得**先生成设计再查现有码；必须"先查再设"
  3. 追踪更新
  - 评估完成后将评估结果更新至追踪文档
  - 需求分析完成后，调用get_prompt("后端开发")工具

后端开发: |
  - 遵循现有项目的文件结构，实现规范，技术选型和开发风格，根据需求的功能点，完整的完成全部后端开发任务。必须全部实现后端工程涉及的所有开发点，不能只实现部分功能。
  - 推荐使用use_write_file创建新文件，use_replace_file修改已有代码
  - 注意：如果涉及数据库变更，可以询问用户是否需要连接到测试数据库，如果需要连接，请用户提供数据库连接信息
  - 如果用户提供了数据库链接信息，使用db_connect工具连接到MySQL数据库，使用db_get_schema工具获取现有数据库结构，使用db_execute_schema工具创建或修改表结构，使用db_execute_query工具执行数据操作
  - 添加必要的日志记录、监控、单元测试、安全机制（如果需要或遵循现有项目要求）
  - 全部后端开发任务完成后，调用get_next_step_guidelines进入步骤3

vue: |
  请创建一个 Vue 3 组件，包含以下功能：
  1. 使用 Composition API
  2. 实现一个响应式的数据属性
  3. 包含一个计算属性
  4. 实现一个方法来更新数据
  5. 使用 v-for 指令来渲染列表
  6. 实现父子组件通信
  请提供完整的组件代码，包括模板、脚本和样式部分。

react: |
  请创建一个 React 函数组件，包含以下功能：
  1. 使用 useState 和 useEffect 钩子
  2. 实现一个状态变量和更新函数
  3. 使用 useCallback 优化性能
  4. 实现条件渲染
  5. 使用 map 函数渲染列表
  6. 实现父子组件通信
  请提供完整的组件代码，包括导入语句和样式（可以使用内联样式或 CSS 模块）。

java: |
  请创建一个 Java 类，实现以下功能：
  1. 使用面向对象编程原则
  2. 包含至少一个私有属性和对应的 getter 和 setter 方法
  3. 实现一个构造函数
  4. 重写 toString() 方法
  5. 实现一个自定义方法，展示类的核心功能
  6. 使用异常处理
  请提供完整的类代码，包括所有必要的导入语句。
