# 天气服务 业务配置
# 生成时间: 2025-04-24 18:29:19

# 服务基本信息
weather.name=天气服务
weather.description=提供全球城市天气查询服务
weather.version=1.0.0
weather.type=API
weather.usage=查询指定城市的天气信息，参数：city - 城市名称（如：Beijing, Shanghai, New York等）

# 业务配置项
# API密钥
weather.api.key=your-api-key
# API基础URL
weather.api.base_url=https://api.openweathermap.org/data/2.5/weather
# 温度单位：metric（摄氏度）、imperial（华氏度）、standard（开尔文）
weather.units=metric
# 语言
weather.lang=zh_cn
# 超时时间，单位：毫秒
weather.timeout=5000
# 缓存时间，单位：秒
weather.cache.time=1800

# 示例业务配置

# 其他配置项
# 在此处添加其他配置项
