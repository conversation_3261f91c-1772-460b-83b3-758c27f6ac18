# JSF开放平台服务 业务配置
# 生成时间: 2025-01-27 20:00:00

# 服务基本信息
jsf.name=JSF开放平台服务
jsf.description=JSF开放平台MCP服务，提供接口查询、方法信息获取、别名查询等功能
jsf.version=1.0.0
jsf.type=API
jsf.usage=JSF开放平台服务，支持接口信息查询（getInterfaceInfo）、方法信息查询（getMethodInfo）、方法列表查询（getMethodList）、别名查询（getAliasByInterfaceName）

# JSF开放API配置
jsf.openapi.appKey=jdos_ofw-outbound
jsf.openapi.token=Jk7J2Lp9XmN4QwR8vT3sB6yH1gF5dE0z
jsf.openapi.index=test.i.jsf.jd.local

# 默认配置
jsf.default.operator=system
jsf.default.timeout=5000
jsf.default.retries=3

# 示例业务配置
jsf.example.interfaceName=erp.ql.station.api.service.gangao.CustomsClearanceApi
jsf.example.operator=testuser

# 其他配置项
# 在此处添加其他配置项 ...
