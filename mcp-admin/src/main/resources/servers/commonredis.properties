# 多集群Redis操作服务 业务配置
# 生成时间: 2025-04-26 15:19:43

# 服务基本信息
commonredis.name=多集群Redis操作服务
commonredis.description=提供多集群Redis数据库操作功能，包括字符串、哈希、列表、集合、有序集合等操作
commonredis.version=1.0.0
commonredis.type=API
commonredis.usage=提供多集群Redis数据库操作功能，支持多种Redis命令，参数：cluster - 集群名称(必填)，command - 命令名称（如：SET、GET、HSET等）

# 业务配置项
# Redis集群列表，以逗号分隔
redis.clusters=main,cache,session
# main集群别名
redis.main.alias=主数据集群
# main集群Redis服务器地址
redis.main.host=localhost
# main集群Redis服务器端口
redis.main.port=6379
# main集群Redis服务器密码
redis.main.password=
# main集群Redis数据库索引
redis.main.database=0
# main集群Redis连接超时时间（毫秒）
redis.main.timeout=2000
# main集群Redis连接池最大连接数
redis.main.pool.max_total=8
# main集群Redis连接池最大空闲连接数
redis.main.pool.max_idle=8
# main集群Redis连接池最小空闲连接数
redis.main.pool.min_idle=0
# cache集群别名
redis.cache.alias=缓存集群
# cache集群Redis服务器地址
redis.cache.host=localhost
# cache集群Redis服务器端口
redis.cache.port=6379
# cache集群Redis服务器密码
redis.cache.password=
# cache集群Redis数据库索引
redis.cache.database=0
# cache集群Redis连接超时时间（毫秒）
redis.cache.timeout=2000
# cache集群Redis连接池最大连接数
redis.cache.pool.max_total=16
# cache集群Redis连接池最大空闲连接数
redis.cache.pool.max_idle=8
# cache集群Redis连接池最小空闲连接数
redis.cache.pool.min_idle=0
# session集群别名
redis.session.alias=会话集群
# session集群Redis服务器地址
redis.session.host=localhost
# session集群Redis服务器端口
redis.session.port=6379
# session集群Redis服务器密码
redis.session.password=
# session集群Redis数据库索引
redis.session.database=0
# session集群Redis连接超时时间（毫秒）
redis.session.timeout=2000
# session集群Redis连接池最大连接数
redis.session.pool.max_total=8
# session集群Redis连接池最大空闲连接数
redis.session.pool.max_idle=8
# session集群Redis连接池最小空闲连接数
redis.session.pool.min_idle=0
# Redis危险命令列表，以逗号分隔
redis.dangerous_commands=FLUSHALL,FLUSHDB,DEL,KEYS,CONFIG,EVAL,EVALSHA,SAVE,BGSAVE,DEBUG

# 示例业务配置

# 其他配置项
# 在此处添加其他配置项
