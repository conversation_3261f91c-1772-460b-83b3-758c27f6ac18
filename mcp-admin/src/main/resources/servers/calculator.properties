# 提供基本的数学计算功能 业务配置
# 生成时间: 2025-04-26 10:49:57

# 服务基本信息
calculator.name=提供基本的数学计算功能
calculator.description=提供基本的数学计算功能
calculator.version=1.0.0
calculator.type=API
calculator.usage=执行基本的数学运算，参数：operation - 操作类型（add, subtract, multiply, divide, power, sqrt）, a - 第一个数字, b - 第二个数字（对于sqrt操作可选）

# 业务配置项
# 计算结果的精度（小数位数）
calculator.precision=2
# 允许的最大计算值
calculator.max_value=1000000

# 示例业务配置

# 其他配置项
# 在此处添加其他配置项
