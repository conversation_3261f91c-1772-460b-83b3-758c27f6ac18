<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <!-- JSF开放API注册中心配置 -->
    <jsf:registry id="jsfOpenApiRegistry" protocol="jsfRegistry" index="${jsf.openapi.index:test.i.jsf.jd.local}"/>

    <!-- JSF开放API接口服务 -->
    <jsf:consumer id="interfaceService" interface="com.jd.jsf.open.api.InterfaceService"
                  protocol="jsf" alias="jsf-open-api" timeout="5000" retries="3" registry="jsfOpenApiRegistry">
    </jsf:consumer>

    <!-- JSF开放API别名服务 -->
    <jsf:consumer id="providerAliaService" interface="com.jd.jsf.open.api.ProviderAliaService"
                  protocol="jsf" alias="jsf-open-api" timeout="5000" retries="3" registry="jsfOpenApiRegistry">
    </jsf:consumer>

</beans>
