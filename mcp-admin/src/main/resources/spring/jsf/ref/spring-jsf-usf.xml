<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <!-- 人员服务接口 -->
   <!-- <jsf:consumer id="businessSystemMenuResourceService" interface="com.jd.susf.service.api.BusinessSystemMenuResourceService"
                  protocol="jsf" alias="${jsf.consumer.alias.usf}" timeout="1000" retries="3">
    </jsf:consumer>-->

    <!-- 权限服务接口 -->
    <jsf:consumer id="susfPermissionService" interface="com.jd.susf.service.api.SusfPermissionService"
                  protocol="jsf" alias="${jsf.consumer.alias.usf}" timeout="1000">
    </jsf:consumer>
    <jsf:consumer id="susfUserService" interface="com.jd.susf.service.api.SusfUserService"
                  protocol="jsf" alias="${jsf.consumer.alias.usf}" timeout="1000">
    </jsf:consumer>
</beans>
