<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <!--provider -->
    <jsf:server id="jsf" protocol="jsf" port="${jsf.port}"/>
    <!-- consumer-->
    <!--  jsf 注册中心  -->
    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jsf.index}"/>

    <!-- 共用部分-->
    <!-- provider 配置开始-->
    <!-- <jsf:provider id="rtsEsService" interface="cn.jdl.sc.omg.view.ofc.rts.RtsEsService"
                   alias="${jsf.provider.alias}" ref="rtsEsServiceImpl" serialization="hessian">
         <jsf:parameter key="token" hide="true" value="${jsf.provider.token}"/>
     </jsf:provider>
     <jsf:provider id="poEsService" interface="cn.jdl.sc.omg.view.ofc.po.PoEsService"
                   alias="${jsf.provider.alias}" ref="poEsServiceImpl" serialization="hessian">
         <jsf:parameter key="token" hide="true" value="${jsf.provider.token}"/>
     </jsf:provider>-->
    <!--<jsf:provider id="publicRtwEsService" interface="cn.jdl.sc.omg.view.ofc.rtw.RtwEsService"
                  alias="${jsf.provider.alias}" ref="rtwEsServiceImpl" serialization="hessian">
        <jsf:parameter key="token" hide="true" value="${jsf.provider.token}"/>
    </jsf:provider>-->
    <!-- provider 配置结束-->
    <!-- consumer 配置开始-->
    <jsf:filter id="consumerLoggingFilter" class="com.jdl.sc.core.jsf.filter.ConsumerLoggingFilter" providers=""
                consumers="*"/>
    <!-- consumer 配置结束-->
    <import resource="jsf/ref/spring-jsf-usf.xml"/>
    <import resource="jsf/spring-jsf-openapi.xml"/>
</beans>
