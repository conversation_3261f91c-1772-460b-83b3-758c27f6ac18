<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/mvc
        http://www.springframework.org/schema/mvc/spring-mvc.xsd"
       default-lazy-init="false" default-autowire="byName">


    <!-- 单点登录拦截器 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean id="springSSOInterceptor" class="com.jdl.mcp.admin.security.SpringSSOInterceptor">
                <property name="registryConfig" ref="jsfRegistry"/>
            </bean>
        </mvc:interceptor>

    </mvc:interceptors>
</beans>
