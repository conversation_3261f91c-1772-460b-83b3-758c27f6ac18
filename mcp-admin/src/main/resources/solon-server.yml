# 如果使用 servelt 则使用与 sprongboot 相同的等口
#server.port: 8001
# 在mcpserver.yml中
solon:
  mcp:
    heartbeat:
      interval: 30000  # 30秒，默认可能太频繁
      enabled: false    # 可以选择关闭心跳
solon.ai:
  mcp:
    global:
      enable: true

  chat:
    chatrhino:
      apiUrl: ${CHAT_API_URL:http://gpt-proxy.jd.com/v1/chat/completions} # 使用完整地址（而不是 api_base）
      # API密钥，可通过CHAT_API_KEY环境变量覆盖
      apiKey: ${CHAT_API_KEY:618dc981-72c0-4b7c-bd2b-90d550f828be}
      provider: "chatrhino"
      # 模型名称，可通过CHAT_MODEL环境变量覆盖
      model: ${CHAT_MODEL:Chatrhino-81B-Pro}
      timeout: "300s"
      # 系统提示词
      systemPrompt: |
        你是MCP平台的AI助手，可以帮助用户使用各种MCP服务。
        当用户需要查询数据或执行特定操作时，你可以调用相应的函数来完成任务。
        请根据用户的需求，选择合适的函数进行调用。如果不需要调用函数，直接回答用户的问题。
        请注意，你应该尽可能使用函数来处理用户的请求，特别是当用户请求查询数据或执行特定操作时。