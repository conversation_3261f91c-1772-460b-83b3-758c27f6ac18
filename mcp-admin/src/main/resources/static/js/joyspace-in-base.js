/**
 * Joyspace测试页面脚本 (嵌入版)
 */
$(document).ready(function() {
    console.log('应用脚本已加载，准备初始化...');

    // 检查库是否已加载
    var hljsLoaded = typeof hljs !== 'undefined';
    var markedLoaded = typeof marked !== 'undefined';

    console.log('库加载状态: highlight.js=' + hljsLoaded + ', marked=' + markedLoaded);

    // 初始化Markdown解析器
    if (markedLoaded) {
        try {
            marked.setOptions({
                renderer: new marked.Renderer(),
                highlight: function(code, lang) {
                    if (hljsLoaded && hljs.getLanguage(lang)) {
                        return hljs.highlight(code, { language: lang }).value;
                    }
                    return code;
                },
                langPrefix: 'hljs language-',
                pedantic: false,
                gfm: true,
                breaks: false,
                sanitize: false,
                smartypants: false,
                xhtml: false
            });
            console.log('Markdown解析器初始化成功');
        } catch (e) {
            console.error('Markdown解析器初始化失败:', e);
        }
    }

    // 获取文件夹列表
    $('#folderListForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $('#folderListLoading').show();
        $('#folderListResult').hide();

        $.ajax({
            url: '/joyspace/getFolderList',
            type: 'POST',
            data: formData,
            success: function(response) {
                // 优化JSON显示
                try {
                    // 创建一个深度格式化JSON的函数
                    function formatJsonData(data) {
                        if (typeof data === 'string') {
                            try {
                                // 尝试解析JSON字符串
                                return formatJsonData(JSON.parse(data));
                            } catch (e) {
                                // 如果不是有效的JSON，则返回原始字符串
                                return data;
                            }
                        } else if (typeof data === 'object' && data !== null) {
                            // 处理对象（包括数组）
                            const newObj = Array.isArray(data) ? [] : {};
                            for (const key in data) {
                                if (Object.prototype.hasOwnProperty.call(data, key)) {
                                    // 递归处理每个属性
                                    newObj[key] = formatJsonData(data[key]);
                                }
                            }
                            return newObj;
                        } else {
                            // 原始类型直接返回
                            return data;
                        }
                    }

                    // 格式化整个响应
                    const formattedResponse = formatJsonData(response);

                    // 将格式化后的响应转换为字符串并显示
                    $('#folderListJsonContent').text(JSON.stringify(formattedResponse, null, 2));

                    if (typeof hljs !== 'undefined') {
                        try {
                            hljs.highlightElement(document.getElementById('folderListJsonContent'));
                        } catch (e) {
                            console.error('语法高亮失败:', e);
                        }
                    }
                } catch (e) {
                    console.error('JSON解析失败:', e);
                    $('#folderListJsonContent').text(JSON.stringify(response, null, 2));
                }
                $('#folderListResult').show();

                // 如果成功获取文件夹列表，可以提取文件夹ID供其他API使用
                if (response.success && response.jsonData) {
                    try {
                        // 获取格式化后的jsonData
                        const jsonData = formattedResponse.jsonData;
                        if (jsonData && jsonData.data && jsonData.data.folders && jsonData.data.folders.length > 0) {
                            // 创建文件夹选择下拉菜单
                            createFolderDropdown(jsonData.data.folders);
                        }
                    } catch (e) {
                        console.error('处理文件夹列表失败:', e);
                    }
                }
            },
            error: function(xhr, status, error) {
                $('#folderListJsonContent').text('Error: ' + error);
                $('#folderListResult').show();
            },
            complete: function() {
                $('#folderListLoading').hide();
            }
        });
    });

    // 获取文件列表
    $('#fileListForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $('#fileListLoading').show();
        $('#fileListResult').hide();

        $.ajax({
            url: '/joyspace/getFileList',
            type: 'POST',
            data: formData,
            success: function(response) {
                // 优化JSON显示
                try {
                    // 创建一个深度格式化JSON的函数
                    function formatJsonData(data) {
                        if (typeof data === 'string') {
                            try {
                                // 尝试解析JSON字符串
                                return formatJsonData(JSON.parse(data));
                            } catch (e) {
                                // 如果不是有效的JSON，则返回原始字符串
                                return data;
                            }
                        } else if (typeof data === 'object' && data !== null) {
                            // 处理对象（包括数组）
                            const newObj = Array.isArray(data) ? [] : {};
                            for (const key in data) {
                                if (Object.prototype.hasOwnProperty.call(data, key)) {
                                    // 递归处理每个属性
                                    newObj[key] = formatJsonData(data[key]);
                                }
                            }
                            return newObj;
                        } else {
                            // 原始类型直接返回
                            return data;
                        }
                    }

                    // 格式化整个响应
                    const formattedResponse = formatJsonData(response);

                    // 将格式化后的响应转换为字符串并显示
                    $('#fileListJsonContent').text(JSON.stringify(formattedResponse, null, 2));

                    if (typeof hljs !== 'undefined') {
                        try {
                            hljs.highlightElement(document.getElementById('fileListJsonContent'));
                        } catch (e) {
                            console.error('语法高亮失败:', e);
                        }
                    }
                } catch (e) {
                    console.error('JSON解析失败:', e);
                    $('#fileListJsonContent').text(JSON.stringify(response, null, 2));
                }
                $('#fileListResult').show();

                // 如果成功获取文件列表，可以提取文件ID供其他API使用
                if (response.success && response.jsonData) {
                    try {
                        // 获取格式化后的jsonData
                        const jsonData = formattedResponse.jsonData;
                        if (jsonData && jsonData.data && jsonData.data.pages && jsonData.data.pages.length > 0) {
                            // 创建文件选择下拉菜单
                            createFileDropdown(jsonData.data.pages);
                        }
                    } catch (e) {
                        console.error('处理文件列表失败:', e);
                    }
                }
            },
            error: function(xhr, status, error) {
                $('#fileListJsonContent').text('Error: ' + error);
                $('#fileListResult').show();
            },
            complete: function() {
                $('#fileListLoading').hide();
            }
        });
    });

    // 获取页面信息
    $('#pageInfoForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $('#pageInfoLoading').show();
        $('#pageInfoResult').hide();

        $.ajax({
            url: '/joyspace/getPageInfo',
            type: 'POST',
            data: formData,
            success: function(response) {
                // 优化JSON显示
                try {
                    // 创建一个深度格式化JSON的函数
                    function formatJsonData(data) {
                        if (typeof data === 'string') {
                            try {
                                // 尝试解析JSON字符串
                                return formatJsonData(JSON.parse(data));
                            } catch (e) {
                                // 如果不是有效的JSON，则返回原始字符串
                                return data;
                            }
                        } else if (typeof data === 'object' && data !== null) {
                            // 处理对象（包括数组）
                            const newObj = Array.isArray(data) ? [] : {};
                            for (const key in data) {
                                if (Object.prototype.hasOwnProperty.call(data, key)) {
                                    // 递归处理每个属性
                                    newObj[key] = formatJsonData(data[key]);
                                }
                            }
                            return newObj;
                        } else {
                            // 原始类型直接返回
                            return data;
                        }
                    }

                    // 格式化整个响应
                    const formattedResponse = formatJsonData(response);

                    // 将格式化后的响应转换为字符串并显示
                    $('#pageInfoJsonContent').text(JSON.stringify(formattedResponse, null, 2));

                    if (typeof hljs !== 'undefined') {
                        try {
                            hljs.highlightElement(document.getElementById('pageInfoJsonContent'));
                        } catch (e) {
                            console.error('语法高亮失败:', e);
                        }
                    }
                } catch (e) {
                    console.error('JSON解析失败:', e);
                    $('#pageInfoJsonContent').text(JSON.stringify(response, null, 2));
                }
                $('#pageInfoResult').show();
            },
            error: function(xhr, status, error) {
                $('#pageInfoJsonContent').text('Error: ' + error);
                $('#pageInfoResult').show();
            },
            complete: function() {
                $('#pageInfoLoading').hide();
            }
        });
    });

    // 获取页面内容
    $('#pageContentForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $('#pageContentLoading').show();
        $('#pageContentResult').hide();

        $.ajax({
            url: '/joyspace/getPageContent',
            type: 'POST',
            data: formData,
            success: function(response) {
                // 优化JSON显示
                try {
                    // 创建一个深度格式化JSON的函数
                    function formatJsonData(data) {
                        if (typeof data === 'string') {
                            try {
                                // 尝试解析JSON字符串
                                return formatJsonData(JSON.parse(data));
                            } catch (e) {
                                // 如果不是有效的JSON，则返回原始字符串
                                return data;
                            }
                        } else if (typeof data === 'object' && data !== null) {
                            // 处理对象（包括数组）
                            const newObj = Array.isArray(data) ? [] : {};
                            for (const key in data) {
                                if (Object.prototype.hasOwnProperty.call(data, key)) {
                                    // 递归处理每个属性
                                    newObj[key] = formatJsonData(data[key]);
                                }
                            }
                            return newObj;
                        } else {
                            // 原始类型直接返回
                            return data;
                        }
                    }

                    // 格式化整个响应
                    const formattedResponse = formatJsonData(response);

                    // 将格式化后的响应转换为字符串并显示
                    $('#pageContentJsonContent').text(JSON.stringify(formattedResponse, null, 2));

                    if (typeof hljs !== 'undefined') {
                        try {
                            hljs.highlightElement(document.getElementById('pageContentJsonContent'));
                        } catch (e) {
                            console.error('语法高亮失败:', e);
                        }
                    }
                } catch (e) {
                    console.error('JSON解析失败:', e);
                    $('#pageContentJsonContent').text(JSON.stringify(response, null, 2));
                }

                // 处理Markdown内容
                if (response.markdownData) {
                    if (typeof marked !== 'undefined') {
                        try {
                            $('#pageContentMarkdownContent').html(marked.parse(response.markdownData));
                        } catch (e) {
                            console.error('Markdown解析失败:', e);
                            $('#pageContentMarkdownContent').html('<div class="alert alert-warning">Markdown解析失败: ' + e.message + '</div>');
                        }
                    } else {
                        $('#pageContentMarkdownContent').html('<div class="alert alert-warning">Markdown解析器未加载</div>');
                    }
                } else {
                    $('#pageContentMarkdownContent').html('<div class="alert alert-warning">没有可用的Markdown内容</div>');
                }

                $('#pageContentResult').show();
            },
            error: function(xhr, status, error) {
                $('#pageContentJsonContent').text('Error: ' + error);
                $('#pageContentMarkdownContent').html('<div class="alert alert-danger">请求失败: ' + error + '</div>');
                $('#pageContentResult').show();
            },
            complete: function() {
                $('#pageContentLoading').hide();
            }
        });
    });

    // 创建文件夹下拉菜单
    function createFolderDropdown(folders) {
        if (!folders || folders.length === 0) return;

        // 创建下拉菜单
        const dropdown = $('<div class="mt-3"><label>选择文件夹:</label><select class="form-control folder-select"></select></div>');
        const select = dropdown.find('select');

        // 添加选项
        folders.forEach(folder => {
            select.append(`<option value="${folder.id}">${folder.name}</option>`);
        });

        // 添加事件监听
        select.on('change', function() {
            const folderId = $(this).val();
            if (folderId) {
                // 自动填充到文件列表表单
                $('#fileListFolderUrl').val(folderId);
            }
        });

        // 添加到页面
        $('#folderListResult').after(dropdown);
    }

    // 创建文件下拉菜单
    function createFileDropdown(files) {
        if (!files || files.length === 0) return;

        // 创建下拉菜单
        const dropdown = $('<div class="mt-3"><label>选择文件:</label><select class="form-control file-select"></select></div>');
        const select = dropdown.find('select');

        // 添加选项
        files.forEach(file => {
            select.append(`<option value="${file.id}">${file.title || file.id}</option>`);
        });

        // 添加事件监听
        select.on('change', function() {
            const fileId = $(this).val();
            if (fileId) {
                // 自动填充到页面信息和页面内容表单
                $('#pageInfoPageUrl').val(fileId);
                $('#pageContentPageUrl').val(fileId);
            }
        });

        // 添加到页面
        $('#fileListResult').after(dropdown);
    }

    // 添加示例值按钮
    $('.add-example').click(function() {
        const target = $(this).data('target');
        const value = $(this).data('value');
        $(target).val(value);
    });
});
