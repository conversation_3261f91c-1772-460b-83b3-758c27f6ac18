/**
 * Timline测试页面脚本 (嵌入版)
 */
$(document).ready(function() {
    console.log('应用脚本已加载，准备初始化...');

    // 调试信息：检查按钮是否存在
    console.log('按钮检查：', {
        'exampleTextMessage': $('#exampleTextMessage').length,
        'examplePersonalMessage': $('#examplePersonalMessage').length,
        'exampleGroupAtAllMessage': $('#exampleGroupAtAllMessage').length,
        'exampleGroupAtUserMessage': $('#exampleGroupAtUserMessage').length,
        'exampleCardMessage': $('#exampleCardMessage').length
    });

    // 检查库是否已加载
    var hljsLoaded = typeof hljs !== 'undefined';
    console.log('库加载状态: highlight.js=' + hljsLoaded);

    // 发送消息
    $('#sendMessageForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $('#sendMessageLoading').show();
        $('#sendMessageResult').hide();

        $.ajax({
            url: '/timline/sendMessage',
            type: 'POST',
            data: formData,
            success: function(response) {
                // 优化JSON显示
                try {
                    // 创建一个深度格式化JSON的函数
                    function formatJsonData(data) {
                        if (typeof data === 'string') {
                            try {
                                // 尝试解析JSON字符串
                                return formatJsonData(JSON.parse(data));
                            } catch (e) {
                                // 如果不是有效的JSON，则返回原始字符串
                                return data;
                            }
                        } else if (typeof data === 'object' && data !== null) {
                            // 处理对象（包括数组）
                            const newObj = Array.isArray(data) ? [] : {};
                            for (const key in data) {
                                if (Object.prototype.hasOwnProperty.call(data, key)) {
                                    // 递归处理每个属性
                                    newObj[key] = formatJsonData(data[key]);
                                }
                            }
                            return newObj;
                        } else {
                            // 原始类型直接返回
                            return data;
                        }
                    }

                    // 格式化整个响应
                    const formattedResponse = formatJsonData(response);

                    // 将格式化后的响应转换为字符串并显示
                    $('#sendMessageJsonContent').text(JSON.stringify(formattedResponse, null, 2));

                    if (typeof hljs !== 'undefined') {
                        try {
                            hljs.highlightElement(document.getElementById('sendMessageJsonContent'));
                        } catch (e) {
                            console.error('语法高亮失败:', e);
                        }
                    }
                } catch (e) {
                    console.error('JSON解析失败:', e);
                    $('#sendMessageJsonContent').text(JSON.stringify(response, null, 2));
                }
                $('#sendMessageResult').show();
            },
            error: function(xhr, status, error) {
                $('#sendMessageJsonContent').text('Error: ' + error);
                $('#sendMessageResult').show();
            },
            complete: function() {
                $('#sendMessageLoading').hide();
            }
        });
    });

    // 定义示例消息
    const examples = {
        textMessage: {
            "body": {
                "type": "text",
                "content": "这是一条测试消息"
            }
        },
        personalMessage: {
            "body": {
                "type": "text",
                "content": "这是一条普通的个人测试消息"
            }
        },
        groupAtAllMessage: {
            "body": {
                "type": "text",
                "content": "@全体成员 这是一条全体成员通知",
                "atUsers": [{
                    "app": "ee",
                    "pin": "all",
                    "nickname": "全体成员"
                }]
            }
        },
        groupAtUserMessage: {
            "body": {
                "type": "text",
                "content": "@连大湖 这是一条@消息测试",
                "atUsers": [{
                    "app": "ee",
                    "pin": "bjliandahu",
                    "nickname": "连大湖"
                }]
            }
        }
    };

    // 绑定点击事件 - 使用事件委托
    $(document).on('click', '#exampleTextMessage', function() {
        console.log('点击了基础文本消息示例按钮');
        $('#sendMessageType').val('1');
        $('#sendMessageContent').val(JSON.stringify(examples.textMessage, null, 2));
    });

    $(document).on('click', '#examplePersonalMessage', function() {
        console.log('点击了个人消息示例按钮');
        $('#sendMessageType').val('1');
        $('#sendMessageContent').val(JSON.stringify(examples.personalMessage, null, 2));
    });

    $(document).on('click', '#exampleGroupAtAllMessage', function() {
        console.log('点击了群组消息@全员示例按钮');
        $('#sendMessageType').val('1');
        $('#sendMessageContent').val(JSON.stringify(examples.groupAtAllMessage, null, 2));
    });

    $(document).on('click', '#exampleGroupAtUserMessage', function() {
        console.log('点击了群组消息@用户示例按钮');
        $('#sendMessageType').val('1');
        $('#sendMessageContent').val(JSON.stringify(examples.groupAtUserMessage, null, 2));
    });

    // 卡片消息示例
    const cardMessageExample = {
        "data" : {
            "templateId" : "templateMsgCard",
            "sessionType" : 1,
            "templateType" : 1,
            "width_mode" : "wide",
            "reload" : false,
            "summary" : "",
            "cardData" : {
                "elements" : [
                {
                    "preview" : true,
                    "img_url" : "https://apijoyspace.jd.com/v1/files/IrFPuO2K5qBbUjGA0LiP/link",
                    "scale" : 2.3,
                    "tag" : "img"
                },
                {
                    "tag" : "hr"
                }, 
                {
                    "tag" : "footer",
                    "content" : "来自MCP 平台"
                }],
                "header" : {
                    "theme" : "red",
                    "title" : {
                        "tag" : "plain_text",
                        "content" : "京东价值观"
                    }
                }
            },
            "forward" : {
                "reload" : false,
                "cardData" : {
                    "key" : "value"
                }
            },
            "callbackData" : {
                "key" : "value"
            },
            "at" : null
        }
    };

    $(document).on('click', '#exampleCardMessage', function() {
        console.log('点击了卡片消息示例按钮');
        $('#sendMessageType').val('2'); // 设置为卡片消息类型
        $('#sendMessageContent').val(JSON.stringify(cardMessageExample, null, 2));
    });
});
