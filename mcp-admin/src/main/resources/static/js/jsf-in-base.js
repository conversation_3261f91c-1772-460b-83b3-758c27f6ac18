/**
 * JSF测试页面脚本 (嵌入版)
 */
$(document).ready(function() {
    console.log('JSF应用脚本已加载，准备初始化...');

    // 检查库是否已加载
    var hljsLoaded = typeof hljs !== 'undefined';
    console.log('库加载状态: highlight.js=' + hljsLoaded);

    // 通用的JSON格式化函数
    function formatJsonData(data) {
        if (typeof data === 'string') {
            try {
                // 尝试解析JSON字符串
                return formatJsonData(JSON.parse(data));
            } catch (e) {
                // 如果不是有效的JSON，则返回原始字符串
                return data;
            }
        } else if (typeof data === 'object' && data !== null) {
            // 处理对象（包括数组）
            const newObj = Array.isArray(data) ? [] : {};
            for (const key in data) {
                if (Object.prototype.hasOwnProperty.call(data, key)) {
                    // 递归处理每个属性
                    newObj[key] = formatJsonData(data[key]);
                }
            }
            return newObj;
        } else {
            // 原始类型直接返回
            return data;
        }
    }

    // 通用的结果显示函数
    function displayResult(response, contentElementId, resultElementId) {
        try {
            // 格式化整个响应
            const formattedResponse = formatJsonData(response);
            
            // 将格式化后的响应转换为字符串并显示
            $(contentElementId).text(JSON.stringify(formattedResponse, null, 2));

            if (typeof hljs !== 'undefined') {
                try {
                    hljs.highlightElement(document.querySelector(contentElementId));
                } catch (e) {
                    console.error('语法高亮失败:', e);
                }
            }
        } catch (e) {
            console.error('JSON解析失败:', e);
            $(contentElementId).text(JSON.stringify(response, null, 2));
        }
        $(resultElementId).show();
    }

    // 获取接口信息
    $('#interfaceInfoForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $('#interfaceInfoLoading').show();
        $('#interfaceInfoResult').hide();

        $.ajax({
            url: '/jsf/getInterfaceInfo',
            type: 'POST',
            data: formData,
            success: function(response) {
                displayResult(response, '#interfaceInfoJsonContent', '#interfaceInfoResult');
            },
            error: function(xhr, status, error) {
                $('#interfaceInfoJsonContent').text('Error: ' + error);
                $('#interfaceInfoResult').show();
            },
            complete: function() {
                $('#interfaceInfoLoading').hide();
            }
        });
    });

    // 获取方法信息
    $('#methodInfoForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $('#methodInfoLoading').show();
        $('#methodInfoResult').hide();

        $.ajax({
            url: '/jsf/getMethodInfo',
            type: 'POST',
            data: formData,
            success: function(response) {
                displayResult(response, '#methodInfoJsonContent', '#methodInfoResult');
            },
            error: function(xhr, status, error) {
                $('#methodInfoJsonContent').text('Error: ' + error);
                $('#methodInfoResult').show();
            },
            complete: function() {
                $('#methodInfoLoading').hide();
            }
        });
    });

    // 获取方法列表
    $('#methodListForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $('#methodListLoading').show();
        $('#methodListResult').hide();

        $.ajax({
            url: '/jsf/getMethodList',
            type: 'POST',
            data: formData,
            success: function(response) {
                displayResult(response, '#methodListJsonContent', '#methodListResult');
                
                // 如果成功获取方法列表，可以提取方法名供其他API使用
                if (response.success && response.data) {
                    try {
                        console.log('原始响应:', response);
                        const formattedResponse = formatJsonData(response);
                        console.log('格式化后的响应:', formattedResponse);
                        if (formattedResponse.data && Array.isArray(formattedResponse.data)) {
                            console.log('准备创建方法下拉菜单，数据:', formattedResponse.data);
                            createMethodDropdown(formattedResponse.data);
                        } else {
                            console.log('数据格式不正确:', formattedResponse.data);
                        }
                    } catch (e) {
                        console.error('处理方法列表失败:', e);
                    }
                } else {
                    console.log('响应不成功或没有数据:', response);
                }
            },
            error: function(xhr, status, error) {
                $('#methodListJsonContent').text('Error: ' + error);
                $('#methodListResult').show();
            },
            complete: function() {
                $('#methodListLoading').hide();
            }
        });
    });

    // 获取别名列表
    $('#aliasListForm').submit(function(e) {
        e.preventDefault();
        const formData = $(this).serialize();

        $('#aliasListLoading').show();
        $('#aliasListResult').hide();

        $.ajax({
            url: '/jsf/getAliasByInterfaceName',
            type: 'POST',
            data: formData,
            success: function(response) {
                displayResult(response, '#aliasListJsonContent', '#aliasListResult');
                
                // 如果成功获取别名列表，可以提取别名供其他API使用
                if (response.success && response.data) {
                    try {
                        const formattedResponse = formatJsonData(response);
                        if (formattedResponse.data && Array.isArray(formattedResponse.data)) {
                            createAliasDropdown(formattedResponse.data);
                        }
                    } catch (e) {
                        console.error('处理别名列表失败:', e);
                    }
                }
            },
            error: function(xhr, status, error) {
                $('#aliasListJsonContent').text('Error: ' + error);
                $('#aliasListResult').show();
            },
            complete: function() {
                $('#aliasListLoading').hide();
            }
        });
    });

    // 从完整的方法签名中提取方法名
    function extractMethodName(methodSignature) {
        if (!methodSignature) return '';

        // 方法签名格式: "返回类型 方法名(参数列表)"
        // 例如: "com.jd.cmc.soa.client.result.BaseResult addActivity(java.util.Map, int, java.lang.String)"

        console.log('原始方法签名:', methodSignature);

        // 找到最后一个空格和第一个左括号之间的内容
        const lastSpaceIndex = methodSignature.lastIndexOf(' ');
        const firstParenIndex = methodSignature.indexOf('(');

        console.log('最后一个空格位置:', lastSpaceIndex);
        console.log('第一个左括号位置:', firstParenIndex);

        if (lastSpaceIndex !== -1 && firstParenIndex !== -1 && lastSpaceIndex < firstParenIndex) {
            const methodName = methodSignature.substring(lastSpaceIndex + 1, firstParenIndex);
            console.log('提取的方法名:', methodName);
            return methodName;
        }

        // 如果没有找到空格，可能是简单的方法名
        if (firstParenIndex !== -1) {
            const methodName = methodSignature.substring(0, firstParenIndex);
            console.log('简单方法名:', methodName);
            return methodName;
        }

        // 兜底返回原字符串
        console.log('兜底返回:', methodSignature);
        return methodSignature;
    }

    // 创建方法下拉菜单
    function createMethodDropdown(methods) {
        console.log('createMethodDropdown 被调用，方法数量:', methods ? methods.length : 0);
        if (!methods || methods.length === 0) {
            console.log('没有方法数据，退出');
            return;
        }

        // 移除已存在的下拉菜单
        $('.method-dropdown').remove();

        // 创建下拉菜单
        const dropdown = $('<div class="mt-3 method-dropdown"><label>选择方法:</label><select class="form-control method-select"></select></div>');
        const select = dropdown.find('select');

        // 添加空选项
        select.append('<option value="">请选择方法</option>');

        // 添加选项
        methods.forEach(method => {
            console.log('添加方法选项:', method);
            select.append(`<option value="${method}">${method}</option>`);
        });

        // 添加事件监听
        select.on('change', function() {
            console.log('方法选择发生变化');
            const methodSignature = $(this).val();
            console.log('选择的方法签名:', methodSignature);
            if (methodSignature) {
                // 从完整的方法签名中提取方法名
                // 例如: "com.jd.cmc.soa.client.result.BaseResult addActivity(java.util.Map, int, java.lang.String)"
                // 提取出: "addActivity"
                const methodName = extractMethodName(methodSignature);
                console.log('准备填充方法名:', methodName);
                // 自动填充到方法信息表单
                $('#methodInfoMethodName').val(methodName);
                console.log('已填充到输入框');
            }
        });

        // 添加到页面
        $('#methodListResult').after(dropdown);
        console.log('下拉菜单已添加到页面');
    }

    // 创建别名下拉菜单
    function createAliasDropdown(aliases) {
        if (!aliases || aliases.length === 0) return;

        // 移除已存在的下拉菜单
        $('.alias-dropdown').remove();

        // 创建下拉菜单
        const dropdown = $('<div class="mt-3 alias-dropdown"><label>选择别名:</label><select class="form-control alias-select"></select></div>');
        const select = dropdown.find('select');

        // 添加空选项
        select.append('<option value="">请选择别名</option>');

        // 添加选项
        aliases.forEach(alias => {
            select.append(`<option value="${alias}">${alias}</option>`);
        });

        // 添加事件监听
        select.on('change', function() {
            const aliasName = $(this).val();
            if (aliasName) {
                // 自动填充到方法信息和方法列表表单
                $('#methodInfoAlias').val(aliasName);
                $('#methodListAlias').val(aliasName);
            }
        });

        // 添加到页面
        $('#aliasListResult').after(dropdown);
    }

    // 添加示例值按钮功能
    $('.add-example').click(function() {
        const target = $(this).data('target');
        const value = $(this).data('value');
        $(target).val(value);
    });

    // 表单间数据同步功能
    function syncInterfaceName() {
        const interfaceName = $(this).val();
        if (interfaceName) {
            // 同步到其他表单
            $('#methodInfoInterfaceName').val(interfaceName);
            $('#methodListInterfaceName').val(interfaceName);
            $('#aliasListInterfaceName').val(interfaceName);
        }
    }

    function syncOperator() {
        const operator = $(this).val();
        if (operator) {
            // 同步到其他表单
            $('#methodInfoOperator').val(operator);
            $('#methodListOperator').val(operator);
            $('#aliasListOperator').val(operator);
        }
    }

    // 绑定同步事件
    $('#interfaceInfoInterfaceName').on('blur', syncInterfaceName);
    $('#interfaceInfoOperator').on('blur', syncOperator);
});
