/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

.card-title {
    margin-bottom: 0;
    font-weight: 600;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    border-top: none;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    font-weight: 500;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 页脚样式 */
.footer {
    margin-top: 50px;
    border-top: 1px solid #eee;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}

/* 紫色主题 */
.bg-purple {
    background-color: #6f42c1 !important;
}

.text-purple {
    color: #6f42c1 !important;
}

.btn-purple {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: #fff;
}

.btn-purple:hover {
    background-color: #5a32a3;
    border-color: #5a32a3;
    color: #fff;
}

/* Toast通知 */
.toast {
    z-index: 1100;
}

/* 服务状态标签 */
.server-status-running {
    color: #28a745;
}

.server-status-stopped {
    color: #6c757d;
}

.server-status-error {
    color: #dc3545;
}

.server-status-starting {
    color: #ffc107;
}

.server-status-stopping {
    color: #fd7e14;
}
