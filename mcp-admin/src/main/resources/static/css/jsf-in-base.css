/**
 * JSF测试页面样式
 */

/* 加载动画样式 */
.loading {
    display: none;
    text-align: center;
    padding: 20px;
}

.loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 结果容器样式 */
.result-container {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
}

.result-container pre {
    margin: 0;
    padding: 1rem;
    background-color: transparent;
    border: none;
    font-size: 0.875rem;
    line-height: 1.5;
}

.result-container code {
    background-color: transparent;
    color: inherit;
    padding: 0;
}

/* 表单样式优化 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-group .text-danger {
    color: #dc3545 !important;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 卡片样式 */
.card {
    margin-bottom: 1.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 1.25rem;
}

.card-header h5 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 500;
    color: #495057;
}

.card-body {
    padding: 1.25rem;
}

/* 标签页样式 */
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    color: #495057;
    padding: 0.5rem 1rem;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #0056b3;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

/* 按钮样式 */
.btn {
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0056b3;
    border-color: #004085;
}

/* 下拉菜单样式 */
.method-dropdown,
.alias-dropdown {
    background-color: #e7f3ff;
    padding: 1rem;
    border-radius: 0.375rem;
    border: 1px solid #b3d9ff;
}

.method-dropdown label,
.alias-dropdown label {
    font-weight: 500;
    color: #0056b3;
    margin-bottom: 0.5rem;
}

.method-select,
.alias-select {
    border: 1px solid #80bdff;
}

.method-select:focus,
.alias-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 警告框样式 */
.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .result-container {
        max-height: 300px;
    }
    
    .nav-tabs .nav-link {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* 语法高亮样式增强 */
.hljs {
    background: #f8f9fa !important;
    color: #495057 !important;
}

/* 滚动条样式 */
.result-container::-webkit-scrollbar {
    width: 8px;
}

.result-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.result-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.result-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
