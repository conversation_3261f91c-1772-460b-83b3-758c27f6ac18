/**
 * 文档中心样式
 */

/* 新首页样式 */
.doc-hero {
    background-color: #f8f9fa;
    padding: 3rem 2rem;
    margin: -2rem -2rem 2rem -2rem;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
}

.doc-hero h1 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #212529;
}

.doc-hero .lead {
    font-size: 1.25rem;
    color: #6c757d;
    max-width: 800px;
    margin: 0 auto;
}

.doc-intro {
    margin-bottom: 3rem;
}

.doc-highlights {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-top: 2rem;
}

.highlight-item {
    flex: 1;
    min-width: 250px;
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.2s;
}

.highlight-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.highlight-icon {
    width: 60px;
    height: 60px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.highlight-icon i {
    font-size: 1.8rem;
    color: #fff;
}

.highlight-item h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.highlight-item p {
    color: #6c757d;
    margin-bottom: 0;
}

.doc-architecture {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin: 2rem 0 3rem 0;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

.arch-diagram {
    flex: 1;
    min-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.arch-diagram img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.arch-description {
    flex: 1;
    min-width: 300px;
}

.arch-description h3 {
    margin-top: 0;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.doc-features {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.feature-item {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    transition: all 0.2s;
}

.feature-item:hover {
    background-color: #e9ecef;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 1.5rem;
    color: #fff;
}

.feature-content {
    flex: 1;
}

.feature-content h3 {
    font-size: 1.2rem;
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.feature-content p {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 0.95rem;
}

.doc-quickstart {
    margin-bottom: 3rem;
}

.quickstart-step {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    transition: all 0.2s;
}

.quickstart-step:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.step-number {
    width: 40px;
    height: 40px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-weight: 600;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h3 {
    font-size: 1.2rem;
    margin-top: 0;
    margin-bottom: 1rem;
    font-weight: 600;
}

.code-block {
    background-color: #212529;
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
    color: #f8f9fa;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.9rem;
}

.code-block code {
    background-color: transparent;
    padding: 0;
    color: inherit;
}

.doc-navigation {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.nav-section {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    transition: all 0.2s;
}

.nav-section:hover {
    background-color: #e9ecef;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.nav-icon {
    width: 40px;
    height: 40px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.nav-icon i {
    font-size: 1.2rem;
    color: #fff;
}

.nav-content {
    flex: 1;
}

.nav-content h3 {
    font-size: 1.1rem;
    margin-top: 0;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.nav-content ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-content li {
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.nav-content a {
    color: #495057;
    text-decoration: none;
    transition: all 0.2s;
}

.nav-content a:hover {
    color: #0d6efd;
    text-decoration: none;
}

.doc-cta {
    text-align: center;
    padding: 3rem 2rem;
    margin: 3rem -2rem -2rem -2rem;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.doc-cta h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.doc-cta p {
    font-size: 1.1rem;
    color: #6c757d;
    max-width: 800px;
    margin: 0 auto 2rem auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.2s;
}

.cta-buttons .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 响应式调整 */
@media (max-width: 992px) {
    .doc-hero {
        padding: 2rem 1rem;
    }

    .doc-hero h1 {
        font-size: 2rem;
    }

    .doc-hero .lead {
        font-size: 1.1rem;
    }

    .doc-features {
        grid-template-columns: 1fr;
    }

    .doc-navigation {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .doc-highlights {
        flex-direction: column;
    }

    .doc-architecture {
        flex-direction: column;
        padding: 1.5rem;
    }

    .feature-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .nav-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .quickstart-step {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}

/* 文档中心容器 */
.docs-container {
    display: flex;
    min-height: calc(100vh - 160px);
    background-color: #fff;
    margin: 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 左侧导航 */
.docs-nav {
    width: 280px;
    flex-shrink: 0;
    background-color: #f8f9fa;
    border-right: 1px solid #e9ecef;
    padding: 0;
    overflow-y: auto;
    height: calc(100vh - 160px);
    position: sticky;
    top: 0;
}

.docs-nav-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.docs-nav-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
}

.docs-env-selector {
    width: 80px;
}

.doc-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.doc-nav-category {
    margin-bottom: 0.5rem;
}

.doc-nav-toggle {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #495057;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
}

.doc-nav-toggle:hover {
    background-color: #e9ecef;
    color: #212529;
    text-decoration: none;
}

.doc-nav-toggle i {
    margin-right: 0.5rem;
}

.doc-nav-toggle .fa-chevron-right {
    margin-left: auto;
    transition: transform 0.3s ease;
}

.doc-nav-toggle .fa-chevron-right.rotate-90 {
    transform: rotate(90deg);
}

.doc-nav-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* 确保菜单保持展开状态 */
.doc-nav-items.show {
    display: block !important;
}

/* 添加菜单动画效果 */
.doc-nav-items {
    overflow: hidden;
    transition: height 0.3s ease;
}

.doc-nav-item {
    display: block;
    padding: 0.5rem 1rem 0.5rem 2.5rem;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.2s;
}

.doc-nav-item:hover {
    background-color: #e9ecef;
    color: #212529;
    text-decoration: none;
}

.doc-nav-item.active {
    background-color: #e9ecef;
    color: #0d6efd;
    font-weight: 500;
}

/* 文档内容区域 */
.docs-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    min-width: 0; /* 防止内容溢出 */
}

/* 面包屑导航 */
.doc-breadcrumb {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.doc-breadcrumb a {
    color: #6c757d;
    text-decoration: none;
}

.doc-breadcrumb a:hover {
    color: #0d6efd;
    text-decoration: none;
}

.doc-breadcrumb i {
    margin: 0 0.5rem;
    color: #adb5bd;
}

.doc-breadcrumb span {
    color: #212529;
}

/* 文档标题 */
.doc-title {
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: 500;
}

/* 文档内容 */
.doc-body {
    font-size: 1rem;
    line-height: 1.6;
    color: #212529;
}

.doc-body h1,
.doc-body h2,
.doc-body h3,
.doc-body h4,
.doc-body h5,
.doc-body h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.doc-body h1 { font-size: 2rem; }
.doc-body h2 { font-size: 1.75rem; }
.doc-body h3 { font-size: 1.5rem; }
.doc-body h4 { font-size: 1.25rem; }
.doc-body h5 { font-size: 1.1rem; }
.doc-body h6 { font-size: 1rem; }

.doc-body p {
    margin-bottom: 1rem;
}

.doc-body code {
    padding: 0.2rem 0.4rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.doc-body pre {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    overflow-x: auto;
}

.doc-body pre code {
    padding: 0;
    background-color: transparent;
}

.doc-body table {
    width: 100%;
    margin-bottom: 1rem;
    border-collapse: collapse;
}

.doc-body th,
.doc-body td {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
}

.doc-body th {
    background-color: #f8f9fa;
    font-weight: 500;
}

/* 欢迎页 */
.doc-welcome-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.doc-category-card {
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.doc-category-card:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.doc-category-icon {
    width: 48px;
    height: 48px;
    background-color: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.doc-category-icon i {
    font-size: 1.5rem;
    color: #fff;
}

.doc-category-card h3 {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
    font-weight: 500;
}

.doc-category-card p {
    margin-bottom: 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.doc-category-card ul {
    list-style: none;
    padding: 0;
    margin: 0 0 1rem 0;
    flex-grow: 1;
}

.doc-category-card li {
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1rem;
}

.doc-category-card li:before {
    content: '\2022';
    position: absolute;
    left: 0;
    color: #0d6efd;
}

.doc-category-card a {
    color: #495057;
    text-decoration: none;
    transition: all 0.2s;
}

.doc-category-card a:hover {
    color: #0d6efd;
    text-decoration: none;
}

.more-link {
    text-align: right;
    margin-top: auto;
}

.more-link a {
    font-size: 0.9rem;
    font-weight: 500;
}

.swagger-card {
    background-color: #f0f8ff;
    border: 1px solid #cce5ff;
}

.swagger-card .doc-category-icon {
    background-color: #0dcaf0;
}

.swagger-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .docs-container {
        flex-direction: column;
    }

    .docs-nav {
        width: 100%;
        height: auto;
        position: relative;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }

    .docs-content {
        padding: 1rem;
    }

    .doc-welcome-categories {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }
}

@media (max-width: 768px) {
    .doc-welcome-categories {
        grid-template-columns: 1fr;
    }

    .doc-title {
        font-size: 1.8rem;
    }

    .doc-body h2 {
        font-size: 1.5rem;
    }

    .doc-body h3 {
        font-size: 1.3rem;
    }
}

/* 加载动画 */
#docLoading {
    padding: 40px 0;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 代码高亮 */
.hljs {
    background-color: #f8f9fa !important;
    padding: 15px !important;
    border-radius: 5px !important;
}

/* 复制按钮 */
.copy-button {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    float: right;
}

/* 分类页面 */
#docCategoryView {
    padding: 1rem 0;
}

.category-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.category-header h1 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.8rem;
}

.category-header p {
    color: #6c757d;
    margin-top: 0.5rem;
}

.category-docs {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.category-doc-item {
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.category-doc-item:hover {
    background-color: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.category-doc-item h3 {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.category-doc-item p {
    margin-bottom: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* 导航项目 */
.doc-nav-home {
    margin-bottom: 1rem;
}

.doc-nav-home-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #495057;
    text-decoration: none;
    transition: all 0.2s;
    font-weight: 500;
}

.doc-nav-home-link:hover {
    background-color: #e9ecef;
    color: #0d6efd;
    text-decoration: none;
}

.doc-nav-home-link i {
    margin-right: 0.5rem;
}

.doc-nav-swagger {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.doc-nav-swagger-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #0dcaf0;
    text-decoration: none;
    transition: all 0.2s;
    font-weight: 500;
}

.doc-nav-swagger-link:hover {
    background-color: #e9ecef;
    color: #0a58ca;
    text-decoration: none;
}

.doc-nav-swagger-link i:first-child {
    margin-right: 0.5rem;
}

.doc-nav-swagger-link i:last-child {
    margin-left: auto;
    font-size: 0.8rem;
}
