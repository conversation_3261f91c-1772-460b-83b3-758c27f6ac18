/* Joyspace测试页面样式 */
.card {
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 15px;
}

.result-container {
    max-height: 500px;
    overflow-y: auto;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.tab-content {
    padding: 15px;
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 5px 5px;
}

.nav-tabs {
    margin-bottom: 0;
}

pre {
    margin: 0;
    white-space: pre-wrap;
}

.markdown-content {
    padding: 15px;
    background-color: white;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.loading {
    display: none;
    text-align: center;
    padding: 20px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .result-container {
        max-height: 300px;
    }
}

/* 美化表单元素 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 美化按钮 */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* 美化结果区域 */
.result-container code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 14px;
}

/* 美化Markdown内容 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.markdown-content p {
    margin-bottom: 1rem;
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.markdown-content table {
    width: 100%;
    margin-bottom: 1rem;
    border-collapse: collapse;
}

.markdown-content table th,
.markdown-content table td {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
}

.markdown-content table th {
    background-color: #f8f9fa;
}

.markdown-content blockquote {
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #dee2e6;
    background-color: #f8f9fa;
}

.markdown-content code {
    padding: 0.2rem 0.4rem;
    background-color: #f8f9fa;
    border-radius: 3px;
}

.markdown-content pre code {
    display: block;
    padding: 1rem;
    overflow-x: auto;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.markdown-content img {
    max-width: 100%;
    height: auto;
}
