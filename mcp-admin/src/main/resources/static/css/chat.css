/* 聊天容器 */
.chat-container {
    max-width: 1200px;
    margin: 0 auto;
    height: calc(100vh - 200px); /* 调整高度以适应AdminLTE框架 */
    display: flex;
    flex-direction: column;
    background-color: #f5f7fb;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f5f7fb;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f5f7fb;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c9d6;
    border-radius: 3px;
}

/* 消息样式 */
.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    justify-content: flex-end;
}

.message-content {
    max-width: 70%;
    padding: 12px 18px;
    border-radius: 15px;
    position: relative;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message.user .message-content {
    background-color: #2979ff;
    color: white;
    margin-left: 50px;
}

.message.assistant .message-content {
    background-color: white;
    margin-right: 50px;
    color: #2c3e50;
}

/* 消息时间 */
.message-time {
    font-size: 0.75em;
    color: rgba(108, 117, 125, 0.8);
    margin-top: 5px;
    text-align: right;
}

.message.user .message-time {
    color: rgba(255, 255, 255, 0.8);
}

/* 打字指示器 */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 8px 0;
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    background-color: #90a4ae;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
    animation: typing 1s infinite;
}

@keyframes typing {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

/* 输入区域 */
.chat-input-container {
    padding: 20px;
    background-color: white;
    border-top: 1px solid #e9ecef;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
    position: relative;
    z-index: 1000;
    border-radius: 0 0 8px 8px;
}

.chat-input-container .input-group {
    background: white;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    max-width: 1000px;
    margin: 0 auto;
}

.chat-input-container .input-group:focus-within {
    box-shadow: 0 4px 12px rgba(41, 121, 255, 0.1);
    transform: translateY(-1px);
}

.chat-input-container input {
    border: none;
    padding: 15px 20px;
    font-size: 1em;
    background-color: white;
    transition: all 0.3s ease;
}

.chat-input-container input:focus {
    box-shadow: none;
    background-color: white;
}

.chat-input-container input::placeholder {
    color: #a0aec0;
}

/* 禁用状态样式 */
.chat-input-container input:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.chat-input-container button:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

.chat-input-container .input-group-append {
    display: flex;
    align-items: stretch;
    margin-left: 0;
}

/* 提供者选择下拉框 */
#provider-select {
    border: none;
    border-left: 1px solid #e9ecef;
    padding: 10px 15px;
    background-color: #f8f9fa;
    color: #495057;
    font-size: 0.9em;
    transition: all 0.3s ease;
    min-width: 200px;
}

#provider-select:focus {
    background-color: #f1f4f8;
    outline: none;
    box-shadow: none;
}

#provider-select option {
    padding: 10px;
}

/* 发送按钮 */
.chat-input-container .btn-primary {
    padding: 12px 25px;
    border-radius: 0 25px 25px 0;
    background-color: #2979ff;
    border-color: #2979ff;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-input-container .btn-primary:hover {
    background-color: #2962ff;
    border-color: #2962ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(41, 121, 255, 0.2);
}

.chat-input-container .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(41, 121, 255, 0.1);
}

.chat-input-container .btn-primary i {
    font-size: 0.9em;
    transition: transform 0.3s ease;
}

.chat-input-container .btn-primary:hover i {
    transform: translateX(2px);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chat-input-container {
        padding: 15px;
    }

    .chat-input-container .input-group {
        flex-wrap: nowrap;
    }

    #provider-select {
        min-width: 140px;
        padding: 8px 10px;
        font-size: 0.85em;
    }

    .chat-input-container .btn-primary {
        padding: 8px 15px;
    }

    .chat-input-container .btn-primary span {
        display: none;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .chat-input-container {
        background-color: #1a1a1a;
        border-top-color: #2d2d2d;
    }

    .chat-input-container .input-group {
        background-color: #2d2d2d;
    }

    .chat-input-container input {
        background-color: #2d2d2d;
        color: #fff;
    }

    .chat-input-container input::placeholder {
        color: #666;
    }

    #provider-select {
        background-color: #333;
        color: #fff;
        border-left-color: #444;
    }

    #provider-select:focus {
        background-color: #404040;
    }
}

/* 函数调用结果 */
.function-result {
    background-color: #f0f7ff;
    border: 1px solid #d0e3ff;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
    font-size: 0.9em;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.function-result-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #d0e3ff;
    color: #2979ff;
    font-weight: 500;
}

.function-result-header i {
    margin-right: 8px;
}

.function-result-content {
    overflow-x: auto;
}

.function-result pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.function-result table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
    background-color: white;
    border-radius: 4px;
    overflow: hidden;
}

.function-result table th,
.function-result table td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #e6e6e6;
}

.function-result table th {
    background-color: #f5f5f5;
    font-weight: 500;
    color: #333;
}

.function-result table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.function-result-empty {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 10px;
}

.function-result-error {
    background-color: #fff0f0;
    border-color: #ffcdd2;
    color: #d32f2f;
}

/* 欢迎消息样式 */
.welcome-message {
    background-color: rgba(41, 121, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.welcome-message h4 {
    color: #2979ff;
    margin-bottom: 15px;
}

.welcome-message ul {
    padding-left: 20px;
    margin-bottom: 0;
}

.welcome-message li {
    margin-bottom: 15px;
    color: #2c3e50;
}

.server-detail {
    font-size: 0.9em;
    color: #5a6a7e;
    margin-top: 5px;
    margin-bottom: 0;
    padding-left: 20px;
    border-left: 2px solid #e0e7ff;
    margin-left: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-container {
        height: calc(100vh - 250px);
    }

    .message-content {
        max-width: 85%;
    }

    .chat-input-container .input-group {
        flex-wrap: nowrap;
    }

    #provider-select {
        width: auto;
    }
}