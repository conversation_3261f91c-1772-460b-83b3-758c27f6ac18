# MCP平台模块结构

## 模块概览

MCP平台由以下主要模块组成：

| 模块名称 | 说明 | 主要职责 |
|---------|------|---------|
| mcp-core | 核心模块 | 定义核心注解、接口和工具类 |
| mcp-server | 服务模块 | 提供各类MCP工具实现 |
| mcp-admin | 管理模块 | 提供Web管理界面和API接口 |
| mcp-generator | 生成器模块 | 生成MCP工具代码和文档 |
| mcp-solon | Solon集成模块 | 提供与Solon框架的集成支持 |

## 模块详细说明

### mcp-core 模块

核心模块，定义了MCP平台的基础注解、接口和工具类。

#### 主要组件

- **核心注解**：提供MCP工具相关的注解支持
  ```java
  @McpEndpoint(name = "name")  // 标记一个类为MCP工具端点，指定工具名称
  @Tool(name = "name", description = "desc")  // 标记一个方法为具体工具实现
  @ToolParamBean  // 标记参数Bean，支持参数校验
  @ToolParam(name = "name", description = "desc")  // 标记单个参数
  ```

- **核心接口**：定义MCP平台的基础接口
  ```java
  // 工具提供者接口
  public interface McpToolProvider {
      String getName();  // 获取提供者名称
      String getDescription();  // 获取提供者描述
      List<McpTool> getTools();  // 获取所有工具
      McpTool getTool(String name);  // 获取指定工具
  }

  // 工具接口
  public interface McpTool {
      String getName();  // 获取工具名称
      String getDescription();  // 获取工具描述
      List<ToolParameter> getParameters();  // 获取参数列表
      Object execute(Map<String, Object> params);  // 执行工具
  }

  // 参数接口
  public interface ToolParameter {
      String getName();  // 获取参数名称
      String getDescription();  // 获取参数描述
      Class<?> getType();  // 获取参数类型
      boolean isRequired();  // 是否必须
      Object getDefaultValue();  // 获取默认值
  }
  ```

- **工具验证器**：负责验证工具参数和执行结果
  ```java
  public class ToolValidator {
      // 验证工具参数
      public void validateToolParameters(Map<String, Object> params) {
          // 参数存在性检查
          // 参数类型检查
          // 参数值校验
      }

      // 验证工具结果
      public void validateToolResult(Object result) {
          // 结果类型检查
          // 结果值校验
      }

      // 验证参数Bean
      public void validateParamBean(Object bean) {
          // Bean属性校验
          // Bean约束检查
      }
  }
  ```

#### 依赖关系

- 不依赖其他模块
- 被其他所有模块依赖

### mcp-server 模块

服务实现模块，提供各类MCP工具实现。

#### 主要组件

- **工具实现类**：提供具体的工具实现
  ```java
  @Component
  @McpEndpoint(name = "weather")
  public class WeatherMcpTool {
      @Tool(name = "weather", description = "提供全球城市天气查询服务")
      public Map<String, Object> weather(@ToolParamBean Params params) {
          // 工具实现逻辑
      }
  }
  ```

#### 依赖关系

- 依赖mcp-core模块
- 被mcp-admin模块依赖

### mcp-admin 模块

Web管理模块，提供管理界面和API接口。

#### 主要组件

- **模板资源**：提供Web界面模板
  - 主页模板 (home.html)：展示工具列表和系统状态
  - 文档页面模板 (docs.html)：展示API文档和使用说明
  - 工具详情页模板 (server-detail.html)：展示工具的详细信息
  - 聊天界面模板 (chat.html)：提供交互式工具调用界面
  - API测试页面 (api-test.html)：提供API测试功能

- **静态资源**：提供Web界面所需的静态文件
  - CSS样式文件
    - admin.css：管理界面样式
    - chat.css：聊天界面样式
    - docs.css：文档页面样式
  - JavaScript脚本
    - 工具管理脚本
    - SSE连接处理脚本
    - API调用脚本
  - 文档Markdown文件
    - 系统文档：介绍系统架构和模块
    - API文档：介绍API使用方法
    - 工具文档：介绍各个工具的功能

- **配置文件**：提供系统配置
  - application.yml：主配置文件
  - application-{env}.yml：环境特定配置
  - servers/*.properties：工具服务器配置
  - logback-spring.xml：日志配置

#### 功能特性

- **Web管理界面**
  - 工具列表展示
  - 工具详情查看
  - 工具执行测试
  - API文档浏览
  - 系统状态监控

- **API接口**
  - SSE长连接支持
  - 工具执行接口
  - 工具状态查询
  - 文档获取接口

- **文档系统**
  - Markdown文档支持
  - 实时文档渲染
  - API文档自动生成
  - 工具文档管理

#### 依赖关系

- 依赖mcp-core模块
- 依赖mcp-server模块
- 可选依赖mcp-generator模块

### mcp-generator 模块

代码生成器模块，用于生成MCP工具代码和文档。

#### 主要组件

- **命令行工具**：提供命令行接口
  ```java
  @Command(name = "generate", description = "Generate MCP tool code")
  public class GenerateEnhancedMcpServerCommand implements Runnable {
      @Option(names = {"-c", "--config"}, description = "Configuration file path")
      private String configPath;
      
      @Override
      public void run() {
          // 生成代码
      }
  }
  ```

- **配置加载器**：加载工具配置
  ```java
  public class ConfigLoader {
      public McpServerConfig loadConfig(String path) {
          // 加载YAML配置
          // 解析配置
      }
  }
  ```

#### 依赖关系

- 依赖mcp-core模块
- 可选被mcp-admin模块依赖

### mcp-solon 模块

Solon框架集成模块，提供与Solon框架的集成支持。

#### 主要组件

- **McpMethodToolProvider**：基于Solon方法的工具提供者
  ```java
  public class McpMethodToolProvider implements McpToolProvider {
      private final List<McpTool> tools;
      
      public McpMethodToolProvider(Object bean) {
          // 扫描bean方法
          // 创建工具列表
      }
  }
  ```

#### 依赖关系

- 依赖mcp-core模块
- 依赖Solon框架

## 模块间交互

### 工具注册流程

1. 使用@McpEndpoint注解标记工具类
2. 使用@Tool注解标记工具方法
3. Spring自动扫描并注册工具
4. SSE控制器监听工具更新

### 工具执行流程

1. 客户端发起工具执行请求
   - 通过HTTP API调用
   - 通过SSE长连接调用
   - 通过Web界面调用

2. 请求参数处理
   - 参数解析和转换
   - 参数类型验证
   - 参数值校验
   - 参数Bean封装

3. 工具查找和验证
   - 根据工具名称查找工具实例
   - 验证工具是否可用
   - 检查工具执行权限
   - 准备工具执行环境

4. 工具方法执行
   - 注入依赖对象
   - 调用工具方法
   - 异常处理和包装
   - 执行结果验证

5. 结果处理和返回
   - 结果格式转换
   - 结果数据包装
   - 错误信息处理
   - 返回客户端

6. 执行日志记录
   - 记录执行时间
   - 记录执行参数
   - 记录执行结果
   - 记录错误信息

### 代码生成流程

1. 配置文件处理
   - 读取YAML配置文件
   - 验证配置格式
   - 解析配置参数
   - 加载模板文件

2. 工具定义解析
   - 解析工具名称和描述
   - 解析工具参数定义
   - 解析工具返回值定义
   - 验证定义完整性

3. 代码生成
   - 生成工具类代码
   - 生成参数Bean代码
   - 生成工具接口代码
   - 生成工具实现代码

4. API接口生成
   - 生成API控制器
   - 生成请求/响应模型
   - 生成API文档
   - 生成测试用例

5. 文档生成
   - 生成工具使用文档
   - 生成API接口文档
   - 生成配置说明文档
   - 生成示例代码

6. 资源处理
   - 复制静态资源
   - 生成配置文件
   - 生成部署脚本
   - 更新依赖配置

## 扩展机制

### 工具扩展

1. 工具类扩展
   - 创建新的工具类
   - 使用@McpEndpoint注解标记
   - 实现工具方法并使用@Tool注解
   - 配置工具属性和依赖

2. 工具方法扩展
   - 定义方法参数和返回值
   - 添加参数校验注解
   - 实现业务逻辑
   - 处理异常情况

3. 工具提供者扩展
   - 实现McpToolProvider接口
   - 注册工具实例
   - 管理工具生命周期
   - 处理工具状态

### 参数扩展

1. 参数Bean扩展
   - 创建参数Bean类
   - 使用@ToolParamBean注解标记
   - 定义参数属性和约束
   - 实现参数验证逻辑

2. 参数验证扩展
   - 创建自定义验证注解
   - 实现验证器逻辑
   - 注册验证器
   - 配置验证规则

3. 参数转换扩展
   - 实现类型转换器
   - 注册转换器
   - 处理特殊格式
   - 处理默认值

### 生成器扩展

1. 模板扩展
   - 创建自定义模板文件
   - 定义模板变量
   - 实现模板逻辑
   - 配置模板路径

2. 生成逻辑扩展
   - 实现自定义生成器
   - 处理特殊生成需求
   - 集成外部工具
   - 配置生成选项

3. 配置扩展
   - 扩展配置格式
   - 添加配置验证
   - 处理配置继承
   - 支持环境变量
