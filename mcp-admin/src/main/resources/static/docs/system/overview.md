# MCP平台系统概述

## 什么是MCP平台

MCP (Modular Computing Platform) 是一个基于Java Spring Boot的模块化计算平台，提供了一套灵活的服务注册、管理和执行框架。该平台支持多种服务类型，包括API服务、数据库操作、缓存操作、AI服务等，并提供了完整的REST API和MCP协议接口。

MCP平台的核心价值在于：

1. **服务标准化**：提供统一的服务定义、注册和管理机制
2. **接口标准化**：同时支持REST API和MCP协议接口
3. **文档标准化**：自动生成服务文档和API文档
4. **代码生成**：提供代码生成器，快速创建新服务
5. **AI集成**：支持与大模型集成，实现智能服务

## 系统架构

MCP平台采用模块化设计，主要包含以下模块：

```
mcp-platform/
├── mcp-core/           # 核心接口和抽象类
├── mcp-server/         # 服务实现和工具类
├── mcp-admin/          # Web服务器和管理后台
└── mcp-generator/      # 代码生成器
```

### 模块说明

#### mcp-core

核心模块，定义了MCP平台的基础接口和抽象类，包括：

- `McpServer`：服务接口，定义了服务的基本操作
- `AbstractMcpServer`：服务抽象类，实现了服务接口的通用方法
- `McpServerManager`：服务管理器，负责服务的注册和获取
- `McpServerStatus`：服务状态枚举
- `McpServerTool`：MCP协议工具接口

#### mcp-server

服务实现模块，包含各种服务实现和工具类，包括：

- 各种服务实现类（如天气服务、数据库服务等）
- 服务工具类
- MCP协议工具实现类

#### mcp-admin

Web服务器和管理后台模块，提供了Web界面和API接口，包括：

- REST API控制器
- MCP协议控制器
- 文档控制器
- 管理后台界面
- 静态资源和配置文件

#### mcp-generator

代码生成器模块，用于生成服务代码模板，包括：

- 服务类生成
- API控制器生成
- MCP工具类生成
- 文档生成

## 技术栈

MCP平台使用了以下技术栈：

- **后端**：Java 8+, Spring Boot 2.7
- **前端**：Thymeleaf, Bootstrap 5, jQuery
- **API文档**：Swagger/OpenAPI 3
- **MCP协议**：Solon AI MCP
- **构建工具**：Maven

## 系统特点

### 1. 模块化设计

MCP平台采用模块化设计，各个模块职责明确，便于扩展和维护。

### 2. 统一服务管理

提供统一的服务注册和管理机制，所有服务都通过`McpServerManager`进行管理。

### 3. 双重接口支持

同时支持REST API和MCP协议接口，满足不同场景的需求：

- REST API：适用于传统的HTTP请求
- MCP协议：适用于与大模型集成

### 4. 自动文档生成

自动生成服务文档和API文档，包括：

- 服务说明文档
- REST API文档
- MCP协议文档

### 5. 代码生成器

提供代码生成器，快速创建新服务，减少重复工作。

### 6. 大模型集成

支持与大模型集成，实现智能服务，如：

- 通过MCP协议与Cline集成
- 支持函数调用功能

## 系统流程

### 服务注册流程

1. 服务实现类继承`AbstractMcpServer`，实现服务逻辑
2. 服务类使用`@Component`注解，由Spring容器管理
3. 服务启动时，通过`McpServerManager`注册到系统中
4. 服务注册后，可以通过API接口访问和执行

### 服务执行流程

1. 客户端通过API接口发送请求
2. 控制器接收请求，解析参数
3. 控制器通过`McpServerManager`获取对应的服务
4. 控制器调用服务的`execute`方法，执行服务逻辑
5. 服务执行完成后，返回结果
6. 控制器将结果封装为响应，返回给客户端

### MCP协议流程

1. 客户端通过SSE连接建立长连接
2. 服务端发送`tools_available`事件，包含可用工具列表
3. 客户端发送执行请求，包含工具ID和参数
4. 服务端执行工具，返回结果
5. 服务端发送`response`事件，包含执行结果

## 部署要求

MCP平台的部署要求如下：

- **JDK**: 8+
- **内存**: 2GB+
- **磁盘**: 1GB+
- **网络**: 支持HTTP/HTTPS

## 总结

MCP平台是一个功能强大、灵活可扩展的模块化计算平台，提供了统一的服务管理和接口规范，支持与大模型集成，适用于各种业务场景。通过代码生成器和自动文档生成，大大提高了开发效率和系统可维护性。
