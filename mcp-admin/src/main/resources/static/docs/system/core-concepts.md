# MCP平台核心概念

## MCP服务

MCP服务是平台的核心概念，代表一个可执行的功能单元。每个MCP服务都有唯一的ID、名称、描述、版本和类型，并提供execute方法执行服务逻辑。

### 服务属性

- **ID**：服务的唯一标识符，如"weather"、"mysql"等
- **名称**：服务的显示名称，如"天气服务"、"MySQL服务"等
- **描述**：服务的详细描述，说明服务的功能和用途
- **版本**：服务的版本号，如"1.0.0"
- **类型**：服务的类型，如"API"、"DATABASE"、"CACHE"、"AI"等

### 服务状态

服务状态表示服务当前的运行状态，包括：

- **STARTING**：服务正在启动
- **RUNNING**：服务正在运行
- **STOPPING**：服务正在停止
- **STOPPED**：服务已停止
- **ERROR**：服务出错

### 服务参数

服务参数是执行服务时需要提供的输入参数，每个参数都有名称、类型、描述和是否必填等属性。

例如，天气服务的参数：

```json
{
  "city": "Beijing"
}
```

### 服务输出

服务输出是服务执行后返回的结果，通常是一个JSON对象，包含多个字段。

例如，天气服务的输出：

```json
{
  "city": "Beijing",
  "temperature": 25.5,
  "humidity": 60,
  "pressure": 1013,
  "windSpeed": 3.5,
  "description": "晴天",
  "timestamp": 1619712345678
}
```

## MCP协议

MCP协议是MCP平台与大模型集成的标准协议，基于Server-Sent Events (SSE)和JSON-RPC 2.0，支持工具注册、工具执行和结果返回。

### 协议组件

- **SSE连接**：客户端与服务端建立长连接
- **工具注册**：服务端向客户端发送可用工具列表
- **工具执行**：客户端向服务端发送执行请求
- **结果返回**：服务端向客户端返回执行结果

### 协议流程

1. 客户端通过SSE连接建立长连接
2. 服务端发送`tools_available`事件，包含可用工具列表
3. 客户端发送执行请求，包含工具ID和参数
4. 服务端执行工具，返回结果
5. 服务端发送`response`事件，包含执行结果

### 协议示例

**工具列表事件**：

```json
event: tools_available
data: {
  "tools": [
    {
      "id": "weather",
      "name": "天气服务",
      "description": "提供全球城市天气查询服务",
      "parameters": {
        "type": "object",
        "properties": {
          "city": {
            "type": "string",
            "description": "城市名称"
          }
        },
        "required": ["city"]
      }
    }
  ]
}
```

**执行请求**：

```json
{
  "jsonrpc": "2.0",
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "method": "weather",
  "params": {
    "city": "Beijing"
  }
}
```

**执行结果**：

```json
event: response
data: {
  "jsonrpc": "2.0",
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "result": {
    "city": "Beijing",
    "temperature": 25.5,
    "humidity": 60,
    "pressure": 1013,
    "windSpeed": 3.5,
    "description": "晴天",
    "timestamp": 1619712345678
  }
}
```

## REST API

REST API是MCP平台提供的传统HTTP接口，仅支持POST方法，用于执行服务和获取结果。

### API路径

每个服务都有对应的API路径，格式为：

```
/api/{serviceId}
```

例如，天气服务的API路径为：

```
/api/weather
```

### API方法

- **POST**：通过请求体传递服务参数

### API参数

- **POST方法**：通过JSON请求体传递，如`{"city": "Beijing"}`

### API响应

API响应是一个JSON对象，包含以下字段：

- **code**：响应码，0表示成功，非0表示错误
- **message**：响应消息，成功或错误描述
- **data**：响应数据，服务执行结果

例如：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "city": "Beijing",
    "temperature": 25.5,
    "humidity": 60,
    "pressure": 1013,
    "windSpeed": 3.5,
    "description": "晴天",
    "timestamp": 1619712345678
  }
}
```

## 服务类型

MCP平台支持多种服务类型，每种类型都有特定的功能和用途。

### API服务

提供外部API调用功能，如天气查询、翻译等。

特点：
- 通过HTTP请求调用外部API
- 支持参数传递和结果解析
- 支持缓存和重试机制

### 数据库服务

提供数据库操作功能，如MySQL、PostgreSQL等。

特点：
- 支持SQL查询和更新
- 支持参数绑定和结果映射
- 支持事务和连接池

### 缓存服务

提供缓存操作功能，如Redis、Memcached等。

特点：
- 支持键值对操作
- 支持过期时间设置
- 支持批量操作

### AI服务

提供AI模型调用功能，如文本生成、图像识别等。

特点：
- 支持模型参数配置
- 支持流式响应
- 支持令牌计数和费用计算

## 代码生成器

代码生成器是MCP平台提供的工具，用于快速生成服务代码和文档，提高开发效率。

### 生成内容

- **服务类**：继承AbstractMcpServer的服务实现类
- **API控制器**：提供REST API接口的控制器
- **MCP工具类**：提供MCP协议接口的工具类
- **单元测试**：服务的单元测试类
- **配置文件**：服务的配置文件
- **文档**：服务的说明文档

### 配置文件

代码生成器使用YAML配置文件定义服务信息，包括：

- **服务基本信息**：ID、名称、描述、版本、类型等
- **服务参数**：参数名称、类型、描述、是否必填等
- **服务输出**：输出字段名称、类型、描述等
- **API配置**：API路径、方法、参数等
- **生成器配置**：是否生成测试、是否覆盖已有文件等

例如：

```yaml
server:
  id: weather
  name: 天气服务
  description: 提供全球城市天气查询服务
  version: 1.0.0
  type: API
  usage: 查询指定城市的天气信息

params:
  - name: city
    type: string
    description: 城市名称，如：Beijing, Shanghai, New York等
    required: true

output:
  fields:
    - name: city
      type: string
      description: 城市名称
    - name: temperature
      type: number
      description: 温度，单位：摄氏度
    # ...

api:
  enabled: true
  path: /api/weather
  method: GET
  # ...

generator:
  overwrite: true
  generateTest: true
  # ...
```

### 生成命令

使用以下命令生成服务代码和文档：

```bash
cd mcp-generator
mvn spring-boot:run -Dspring-boot.run.arguments="generate-enhanced -c src/main/resources/examples/weather-service.yml"
```

## 文档系统

文档系统是MCP平台提供的功能，用于展示系统和服务的说明文档，帮助用户了解和使用平台。

### 文档类型

- **系统文档**：系统概述、模块结构、核心概念等
- **开发文档**：开发指南、代码生成器使用说明等
- **服务文档**：各个服务的详细说明和使用方法
- **API文档**：REST API和MCP协议接口文档

### 文档格式

文档使用Markdown格式编写，支持以下功能：

- 标题和段落
- 列表和表格
- 代码块和引用
- 链接和图片

### 文档访问

通过以下URL访问文档系统：

```
http://localhost:8081/enhanced-docs
```

### 文档生成

服务文档由代码生成器自动生成，包括：

- 服务概述
- 功能说明
- 参数说明
- 输出说明
- 配置说明
- REST API接口
- MCP协议接口
- 常见问题
