# MCP平台开发指南

## 开发环境准备

### 系统要求

- **操作系统**：Windows、macOS或Linux
- **JDK**：Java 8+（推荐使用JDK 8）
- **Maven**：3.6+
- **IDE**：IntelliJ IDEA或Eclipse（推荐使用IntelliJ IDEA）
- **Git**：2.0+

### 环境配置

1. **安装JDK 8**
   - 下载并安装JDK 8
   - 配置JAVA_HOME环境变量
   - 将JDK的bin目录添加到PATH环境变量

2. **安装Maven**
   - 下载并解压Maven
   - 配置MAVEN_HOME环境变量
   - 将Maven的bin目录添加到PATH环境变量
   - 配置Maven的settings.xml，添加私服仓库配置

3. **安装IDE**
   - 下载并安装IntelliJ IDEA或Eclipse
   - 配置JDK和Maven

4. **安装Git**
   - 下载并安装Git
   - 配置Git用户名和邮箱

## 项目结构

MCP平台项目结构如下：

```
mcp-platform/
├── mcp-core/           # 核心接口和抽象类
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/   # Java源代码
│   │   │   └── resources/ # 资源文件
│   │   └── test/       # 测试代码
│   └── pom.xml         # Maven配置文件
├── mcp-server/         # 服务实现和工具类
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/   # Java源代码
│   │   │   └── resources/ # 资源文件
│   │   └── test/       # 测试代码
│   └── pom.xml         # Maven配置文件
├── mcp-admin/          # Web服务器和管理后台
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/   # Java源代码
│   │   │   ├── resources/ # 资源文件
│   │   │   └── webapp/ # Web资源
│   │   └── test/       # 测试代码
│   └── pom.xml         # Maven配置文件
├── mcp-generator/      # 代码生成器
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/   # Java源代码
│   │   │   └── resources/ # 资源文件
│   │   └── test/       # 测试代码
│   └── pom.xml         # Maven配置文件
└── pom.xml             # 父POM文件
```

## 获取代码

### 克隆代码库

```bash
git clone <repository-url>
cd mcp-platform
```

### 分支说明

- **main**：主分支，稳定版本
- **develop**：开发分支，最新功能
- **feature/xxx**：功能分支，开发新功能
- **bugfix/xxx**：修复分支，修复bug

## 构建和运行

### 构建项目

使用以下命令构建整个项目：

```bash
# 使用JDK 8构建和启动
make devrun
```

或者手动执行Maven命令：

```bash
mvn clean install
```

### 运行项目

使用以下命令运行项目：

```bash
cd mcp-admin
mvn spring-boot:run
```

或者使用debug
![DEBUG](running_debug.png)


### 访问项目

项目启动后，可以通过以下URL访问：

- **管理后台**：http://localhost:8001
- **文档中心**：http://localhost:8001/enhanced-docs
- **Swagger UI**：http://localhost:8001/swagger-ui.html

## 开发新服务

### 方法一：使用代码生成器（推荐）

1. **创建配置文件**

   创建一个YAML配置文件，定义服务信息，例如：

   ```yaml
   server:
     id: example
     name: 示例服务
     description: 这是一个示例服务
     version: 1.0.0
     type: API
     usage: 用于演示如何创建新服务

   params:
     - name: param1
       type: string
       description: 参数1
       required: true
     - name: param2
       type: number
       description: 参数2
       required: false

   output:
     fields:
       - name: result
         type: string
         description: 结果
       - name: timestamp
         type: number
         description: 时间戳

   api:
     enabled: true
     path: /api/example
     method: GET

   generator:
     overwrite: true
     generateTest: true
   ```

2. **运行代码生成器**

   ```bash
   cd mcp-generator
   mvn spring-boot:run -Dspring-boot.run.arguments="generate-enhanced -c path/to/your/config.yml"
   ```

3. **实现服务逻辑**

   生成的服务类中，实现`doExecute`方法：

   ```java
   @Override
   protected Object doExecute(Map<String, Object> params) {
       // 获取参数
       String param1 = (String) params.get("param1");
       Number param2 = (Number) params.getOrDefault("param2", 0);

       // 实现业务逻辑
       Map<String, Object> result = new HashMap<>();
       result.put("result", "处理结果: " + param1 + ", " + param2);
       result.put("timestamp", System.currentTimeMillis());

       return result;
   }
   ```

4. **测试服务**

   运行生成的单元测试类，或者通过API接口测试服务。

### 方法二：手动创建

1. **创建服务类**

   在`mcp-server`模块中创建一个新的服务类：

   ```java
   package com.jdl.mcp.server.servers.example;

   import com.jdl.mcp.core.server.AbstractMcpServer;
   import org.springframework.stereotype.Component;

   import java.util.HashMap;
   import java.util.Map;

   @Component
   public class ExampleMcpServer extends AbstractMcpServer {

       public ExampleMcpServer() {
           super("example", "示例服务", "1.0.0");
           setType("API");
           setUsage("用于演示如何创建新服务");
       }

       @Override
       protected Object doExecute(Map<String, Object> params) {
           // 获取参数
           String param1 = (String) params.get("param1");
           Number param2 = (Number) params.getOrDefault("param2", 0);

           // 实现业务逻辑
           Map<String, Object> result = new HashMap<>();
           result.put("result", "处理结果: " + param1 + ", " + param2);
           result.put("timestamp", System.currentTimeMillis());

           return result;
       }
   }
   ```

2. **创建API控制器**

   在`mcp-admin`模块中创建一个新的API控制器：

   ```java
   package com.jdl.mcp.admin.controller.api;

   import com.jdl.mcp.core.server.McpServer;
   import com.jdl.mcp.core.server.McpServerManager;
   import org.springframework.beans.factory.annotation.Autowired;
   import org.springframework.web.bind.annotation.*;

   import java.util.HashMap;
   import java.util.Map;

   @RestController
   @RequestMapping("/api/example")
   public class ExampleApiController {

       @Autowired
       private McpServerManager mcpServerManager;

       @GetMapping("")
       public Map<String, Object> executeGet(
           @RequestParam(value = "param1", required = true) String param1,
           @RequestParam(value = "param2", required = false, defaultValue = "0") Integer param2
       ) {
           try {
               // 获取服务
               McpServer server = mcpServerManager.getServer("example");
               if (server == null) {
                   Map<String, Object> result = new HashMap<>();
                   result.put("success", false);
                   result.put("message", "服务不可用: example");
                   result.put("code", 404);
                   return result;
               }

               // 构建参数
               Map<String, Object> params = new HashMap<>();
               params.put("param1", param1);
               params.put("param2", param2);

               // 执行服务
               Object response = server.execute(params);

               // 构建成功响应
               Map<String, Object> result = new HashMap<>();
               result.put("success", true);
               result.put("message", "操作成功");
               result.put("data", response);
               result.put("code", 0);

               return result;
           } catch (Exception e) {
               // 构建错误响应
               Map<String, Object> result = new HashMap<>();
               result.put("success", false);
               result.put("message", "执行失败: " + e.getMessage());
               result.put("code", 500);

               return result;
           }
       }
   }
   ```

3. **创建MCP工具类**

   在`mcp-server`模块中创建一个新的MCP工具类：

   ```java
   package com.jdl.mcp.server.tools;

   import com.jdl.mcp.core.tools.McpServerToolBase;
   import org.noear.solon.ai.chat.annotation.ToolMapping;
   import org.noear.solon.ai.chat.annotation.ToolParam;
   import org.noear.solon.ai.mcp.server.annotation.McpServerEndpoint;
   import org.springframework.stereotype.Component;

   import java.util.HashMap;
   import java.util.Map;

   @Component
   @McpServerEndpoint(sseEndpoint = "/mcp/example/sse")
   public class ExampleMcpServerTool extends McpServerToolBase {

       @ToolMapping(name = "example", description = "这是一个示例服务")
       public Object handleExample(
           @ToolParam(name = "param1", description = "参数1") String param1,
           @ToolParam(name = "param2", description = "参数2") Integer param2
       ) {
           Map<String, Object> params = new HashMap<>();
           params.put("param1", param1);
           params.put("param2", param2);
           return executeServer("example", params);
       }
   }
   ```

4. **创建文档**

   在`mcp-admin`模块中创建一个新的文档文件：

   ```markdown
   # 示例服务

   ## 1. 服务概述

   - **服务ID**: example
   - **版本**: 1.0.0
   - **类型**: API

   这是一个示例服务，用于演示如何创建新服务。

   ## 2. 功能说明

   用于演示如何创建新服务。

   ## 3. 参数说明

   | 参数名 | 类型 | 必填 | 默认值 | 说明 |
   |-------|------|------|--------|------|
   | param1 | string | 是 | - | 参数1 |
   | param2 | number | 否 | 0 | 参数2 |

   ## 4. 输出说明

   | 字段名 | 类型 | 说明 |
   |-------|------|------|
   | result | String | 结果 |
   | timestamp | Long | 时间戳 |

   ## 5. REST API接口

   ### 5.1 基本信息

   - **路径**: /api/example
   - **方法**: GET
   - **说明**: 这是一个示例服务

   ### 5.2 API参数说明

   **查询参数**:

   | 参数名 | 类型 | 必填 | 说明 |
   |-------|------|------|------|
   | param1 | String | 是 | 参数1 |
   | param2 | Integer | 否 | 参数2 |

   ### 5.3 响应格式

   ```json
   {
     "code": 0,
     "message": "success",
     "data": {
       "result": "处理结果: param1, param2",
       "timestamp": 1619712345678
     }
   }
   ```

   ## 6. MCP协议接口

   ### 6.1 统一SSE端点

   - **URL**: `/mcp/sse`
   - **方法**: GET
   - **内容类型**: text/event-stream

   ### 6.2 单独SSE端点

   - **URL**: `/mcp/example/sse`
   - **方法**: GET
   - **内容类型**: text/event-stream

   ### 6.3 工具信息

   - **工具ID**: `example`
   - **描述**: `这是一个示例服务`
   - **参数**:
     - **param1**: 参数1 (string, 必填)
     - **param2**: 参数2 (number, 可选)
   ```

## 测试服务

### 单元测试

创建单元测试类，测试服务逻辑：

```java
package com.jdl.mcp.server.servers.example;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class ExampleMcpServerTest {

    @Autowired
    private ExampleMcpServer exampleMcpServer;

    @Test
    public void testExecute() {
        // 准备参数
        Map<String, Object> params = new HashMap<>();
        params.put("param1", "test");
        params.put("param2", 123);

        // 执行服务
        Object result = exampleMcpServer.execute(params);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof Map);
        Map<String, Object> resultMap = (Map<String, Object>) result;
        assertTrue(resultMap.containsKey("result"));
        assertTrue(resultMap.containsKey("timestamp"));
    }
}
```

### API测试

使用Swagger UI或Postman测试API接口：

- **GET方法**：http://localhost:8081/api/example?param1=test&param2=123
- **POST方法**：http://localhost:8081/api/example
  ```json
  {
    "param1": "test",
    "param2": 123
  }
  ```

### MCP协议测试

使用MCP SSE测试页面测试MCP协议接口：

1. 访问http://localhost:8081/mcp-sse-test.html
2. 连接SSE端点：http://localhost:8081/mcp/example/sse
3. 发送执行请求：
   ```json
   {
     "jsonrpc": "2.0",
     "id": "test-id",
     "method": "example",
     "params": {
       "param1": "test",
       "param2": 123
     }
   }
   ```

## 调试技巧

### 日志调试

在代码中添加日志语句，帮助调试：

```java
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

private static final Logger logger = LoggerFactory.getLogger(ExampleMcpServer.class);

// 在方法中使用
logger.debug("参数: {}", params);
logger.info("执行服务: {}", getId());
logger.error("执行失败: {}", e.getMessage(), e);
```

### 远程调试

配置远程调试参数：

```bash
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar mcp-admin/target/mcp-admin.jar
```

然后在IDE中配置远程调试，连接到5005端口。

### 热部署

使用Spring Boot DevTools实现热部署：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-devtools</artifactId>
    <optional>true</optional>
</dependency>
```

## 最佳实践

### 代码规范

- 遵循Java编码规范
- 使用驼峰命名法
- 添加适当的注释
- 使用统一的代码格式化工具

### 异常处理

- 使用自定义异常类
- 在服务类中捕获和处理异常
- 返回友好的错误信息
- 记录详细的错误日志

### 参数验证

- 在服务类中验证参数
- 使用断言或验证工具
- 返回明确的错误信息
- 处理空值和默认值

### 文档编写

- 使用Markdown格式编写文档
- 包含服务概述、参数说明、输出说明等
- 提供API示例和MCP协议示例
- 更新文档以反映最新变化

## 常见问题

### Q: 如何解决依赖冲突？

A: 使用Maven的依赖排除机制：

```xml
<dependency>
    <groupId>com.example</groupId>
    <artifactId>example-lib</artifactId>
    <version>1.0.0</version>
    <exclusions>
        <exclusion>
            <groupId>org.conflicting</groupId>
            <artifactId>conflicting-lib</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### Q: 如何配置不同环境的属性？

A: 使用Spring Boot的Profile机制：

```yaml
# application.yml
spring:
  profiles:
    active: dev

---
# application-dev.yml
spring:
  config:
    activate:
      on-profile: dev
server:
  port: 8081

---
# application-prod.yml
spring:
  config:
    activate:
      on-profile: prod
server:
  port: 80
```

### Q: 如何添加新的依赖？

A: 在对应模块的pom.xml中添加依赖：

```xml
<dependency>
    <groupId>com.example</groupId>
    <artifactId>example-lib</artifactId>
    <version>1.0.0</version>
</dependency>
```

### Q: 如何解决JDK版本问题？

A: 确保使用JDK 8，并配置Maven编译插件：

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-compiler-plugin</artifactId>
    <version>3.8.1</version>
    <configuration>
        <source>1.8</source>
        <target>1.8</target>
    </configuration>
</plugin>
```

## 参考资源

- [Spring Boot文档](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [Spring Framework文档](https://docs.spring.io/spring-framework/docs/current/reference/html/)
- [Maven文档](https://maven.apache.org/guides/index.html)
- [Solon AI MCP文档](https://github.com/noear/solon-ai)
- [Swagger/OpenAPI文档](https://swagger.io/docs/)
