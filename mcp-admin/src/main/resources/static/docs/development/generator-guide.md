# MCP平台代码生成器使用说明

## 概述

MCP平台代码生成器是一个强大的工具，用于快速生成MCP服务的代码和文档。它可以根据配置文件自动生成服务类、API控制器、MCP工具类、单元测试和文档，大大提高开发效率。

本文档将详细介绍代码生成器的使用方法、配置选项和最佳实践。

## 功能特点

- **一键生成**：一次配置，自动生成所有必要的代码和文档
- **模板化**：使用FreeMarker模板引擎，支持自定义模板
- **配置灵活**：支持YAML配置文件，可以灵活定义服务属性
- **多种服务类型**：支持API、数据库、缓存、AI等多种服务类型
- **文档生成**：自动生成服务文档，包括REST API和MCP协议接口文档
- **单元测试**：自动生成单元测试类，方便测试服务逻辑

## 安装和配置

代码生成器是MCP平台的一部分，不需要单独安装。只需要确保已经正确安装了MCP平台，并且配置了JDK 8和Maven。

### 环境要求

- **JDK**: 8+（推荐使用JDK 8）
- **Maven**: 3.6+
- **操作系统**: Windows、macOS或Linux

### 验证安装

使用以下命令验证代码生成器是否可用：

```bash
cd mcp-generator
mvn spring-boot:run -Dspring-boot.run.arguments="help"
```

如果安装正确，将显示帮助信息，包括可用的命令和选项。

## 基本使用

### 生成服务

使用以下命令生成服务：

```bash
cd mcp-generator
mvn spring-boot:run -Dspring-boot.run.arguments="generate-enhanced -c path/to/your/config.yml"
```

其中，`path/to/your/config.yml`是配置文件的路径。

### 命令选项

代码生成器支持以下命令选项：

- **generate-enhanced**：生成增强版服务，包括服务类、API控制器、MCP工具类、单元测试和文档
- **help**：显示帮助信息
- **version**：显示版本信息

### 参数选项

- **-c, --config**：指定配置文件路径
- **-o, --output**：指定输出目录（可选，默认使用配置文件中的设置）
- **-f, --force**：强制覆盖已有文件（可选，默认使用配置文件中的设置）

## 配置文件

配置文件是YAML格式，用于定义服务的属性和生成选项。

### 配置文件结构

配置文件包含以下主要部分：

- **server**：服务基本信息
- **params**：服务参数
- **output**：服务输出
- **api**：API配置
- **generator**：生成器配置
- **business**：业务配置

### 配置示例

```yaml
server:
  id: weather
  name: 天气服务
  description: 提供全球城市天气查询服务
  version: 1.0.0
  type: API
  usage: 查询指定城市的天气信息

params:
  - name: city
    type: string
    description: 城市名称，如：Beijing, Shanghai, New York等
    required: true
    example: "Beijing"

output:
  fields:
    - name: city
      type: string
      description: 城市名称
    - name: temperature
      type: number
      description: 温度，单位：摄氏度
    - name: humidity
      type: number
      description: 湿度，单位：百分比
    - name: pressure
      type: number
      description: 气压，单位：百帕
    - name: windSpeed
      type: number
      description: 风速，单位：米/秒
    - name: description
      type: string
      description: 天气描述
    - name: timestamp
      type: number
      description: 时间戳

api:
  enabled: true
  path: /api/weather
  method: GET
  queryParams:
    - name: city
      type: string
      description: 城市名称
      required: true

generator:
  overwrite: true
  generateTest: true
  autoRegister: true
  mockImplementation: true

business:
  enabled: true
  properties:
    api_key: YOUR_API_KEY
    api_url: https://api.example.com/weather
    cache_ttl: 3600
```

### 配置项说明

#### server部分

| 配置项 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| id | string | 是 | 服务的唯一标识符，如"weather" |
| name | string | 是 | 服务的显示名称，如"天气服务" |
| description | string | 是 | 服务的详细描述 |
| version | string | 是 | 服务的版本号，如"1.0.0" |
| type | string | 是 | 服务的类型，如"API"、"DATABASE"、"CACHE"、"AI"等 |
| usage | string | 否 | 服务的使用说明 |

#### params部分

params是一个数组，每个元素包含以下配置项：

| 配置项 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| name | string | 是 | 参数名称 |
| type | string | 是 | 参数类型，如"string"、"number"、"boolean"、"array"、"object"等 |
| description | string | 是 | 参数描述 |
| required | boolean | 是 | 是否必填 |
| example | any | 否 | 参数示例值 |
| defaultValue | any | 否 | 参数默认值 |

#### output部分

output包含一个fields数组，每个元素包含以下配置项：

| 配置项 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| name | string | 是 | 字段名称 |
| type | string | 是 | 字段类型，如"string"、"number"、"boolean"、"array"、"object"等 |
| description | string | 是 | 字段描述 |

#### api部分

| 配置项 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| enabled | boolean | 是 | 是否启用API |
| path | string | 是 | API路径，如"/api/weather" |
| method | string | 是 | API方法，如"GET"、"POST" |
| queryParams | array | 否 | 查询参数数组，每个元素包含name、type、description和required |

#### generator部分

| 配置项 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| overwrite | boolean | 是 | 是否覆盖已有文件 |
| generateTest | boolean | 是 | 是否生成单元测试 |
| autoRegister | boolean | 是 | 是否自动注册服务 |
| mockImplementation | boolean | 是 | 是否生成模拟实现 |

#### business部分

| 配置项 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| enabled | boolean | 是 | 是否启用业务配置 |
| properties | object | 是 | 业务配置属性，键值对形式 |

## 生成的文件

代码生成器会生成以下文件：

### 服务类

```
mcp-server/src/main/java/com/mcp/server/servers/{serviceId}/{ServiceName}McpServer.java
```

服务类是服务的核心实现，继承自AbstractMcpServer，实现了服务的业务逻辑。

### API控制器

```
mcp-admin/src/main/java/com/mcp/admin/controller/api/{ServiceName}ApiController.java
```

API控制器提供REST API接口，支持GET和POST方法，用于执行服务。

### MCP工具类

```
mcp-server/src/main/java/com/mcp/server/tools/{ServiceName}McpServerTool.java
```

MCP工具类提供MCP协议接口，支持SSE连接和工具执行，用于与大模型集成。

### 单元测试

```
mcp-server/src/test/java/com/mcp/server/servers/{serviceId}/{ServiceName}McpServerTest.java
```

单元测试类用于测试服务的业务逻辑，包含基本的测试用例。

### 业务配置文件

```
mcp-admin/src/main/resources/servers/{serviceId}.properties
```

业务配置文件包含服务的业务配置属性，如API密钥、URL等。

### 服务文档

```
mcp-admin/src/main/resources/static/docs/servers/{serviceId}.md
```

服务文档包含服务的详细说明，包括服务概述、参数说明、输出说明、REST API接口和MCP协议接口等。

## 自定义模板

代码生成器使用FreeMarker模板引擎，支持自定义模板。模板文件位于：

```
mcp-generator/src/main/resources/templates/
```

### 主要模板文件

- **enhanced-mcp-server.ftl**：服务类模板
- **enhanced-api-controller-new.ftl**：API控制器模板
- **enhanced-mcp-server-tool.ftl**：MCP工具类模板
- **enhanced-mcp-server-test.ftl**：单元测试模板
- **unified-service-docs.ftl**：服务文档模板

### 自定义模板

如果需要自定义模板，可以修改这些模板文件，或者创建新的模板文件。

## 最佳实践

### 配置文件组织

- 将配置文件放在项目根目录下的`config`目录中
- 使用服务ID作为配置文件名，如`weather.yml`
- 将共同的配置提取到一个基础配置文件中，如`base.yml`

### 参数定义

- 为每个参数提供详细的描述
- 指定参数是否必填
- 提供参数示例值和默认值
- 使用适当的参数类型

### 输出定义

- 为每个输出字段提供详细的描述
- 使用适当的字段类型
- 包含所有可能的输出字段

### API配置

- 使用RESTful风格的API路径
- 为查询参数提供详细的描述
- 指定适当的API方法

### 生成器配置

- 在开发阶段，设置`overwrite`为`true`
- 始终生成单元测试，设置`generateTest`为`true`
- 使用自动注册，设置`autoRegister`为`true`
- 在开发阶段，使用模拟实现，设置`mockImplementation`为`true`

### 业务配置

- 将敏感信息（如API密钥）放在业务配置中
- 使用环境变量或外部配置文件覆盖敏感信息
- 为每个配置项提供默认值

## 示例

### 天气服务示例

```yaml
server:
  id: weather
  name: 天气服务
  description: 提供全球城市天气查询服务
  version: 1.0.0
  type: API
  usage: 查询指定城市的天气信息

params:
  - name: city
    type: string
    description: 城市名称，如：Beijing, Shanghai, New York等
    required: true
    example: "Beijing"

output:
  fields:
    - name: city
      type: string
      description: 城市名称
    - name: temperature
      type: number
      description: 温度，单位：摄氏度
    - name: humidity
      type: number
      description: 湿度，单位：百分比
    - name: pressure
      type: number
      description: 气压，单位：百帕
    - name: windSpeed
      type: number
      description: 风速，单位：米/秒
    - name: description
      type: string
      description: 天气描述
    - name: timestamp
      type: number
      description: 时间戳

api:
  enabled: true
  path: /api/weather
  method: POST

generator:
  overwrite: true
  generateTest: true
  autoRegister: true
  mockImplementation: true

business:
  enabled: true
  properties:
    api_key: YOUR_API_KEY
    api_url: https://api.example.com/weather
    cache_ttl: 3600
```

### 数据库服务示例

```yaml
server:
  id: mysql
  name: MySQL服务
  description: 提供MySQL数据库操作服务
  version: 1.0.0
  type: DATABASE
  usage: 执行SQL查询和更新操作

params:
  - name: sql
    type: string
    description: SQL语句
    required: true
    example: "SELECT * FROM users WHERE id = ?"
  - name: params
    type: array
    description: SQL参数数组
    required: false
    example: [1]
  - name: limit
    type: number
    description: 结果限制数量
    required: false
    defaultValue: 100
    example: 10

output:
  fields:
    - name: results
      type: array
      description: 查询结果数组
    - name: affectedRows
      type: number
      description: 受影响的行数
    - name: lastInsertId
      type: number
      description: 最后插入的ID
    - name: executionTime
      type: number
      description: 执行时间，单位：毫秒

api:
  enabled: true
  path: /api/mysql
  method: POST

generator:
  overwrite: true
  generateTest: true
  autoRegister: true
  mockImplementation: true

business:
  enabled: true
  properties:
    jdbc_url: ********************************
    username: root
    password: password
    max_pool_size: 10
    connection_timeout: 30000
```

### 缓存服务示例

```yaml
server:
  id: redis
  name: Redis服务
  description: 提供Redis缓存操作服务
  version: 1.0.0
  type: CACHE
  usage: 执行Redis键值对操作

params:
  - name: operation
    type: string
    description: 操作类型，如：get, set, del, exists等
    required: true
    example: "get"
  - name: key
    type: string
    description: 键名
    required: true
    example: "user:1"
  - name: value
    type: string
    description: 值（仅用于set操作）
    required: false
    example: "{\"id\":1,\"name\":\"John\"}"
  - name: ttl
    type: number
    description: 过期时间，单位：秒（仅用于set操作）
    required: false
    defaultValue: -1
    example: 3600

output:
  fields:
    - name: result
      type: any
      description: 操作结果
    - name: success
      type: boolean
      description: 操作是否成功
    - name: message
      type: string
      description: 操作消息
    - name: executionTime
      type: number
      description: 执行时间，单位：毫秒

api:
  enabled: true
  path: /api/redis
  method: POST

generator:
  overwrite: true
  generateTest: true
  autoRegister: true
  mockImplementation: true

business:
  enabled: true
  properties:
    redis_host: localhost
    redis_port: 6379
    redis_password: password
    redis_database: 0
    connection_timeout: 2000
    max_total: 8
```

## 常见问题

### Q: 如何处理复杂的参数类型？

A: 对于复杂的参数类型，如对象或数组，可以使用JSON字符串表示，例如：

```yaml
params:
  - name: user
    type: object
    description: 用户对象
    required: true
    example: "{\"id\":1,\"name\":\"John\",\"age\":30}"
```

### Q: 如何生成多个服务？

A: 可以创建多个配置文件，然后分别运行代码生成器，例如：

```bash
mvn spring-boot:run -Dspring-boot.run.arguments="generate-enhanced -c config/weather.yml"
mvn spring-boot:run -Dspring-boot.run.arguments="generate-enhanced -c config/mysql.yml"
mvn spring-boot:run -Dspring-boot.run.arguments="generate-enhanced -c config/redis.yml"
```

### Q: 如何处理服务之间的依赖关系？

A: 可以在服务类中注入其他服务，例如：

```java
@Autowired
private WeatherMcpServer weatherMcpServer;

@Override
protected Object doExecute(Map<String, Object> params) {
    // 调用天气服务
    Map<String, Object> weatherParams = new HashMap<>();
    weatherParams.put("city", params.get("city"));
    Object weatherResult = weatherMcpServer.execute(weatherParams);

    // 处理结果
    // ...

    return result;
}
```

### Q: 如何自定义生成的代码？

A: 可以修改模板文件，或者在生成代码后手动修改。如果需要经常自定义，建议修改模板文件。

### Q: 如何处理敏感信息？

A: 将敏感信息（如API密钥、数据库密码等）放在业务配置文件中，然后在生产环境中使用环境变量或外部配置文件覆盖这些值。

## 参考资源

- [FreeMarker文档](https://freemarker.apache.org/docs/index.html)
- [YAML语法](https://yaml.org/spec/1.2/spec.html)
- [Spring Boot配置](https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-external-config)
- [MCP平台开发指南](development-guide.md)
