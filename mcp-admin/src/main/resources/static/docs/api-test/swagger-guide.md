# Swagger API测试指南

## 概述

MCP平台集成了Swagger/OpenAPI 3.0，提供了强大的API文档和测试功能。通过Swagger UI，您可以浏览所有可用的API，查看详细的API说明，并直接在浏览器中测试API。

本文档将介绍如何使用Swagger UI测试MCP平台的REST API接口。

## 访问Swagger UI

MCP平台的Swagger UI可以通过以下URL访问：

```
http://localhost:8081/swagger-ui.html
```

## Swagger UI界面说明

Swagger UI界面主要包含以下部分：

1. **顶部导航栏**：显示API的基本信息，包括标题、版本等
2. **服务分组**：按照服务类型分组显示API
3. **API列表**：显示每个服务的API列表
4. **API详情**：显示选中API的详细信息，包括参数、响应等
5. **测试面板**：用于测试API，可以输入参数并执行请求

## 浏览API文档

### 服务分组

Swagger UI将API按照服务类型分组显示，每个服务都有一个独立的分组，例如：

- **天气服务 API**：天气查询服务的API
- **MySQL服务 API**：MySQL数据库操作服务的API
- **Redis服务 API**：Redis缓存操作服务的API

点击服务分组可以展开或折叠该服务的API列表。

### API列表

每个服务分组下都有一个API列表，显示该服务提供的所有API，包括：

- **API路径**：API的URL路径
- **API方法**：GET、POST等HTTP方法
- **API摘要**：API的简短描述

点击API列表中的项目可以展开API详情。

### API详情

API详情包含以下信息：

- **API说明**：API的详细描述
- **参数说明**：API的参数列表，包括参数名称、类型、是否必填、描述等
- **请求示例**：API的请求示例，包括请求体、查询参数等
- **响应说明**：API的响应说明，包括响应码、响应体等
- **响应示例**：API的响应示例

## 测试API

### 准备测试

1. 在API列表中找到要测试的API
2. 点击API项目，展开API详情
3. 查看API的参数说明，了解需要提供哪些参数

### 执行测试

1. 在API详情中，找到"Try it out"按钮，点击它
2. 在参数输入框中填写参数值
   - 对于查询参数，直接在参数输入框中填写
   - 对于请求体，在JSON编辑器中填写
3. 点击"Execute"按钮，执行请求
4. 查看响应结果，包括：
   - 响应码
   - 响应头
   - 响应体

### 测试示例

#### 测试天气服务

1. 在API列表中找到"天气服务 API"分组
2. 点击"GET /api/weather"，展开API详情
3. 点击"Try it out"按钮
4. 在"city"参数输入框中填写"Beijing"
5. 点击"Execute"按钮
6. 查看响应结果

响应示例：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "city": "Beijing",
    "temperature": 25.5,
    "humidity": 60,
    "pressure": 1013,
    "windSpeed": 3.5,
    "description": "晴天",
    "timestamp": 1619712345678
  }
}
```

#### 测试MySQL服务

1. 在API列表中找到"MySQL服务 API"分组
2. 点击"POST /api/mysql"，展开API详情
3. 点击"Try it out"按钮
4. 在请求体编辑器中填写：
   ```json
   {
     "sql": "SELECT * FROM users WHERE id = ?",
     "params": [1],
     "limit": 10
   }
   ```
5. 点击"Execute"按钮
6. 查看响应结果

响应示例：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "results": [
      {
        "id": 1,
        "name": "John",
        "age": 30,
        "email": "<EMAIL>"
      }
    ],
    "affectedRows": 1,
    "executionTime": 5
  }
}
```

#### 测试Redis服务

1. 在API列表中找到"Redis服务 API"分组
2. 点击"POST /api/redis"，展开API详情
3. 点击"Try it out"按钮
4. 在请求体编辑器中填写：
   ```json
   {
     "operation": "get",
     "key": "user:1"
   }
   ```
5. 点击"Execute"按钮
6. 查看响应结果

响应示例：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "result": "{\"id\":1,\"name\":\"John\",\"age\":30,\"email\":\"<EMAIL>\"}",
    "success": true,
    "executionTime": 2
  }
}
```

## 高级功能

### 授权

如果API需要授权，可以在Swagger UI中配置授权信息：

1. 点击顶部导航栏中的"Authorize"按钮
2. 在弹出的对话框中填写授权信息，如API密钥、令牌等
3. 点击"Authorize"按钮，完成授权
4. 之后的所有请求都会带上授权信息

### 请求历史

Swagger UI会记录您的请求历史，您可以：

1. 在API详情中，点击"Try it out"按钮
2. 查看参数输入框中的历史值
3. 选择一个历史值，或者输入新的值
4. 点击"Execute"按钮，执行请求

### 导出API文档

您可以导出API文档，以便离线查看或与他人共享：

1. 在Swagger UI界面中，点击顶部导航栏中的"API"链接
2. 在弹出的菜单中，选择导出格式，如JSON、YAML等
3. 下载导出的文档

## 常见问题

### Q: 如何处理复杂的请求体？

A: 对于复杂的请求体，可以使用JSON编辑器，或者先在外部编辑器中编辑好JSON，然后复制粘贴到Swagger UI的请求体编辑器中。

### Q: 如何处理文件上传？

A: 对于文件上传API，Swagger UI提供了文件选择器，可以直接选择本地文件上传。

### Q: 如何处理多个参数？

A: 对于多个参数，可以在参数输入框中分别填写，或者在请求体中使用JSON对象包含多个参数。

### Q: 如何查看请求和响应的详细信息？

A: 执行请求后，Swagger UI会显示请求和响应的详细信息，包括：
- 请求URL
- 请求方法
- 请求头
- 请求体
- 响应码
- 响应头
- 响应体

### Q: 如何处理错误响应？

A: 如果API返回错误响应，Swagger UI会显示错误信息，包括错误码和错误消息。您可以根据错误信息排查问题。

## 最佳实践

### 测试前准备

- 了解API的功能和参数
- 准备测试数据
- 确保系统环境正常

### 测试策略

- 从简单到复杂，先测试基本功能，再测试复杂场景
- 测试正常情况和异常情况
- 测试边界值和特殊值

### 测试记录

- 记录测试结果，包括成功和失败的情况
- 记录发现的问题和解决方法
- 记录测试环境和测试数据

## 参考资源

- [Swagger官方文档](https://swagger.io/docs/)
- [OpenAPI 3.0规范](https://swagger.io/specification/)
- [Swagger UI文档](https://swagger.io/tools/swagger-ui/)
