# MCP SSE测试指南

## 概述

MCP (Modular Computing Platform) 协议是一种基于Server-Sent Events (SSE)的协议，用于与大模型集成。MCP平台提供了MCP SSE测试页面，方便开发者测试MCP协议接口。

本文档将介绍如何使用MCP SSE测试页面测试MCP协议接口。

## 访问MCP SSE测试页面

MCP平台的MCP SSE测试页面可以通过以下URL访问：

```
http://localhost:8081/mcp-sse-test.html
```

## MCP SSE测试页面说明

MCP SSE测试页面主要包含以下部分：

1. **连接设置**：设置SSE连接的URL
2. **连接状态**：显示当前连接状态
3. **工具列表**：显示可用的工具列表
4. **请求编辑器**：编辑请求JSON
5. **发送按钮**：发送请求
6. **响应显示区**：显示响应结果
7. **日志区域**：显示连接和请求的日志

## 测试MCP协议接口

### 建立连接

1. 在连接设置中，输入SSE连接的URL，例如：
   - 统一端点：`http://localhost:8081/mcp/sse`
   - 单独端点：`http://localhost:8081/mcp/{serviceId}/sse`
2. 点击"Connect"按钮，建立SSE连接
3. 观察连接状态，确认连接成功
4. 查看工具列表，了解可用的工具

### 发送请求

1. 在请求编辑器中，编辑请求JSON，例如：
   ```json
   {
     "jsonrpc": "2.0",
     "id": "test-id",
     "method": "weather",
     "params": {
       "city": "Beijing"
     }
   }
   ```
2. 点击"Send"按钮，发送请求
3. 观察响应显示区，查看响应结果
4. 查看日志区域，了解请求和响应的详细信息

### 断开连接

1. 点击"Disconnect"按钮，断开SSE连接
2. 观察连接状态，确认连接已断开

## MCP协议说明

### 协议流程

1. 客户端通过SSE连接建立长连接
2. 服务端发送`tools_available`事件，包含可用工具列表
3. 服务端发送`initialized`事件，表示初始化完成
4. 客户端发送执行请求，包含工具ID和参数
5. 服务端执行工具，返回结果
6. 服务端发送`response`事件，包含执行结果

### 事件类型

- **tools_available**：服务端发送的事件，包含可用工具列表
- **initialized**：服务端发送的事件，表示初始化完成
- **response**：服务端发送的事件，包含执行结果

### 请求格式

请求使用JSON-RPC 2.0格式：

```json
{
  "jsonrpc": "2.0",
  "id": "请求ID",
  "method": "工具ID",
  "params": {
    "参数名1": "参数值1",
    "参数名2": "参数值2"
  }
}
```

### 响应格式

响应使用SSE格式，每个事件包含事件类型和数据：

```
event: 事件类型
data: 事件数据（JSON格式）

```

例如：

```
event: tools_available
data: {"tools":[{"id":"weather","name":"天气服务","description":"提供全球城市天气查询服务","parameters":{"type":"object","properties":{"city":{"type":"string","description":"城市名称"}},"required":["city"]}}]}

```

```
event: response
data: {"jsonrpc":"2.0","id":"test-id","result":{"city":"Beijing","temperature":25.5,"humidity":60,"pressure":1013,"windSpeed":3.5,"description":"晴天","timestamp":1619712345678}}

```

## 测试示例

### 测试天气服务

1. 连接URL：`http://localhost:8081/mcp/weather/sse`
2. 请求JSON：
   ```json
   {
     "jsonrpc": "2.0",
     "id": "weather-test",
     "method": "weather",
     "params": {
       "city": "Beijing"
     }
   }
   ```
3. 响应示例：
   ```
   event: tools_available
   data: {"tools":[{"id":"weather","name":"天气服务","description":"提供全球城市天气查询服务","parameters":{"type":"object","properties":{"city":{"type":"string","description":"城市名称"}},"required":["city"]}}]}

   event: initialized
   data: {}

   event: response
   data: {"jsonrpc":"2.0","id":"weather-test","result":{"city":"Beijing","temperature":25.5,"humidity":60,"pressure":1013,"windSpeed":3.5,"description":"晴天","timestamp":1619712345678}}
   ```

### 测试MySQL服务

1. 连接URL：`http://localhost:8081/mcp/mysql/sse`
2. 请求JSON：
   ```json
   {
     "jsonrpc": "2.0",
     "id": "mysql-test",
     "method": "mysql",
     "params": {
       "sql": "SELECT * FROM users WHERE id = ?",
       "params": [1],
       "limit": 10
     }
   }
   ```
3. 响应示例：
   ```
   event: tools_available
   data: {"tools":[{"id":"mysql","name":"MySQL服务","description":"提供MySQL数据库操作服务","parameters":{"type":"object","properties":{"sql":{"type":"string","description":"SQL语句"},"params":{"type":"array","description":"SQL参数数组"},"limit":{"type":"number","description":"结果限制数量"}},"required":["sql"]}}]}

   event: initialized
   data: {}

   event: response
   data: {"jsonrpc":"2.0","id":"mysql-test","result":{"results":[{"id":1,"name":"John","age":30,"email":"<EMAIL>"}],"affectedRows":1,"executionTime":5}}
   ```

### 测试Redis服务

1. 连接URL：`http://localhost:8081/mcp/redis/sse`
2. 请求JSON：
   ```json
   {
     "jsonrpc": "2.0",
     "id": "redis-test",
     "method": "redis",
     "params": {
       "operation": "get",
       "key": "user:1"
     }
   }
   ```
3. 响应示例：
   ```
   event: tools_available
   data: {"tools":[{"id":"redis","name":"Redis服务","description":"提供Redis缓存操作服务","parameters":{"type":"object","properties":{"operation":{"type":"string","description":"操作类型，如：get, set, del, exists等"},"key":{"type":"string","description":"键名"},"value":{"type":"string","description":"值（仅用于set操作）"},"ttl":{"type":"number","description":"过期时间，单位：秒（仅用于set操作）"}},"required":["operation","key"]}}]}

   event: initialized
   data: {}

   event: response
   data: {"jsonrpc":"2.0","id":"redis-test","result":{"result":"{\"id\":1,\"name\":\"John\",\"age\":30,\"email\":\"<EMAIL>\"}","success":true,"executionTime":2}}
   ```

## 高级功能

### 测试多个工具

1. 连接统一端点：`http://localhost:8081/mcp/sse`
2. 查看工具列表，了解所有可用的工具
3. 发送请求，指定不同的工具ID

### 测试错误处理

1. 发送无效的请求，例如：
   - 缺少必填参数
   - 参数类型错误
   - 工具ID不存在
2. 观察错误响应，了解错误处理机制

### 测试并发请求

1. 在多个浏览器标签页中打开MCP SSE测试页面
2. 分别建立连接
3. 同时发送请求
4. 观察响应，了解并发处理机制

## 与Cline集成

### Cline配置

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL，例如：
   - 统一端点：`http://localhost:8081/mcp/sse`
   - 单独端点：`http://localhost:8081/mcp/{serviceId}/sse`
5. 点击"Save"按钮

### Cline使用

1. 在Cline中创建新的聊天
2. 输入需要使用MCP服务的请求，例如：
   - 天气服务：`查询北京的天气`
   - MySQL服务：`查询所有用户数据`
   - Redis服务：`获取缓存中的用户信息`
3. Cline会自动调用MCP服务，并返回结果

## 常见问题

### Q: 连接失败怎么办？

A: 检查以下几点：
- 确保MCP平台已启动
- 确保URL正确
- 检查网络连接
- 查看浏览器控制台是否有错误信息

### Q: 收不到工具列表怎么办？

A: 检查以下几点：
- 确保连接成功
- 确保服务端正确发送了`tools_available`事件
- 查看浏览器控制台是否有错误信息

### Q: 请求没有响应怎么办？

A: 检查以下几点：
- 确保请求格式正确
- 确保工具ID存在
- 确保参数正确
- 查看服务端日志是否有错误信息

### Q: 如何调试MCP协议？

A: 可以使用以下方法调试MCP协议：
- 使用浏览器开发者工具查看网络请求和响应
- 在服务端添加日志语句，记录请求和响应
- 使用MCP SSE测试页面的日志区域查看详细信息

## 最佳实践

### 测试前准备

- 了解MCP协议的基本原理
- 了解要测试的工具的功能和参数
- 准备测试数据
- 确保系统环境正常

### 测试策略

- 从简单到复杂，先测试基本功能，再测试复杂场景
- 测试正常情况和异常情况
- 测试边界值和特殊值
- 测试并发和性能

### 测试记录

- 记录测试结果，包括成功和失败的情况
- 记录发现的问题和解决方法
- 记录测试环境和测试数据

## 参考资源

- [Server-Sent Events (SSE) 规范](https://html.spec.whatwg.org/multipage/server-sent-events.html)
- [JSON-RPC 2.0 规范](https://www.jsonrpc.org/specification)
- [Solon AI MCP 文档](https://github.com/noear/solon-ai)
- [Cline 文档](https://cline.ai/docs)
