# MCP平台系统概览

## 什么是MCP平台

MCP (Modular Computing Platform) 是一个基于Java Spring Boot和Spring AI的模块化计算平台，提供了一套灵活的服务注册、管理和执行框架。该平台支持多种服务类型，包括天气查询、数据库操作、Redis操作等，并提供了完整的API文档和示例。

## 系统架构

MCP平台采用模块化设计，主要包含以下模块：

```
mcp-platform/
├── mcp-core/           # 核心接口和抽象类
├── mcp-common/         # 通用工具类和常量
├── mcp-server/         # Web服务器，提供HTTP API
├── mcp-admin/          # 管理后台
└── mcp-generator/      # 代码生成器
```

### 模块说明

#### mcp-core

核心模块，定义了MCP平台的基础接口和抽象类，包括：

- `McpServer`：服务接口，定义了服务的基本操作
- `AbstractMcpServer`：服务抽象类，实现了服务接口的通用方法
- `McpServerRegistry`：服务注册表，负责服务的注册和获取
- `McpServerManager`：服务管理器接口，负责服务的管理
- `McpServerStatus`：服务状态枚举

#### mcp-common

通用模块，提供了工具类、常量和通用配置，包括：

- 日期工具类
- 字符串工具类
- JSON工具类
- 加密工具类
- 常量定义

#### mcp-server

Web服务器模块，提供了HTTP API接口，包括：

- RESTful API
- MCP协议接口
- 服务执行接口
- 服务管理接口

#### mcp-admin

管理后台模块，提供了Web界面管理MCP服务，包括：

- 服务列表
- 服务详情
- 服务启动/停止
- 服务执行
- 服务监控
- 文档中心

#### mcp-generator

代码生成器模块，用于生成服务代码模板，包括：

- 服务类生成
- 控制器生成
- 配置生成
- 文档生成

## 系统流程

### 服务注册流程

1. 服务实现类继承`AbstractMcpServer`，实现服务逻辑
2. 服务类使用`@Component`注解，由Spring容器管理
3. 服务启动时，通过`McpServerRegistry`注册到系统中
4. 服务注册后，可以通过API接口访问和执行

### 服务执行流程

1. 客户端通过API接口发送请求
2. 控制器接收请求，解析参数
3. 控制器通过`McpServerRegistry`获取对应的服务
4. 控制器调用服务的`execute`方法，执行服务逻辑
5. 服务执行完成后，返回结果
6. 控制器将结果封装为响应，返回给客户端

### MCP协议流程

1. Cline通过ListTools接口获取可用的工具列表
2. Cline根据用户输入，选择合适的工具
3. Cline通过ExecuteTool接口执行工具，传递参数
4. 服务执行工具逻辑，返回结果
5. Cline将结果展示给用户

## 技术栈

- **后端**：Java 17, Spring Boot 3.2
- **前端**：Thymeleaf, Bootstrap 5, jQuery
- **数据库**：MySQL, Redis
- **构建工具**：Maven
- **文档工具**：Markdown, Swagger/OpenAPI
- **AI集成**：Spring AI, OpenAI API

## 扩展机制

MCP平台提供了灵活的扩展机制，可以方便地添加新的服务：

1. 创建新的服务类，继承`AbstractMcpServer`
2. 实现`execute`方法，处理服务逻辑
3. 使用`@Component`注解，由Spring容器管理
4. 服务会自动注册到系统中，无需额外配置

## 部署架构

MCP平台支持多种部署方式：

### 单机部署

适用于开发和测试环境，所有模块部署在同一台服务器上。

### 分布式部署

适用于生产环境，各模块可以部署在不同的服务器上：

- mcp-server：部署在多台服务器上，通过负载均衡分发请求
- mcp-admin：部署在管理服务器上，提供管理界面
- 数据库：部署在专用的数据库服务器上

### 容器化部署

使用Docker和Kubernetes进行容器化部署：

- 每个模块打包为独立的Docker镜像
- 使用Kubernetes进行编排和管理
- 支持自动扩缩容和故障恢复
