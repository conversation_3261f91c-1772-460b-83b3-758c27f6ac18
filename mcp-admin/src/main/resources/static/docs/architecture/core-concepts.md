# MCP平台核心概念

## 1. 什么是MCP

MCP (Modular Computing Platform) 是一个基于Java Spring Boot和Spring AI的模块化计算平台，提供了一套灵活的服务注册、管理和执行框架。该平台支持多种服务类型，包括天气查询、数据库操作、Redis操作等，并提供了完整的API文档和示例。

MCP平台的核心理念是将各种计算能力模块化，通过统一的接口进行管理和调用，使得开发者可以方便地扩展和使用各种服务。

## 2. MCP协议

MCP协议是一种用于模型与工具交互的协议，允许大型语言模型（如Cline中使用的模型）调用外部工具执行特定任务。MCP协议定义了两个核心接口：

- **ListTools接口**：获取可用的工具列表
- **ExecuteTool接口**：执行特定工具

MCP协议的设计目标是提供一种标准化的方式，使得大型语言模型可以方便地调用外部工具，扩展其能力范围。

## 3. McpServer

McpServer是MCP平台的核心概念，代表一个可以被注册和调用的服务。每个McpServer都有以下特性：

- **唯一标识符**：每个服务都有一个唯一的ID，用于标识和调用
- **描述信息**：包括名称、描述、版本等元数据
- **参数定义**：定义了服务接受的参数类型和格式
- **结果定义**：定义了服务返回的结果类型和格式
- **执行逻辑**：实现了服务的具体功能

McpServer通过实现`AbstractMcpServer`抽象类来创建，该类提供了服务注册、参数验证、结果格式化等通用功能。

## 4. McpServerRegistry

McpServerRegistry是MCP平台的服务注册表，负责管理所有注册的McpServer。它提供了以下功能：

- **服务注册**：将McpServer注册到系统中
- **服务查询**：根据ID查询特定的McpServer
- **服务列表**：获取所有注册的McpServer列表
- **服务状态管理**：管理服务的启动、停止等状态

McpServerRegistry是一个单例对象，在系统启动时自动创建，并在整个系统生命周期中存在。

## 5. McpServerManager

McpServerManager是MCP平台的服务管理器，负责服务的生命周期管理。它提供了以下功能：

- **服务启动**：启动特定的McpServer
- **服务停止**：停止特定的MccServer
- **服务重启**：重启特定的McpServer
- **服务状态查询**：查询特定McpServer的状态

McpServerManager与McpServerRegistry配合使用，共同管理系统中的所有服务。

## 6. McpServerStatus

McpServerStatus是一个枚举类型，表示McpServer的状态。它包括以下几种状态：

- **STOPPED**：服务已停止
- **STARTING**：服务正在启动
- **RUNNING**：服务正在运行
- **STOPPING**：服务正在停止
- **ERROR**：服务出错

服务状态的变化由McpServerManager管理，并通过事件机制通知系统中的其他组件。

## 7. McpToolsController

McpToolsController是MCP平台的控制器，负责处理MCP协议的请求。它提供了以下功能：

- **ListTools接口**：获取可用的工具列表
- **ExecuteTool接口**：执行特定工具

McpToolsController是MCP协议的实现，它将协议请求转发给对应的McpServer处理，并将结果返回给客户端。

## 8. 模块化设计

MCP平台采用模块化设计，主要包含以下模块：

- **mcp-core**：核心接口和抽象类
- **mcp-common**：通用工具类和常量
- **mcp-server**：Web服务器，提供HTTP API
- **mcp-admin**：管理后台
- **mcp-generator**：代码生成器

每个模块都有明确的职责和边界，通过接口进行交互，实现了高内聚、低耦合的设计目标。

## 9. 服务注册机制

MCP平台采用自动注册机制，通过Spring的组件扫描功能，自动发现和注册实现了`AbstractMcpServer`的服务类。服务注册的流程如下：

1. 服务实现类继承`AbstractMcpServer`，实现服务逻辑
2. 服务类使用`@Component`注解，由Spring容器管理
3. 服务启动时，通过`McpServerRegistry`注册到系统中
4. 服务注册后，可以通过API接口访问和执行

这种自动注册机制简化了服务的开发和部署，开发者只需要关注服务的业务逻辑，而不需要关心注册的细节。

## 10. 服务执行流程

MCP平台的服务执行流程如下：

1. 客户端通过API接口发送请求
2. 控制器接收请求，解析参数
3. 控制器通过`McpServerRegistry`获取对应的服务
4. 控制器调用服务的`execute`方法，执行服务逻辑
5. 服务执行完成后，返回结果
6. 控制器将结果封装为响应，返回给客户端

这种执行流程保证了服务的隔离性和可靠性，每个服务都在独立的上下文中执行，不会相互干扰。
