# MCP平台模块结构

## 1. 概述

MCP平台采用模块化设计，将系统分解为多个独立的模块，每个模块负责特定的功能，共同构成一个完整的计算平台。本文档将详细介绍MCP平台的模块结构，包括各模块的功能、职责和相互关系。

## 2. 模块概览

MCP平台主要包含以下几个核心模块：

- **mcp-core**：核心接口和抽象类
- **mcp-server**：服务实现和库功能
- **mcp-admin**：管理后台和API接口
- **mcp-generator**：代码生成器

这些模块之间的关系如下图所示：

```
+------------------+     +------------------+
|                  |     |                  |
|    mcp-admin     |     |    mcp-server    |
|    (Web UI)      |<--->|    (Services)    |
|                  |     |                  |
+------------------+     +------------------+
         ^                        ^
         |                        |
         v                        v
+------------------+     +------------------+
|                  |     |                  |
|    mcp-core      |     |  mcp-generator   |
|  (Interfaces)    |     |  (Code Gen)      |
|                  |     |                  |
+------------------+     +------------------+
```

## 3. mcp-core模块

### 3.1 模块概述

mcp-core模块是MCP平台的核心模块，提供了平台的基础接口和抽象类，是整个平台的基础。该模块定义了服务的基本结构和行为，以及服务管理器的接口和实现。

### 3.2 主要组件

#### 3.2.1 AbstractMcpServer

`AbstractMcpServer`是MCP服务的抽象基类，定义了服务的基本结构和行为。所有的MCP服务都需要继承这个类，并实现其中的抽象方法。

主要方法：
- `initialize()`：初始化服务
- `start()`：启动服务
- `stop()`：停止服务
- `execute(Map<String, Object> params)`：执行服务
- `validateParameters(Map<String, Object> params)`：验证参数
- `executeBusinessLogic(Map<String, Object> params)`：执行业务逻辑
- `getParametersDescription()`：获取参数描述
- `getResultDescription()`：获取结果描述

#### 3.2.2 McpServerManager

`McpServerManager`是服务管理器，负责服务的注册、查找和生命周期管理。它提供了一系列API，允许开发者和系统管理服务。

主要方法：
- `registerServer(McpServer server)`：注册服务
- `unregisterServer(String serverId)`：注销服务
- `getServer(String serverId)`：获取服务
- `getAllServers()`：获取所有服务
- `startServer(String serverId)`：启动服务
- `stopServer(String serverId)`：停止服务
- `getServerStatus(String serverId)`：获取服务状态

#### 3.2.3 McpServerConfig

`McpServerConfig`是服务配置类，负责加载和管理服务配置。它提供了一系列API，允许开发者和系统访问服务配置。

主要方法：
- `loadConfig(String configPath)`：加载配置
- `getServerId()`：获取服务ID
- `getServerName()`：获取服务名称
- `getServerDescription()`：获取服务描述
- `getServerVersion()`：获取服务版本
- `getServerType()`：获取服务类型
- `getParameters()`：获取参数定义
- `getOutput()`：获取输出定义

### 3.3 包结构

mcp-core模块的包结构如下：

```
com.jdl.mcp.core
├── server
│   ├── AbstractMcpServer.java
│   ├── McpServer.java
│   ├── McpServerManager.java
│   ├── McpServerStatus.java
│   └── config
│       ├── McpServerConfig.java
│       ├── ParameterConfig.java
│       └── OutputConfig.java
└── manager
    ├── McpManager.java
    └── impl
        └── DefaultMcpManager.java
```

## 4. mcp-server模块

### 4.1 模块概述

mcp-server模块是MCP平台的服务器模块，提供服务实现和库功能，是平台的核心运行时组件。该模块包含了各种服务的实现类，以及服务配置文件。

### 4.2 主要组件

#### 4.2.1 服务实现类

服务实现类是MCP服务的具体实现，继承自`AbstractMcpServer`类，实现了服务的业务逻辑。

主要服务：
- `MysqlMcpServer`：MySQL数据库服务，提供SQL查询功能
- `RedisMcpServer`：Redis缓存服务，提供缓存操作功能
- `WeatherMcpServer`：天气查询服务，提供天气查询功能

#### 4.2.2 服务配置文件

服务配置文件是JSON格式的文件，定义了服务的参数、输出格式等。配置文件位于`mcp-server/src/main/resources/servers`目录下。

### 4.3 包结构

mcp-server模块的包结构如下：

```
com.jdl.mcp.server
├── servers
│   ├── mysql
│   │   └── MysqlMcpServer.java
│   ├── redis
│   │   └── RedisMcpServer.java
│   └── weather
│       └── WeatherMcpServer.java
├── config
│   └── McpServerConfig.java
└── manager
    └── impl
        └── DefaultMcpServerManager.java
```

## 5. mcp-admin模块

### 5.1 模块概述

mcp-admin模块是MCP平台的管理后台模块，提供Web界面和API接口，是平台的用户交互入口。该模块包含了REST API控制器、MCP协议控制器、管理界面和文档中心等组件。

### 5.2 主要组件

#### 5.2.1 REST API控制器

REST API控制器提供了HTTP API接口，允许外部系统通过HTTP协议调用MCP服务。

主要控制器：
- `MysqlApiController`：MySQL服务的API控制器
- `RedisApiController`：Redis服务的API控制器
- `WeatherApiController`：天气服务的API控制器

#### 5.2.2 MCP协议控制器

MCP协议控制器提供了MCP协议接口，允许Cline等工具通过MCP协议调用MCP服务。

主要控制器：
- `MysqlMcpServerMcpProtocolController`：MySQL服务的MCP协议控制器
- `RedisMcpServerMcpProtocolController`：Redis服务的MCP协议控制器
- `WeatherMcpServerMcpProtocolController`：天气服务的MCP协议控制器

#### 5.2.3 管理界面

管理界面提供了Web界面，允许用户通过浏览器管理和使用MCP服务。

主要页面：
- 首页：系统概览和导航
- 服务列表页：显示所有服务及其状态
- 服务详情页：显示服务详细信息
- 服务执行页：提供参数输入和执行界面
- API文档页：显示API文档

#### 5.2.4 文档中心

文档中心提供了平台文档和API文档，帮助用户了解和使用MCP平台。

主要文档：
- 架构文档：介绍平台架构和设计理念
- 使用指南：介绍平台使用方法
- API文档：介绍API接口
- 服务文档：介绍各服务的功能和使用方法

### 5.3 包结构

mcp-admin模块的包结构如下：

```
com.jdl.mcp.admin
├── controller
│   ├── api
│   │   ├── MysqlApiController.java
│   │   ├── RedisApiController.java
│   │   └── WeatherApiController.java
│   ├── mcp
│   │   ├── MysqlMcpServerMcpProtocolController.java
│   │   ├── RedisMcpServerMcpProtocolController.java
│   │   └── WeatherMcpServerMcpProtocolController.java
│   ├── EnhancedDocsController.java
│   └── HomeController.java
├── service
│   ├── MarkdownService.java
│   └── DocStructureService.java
├── client
│   └── McpServerClient.java
└── launcher
    └── McpServerLauncher.java
```

## 6. mcp-generator模块

### 6.1 模块概述

mcp-generator模块是MCP平台的代码生成器模块，用于生成MCP服务代码，提高开发效率。该模块包含了代码生成服务、模板文件和命令行工具等组件。

### 6.2 主要组件

#### 6.2.1 代码生成服务

代码生成服务负责根据配置文件生成服务代码。它读取配置文件，解析配置信息，然后根据模板生成代码文件。

主要类：
- `CodeGenerator`：代码生成器主类
- `TemplateProcessor`：模板处理器
- `ConfigParser`：配置解析器

#### 6.2.2 模板文件

模板文件是用于生成代码的模板，使用FreeMarker模板引擎。模板文件位于`mcp-generator/src/main/resources/templates`目录下。

主要模板：
- `McpServer.ftl`：服务实现类模板
- `ApiController.ftl`：API控制器模板
- `McpProtocolController.ftl`：MCP协议控制器模板
- `ServerProperties.ftl`：服务配置文件模板

#### 6.2.3 命令行工具

命令行工具提供了命令行接口，允许用户通过命令行生成代码。

主要命令：
- `generate`：生成代码
- `create-template`：创建配置文件模板

### 6.3 包结构

mcp-generator模块的包结构如下：

```
com.jdl.mcp.generator
├── config
│   ├── GeneratorConfig.java
│   └── TemplateConfig.java
├── model
│   ├── ServerModel.java
│   ├── ParameterModel.java
│   └── OutputModel.java
├── service
│   ├── CodeGeneratorService.java
│   ├── TemplateService.java
│   └── ConfigService.java
└── command
    ├── GenerateCommand.java
    └── CreateTemplateCommand.java
```

## 7. 模块依赖关系

MCP平台的模块之间存在以下依赖关系：

- **mcp-core**：不依赖其他模块
- **mcp-server**：依赖mcp-core
- **mcp-admin**：依赖mcp-core和mcp-server
- **mcp-generator**：依赖mcp-core

依赖关系图如下：

```
mcp-core <-- mcp-server <-- mcp-admin
    ^
    |
    +-- mcp-generator
```

## 8. 模块职责划分

MCP平台的模块职责划分如下：

- **mcp-core**：定义接口和抽象类，提供基础功能
- **mcp-server**：实现服务，提供业务功能
- **mcp-admin**：提供用户界面和API接口，处理用户请求
- **mcp-generator**：生成代码，提高开发效率

这种职责划分确保了系统的模块化和可扩展性，每个模块只负责自己的功能，通过接口与其他模块交互。

## 9. 模块扩展点

MCP平台的各模块提供了多个扩展点，允许开发者扩展系统功能：

- **mcp-core**：
  - 新服务类型：通过继承`AbstractMcpServer`类，可以创建新的服务类型
  - 新管理器：通过实现`McpManager`接口，可以创建新的管理器

- **mcp-server**：
  - 新服务实现：通过继承`AbstractMcpServer`类，可以创建新的服务实现
  - 新配置项：通过扩展配置类，可以添加新的配置项

- **mcp-admin**：
  - 新API控制器：通过创建新的控制器类，可以提供新的API接口
  - 新页面：通过创建新的页面模板，可以提供新的用户界面

- **mcp-generator**：
  - 新代码生成模板：通过创建新的模板文件，可以生成新类型的代码
  - 新命令：通过创建新的命令类，可以提供新的命令行功能

## 10. 模块配置

MCP平台的各模块配置如下：

- **mcp-core**：
  - 无需特殊配置

- **mcp-server**：
  - 服务配置文件：`mcp-server/src/main/resources/servers/*.properties`
  - 应用配置文件：`mcp-server/src/main/resources/application.yml`

- **mcp-admin**：
  - 应用配置文件：`mcp-admin/src/main/resources/application.yml`
  - 页面模板：`mcp-admin/src/main/resources/templates/*.html`
  - 静态资源：`mcp-admin/src/main/resources/static/*`

- **mcp-generator**：
  - 应用配置文件：`mcp-generator/src/main/resources/application.yml`
  - 代码模板：`mcp-generator/src/main/resources/templates/*.ftl`
