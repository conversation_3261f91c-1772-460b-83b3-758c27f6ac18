# MCP平台文档中心

欢迎使用MCP (Model Context Protocol) 平台文档中心。MCP平台是一个用于构建和管理服务的框架，支持服务的注册、发现、调用和监控。平台的核心特点是所有服务都同时支持REST API和MCP协议端点，可以通过HTTP请求直接调用，也可以集成到大模型中作为工具使用。

## 1. 平台简介

MCP (Model Context Protocol) 是一个基于Java Spring Boot的服务框架，提供了一套灵活的服务注册、管理和执行框架。该平台支持多种服务类型，包括API服务、数据库服务、缓存服务等，并提供了完整的代码生成器、API文档和示例。

### 主要特点

- **模块化设计**：分为核心、服务器、管理后台和代码生成器等模块
- **双重接口支持**：所有服务同时支持REST API和MCP协议接口，无需单独开发
- **大模型集成**：无缝集成到大模型中，作为工具使用，支持Cline等工具
- **代码生成**：通过代码生成器快速创建新服务，自动生成REST API和MCP协议端点
- **统一管理**：统一管理所有服务，包括注册、发现、调用和监控
- **配置外部化**：将配置项放在配置文件中，支持运行时修改

## 2. 文档导航

### 2.1 架构文档

- [系统架构](/docs/architecture/overview) - MCP平台的整体架构和设计理念
- [核心概念](/docs/architecture/core-concepts) - MCP平台的核心概念和术语解释
- [模块结构](/docs/architecture/modules) - MCP平台的模块结构和职责划分

### 2.2 使用指南

- [快速开始](/docs/guides/getting-started) - 快速上手MCP平台
- [安装部署](/docs/guides/installation) - 安装和部署MCP平台
- [配置管理](/docs/guides/configuration) - 配置MCP平台的各项参数
- [开发指南](/docs/guides/development) - 开发MCP服务和扩展功能
- [代码生成器](/docs/guides/code-generator) - 使用代码生成器快速创建MCP服务

### 2.3 API文档

- [REST API](/docs/api/rest-api) - REST API接口文档，包含请求参数、响应格式和示例
- [MCP协议](/docs/api/mcp-protocol) - MCP协议接口文档，包含协议格式、参数架构和示例

### 2.4 服务文档

- [MySQL服务](/docs/servers/mysql) - MySQL数据库服务，提供SQL查询功能
- [Redis服务](/docs/servers/redis) - Redis缓存服务，提供缓存操作功能
- [天气服务](/docs/servers/weather) - 天气查询服务，提供天气查询功能

## 3. 快速开始

### 3.1 安装要求

- JDK 8
- Maven 3.6+
- MySQL 5.7+（可选，如果使用MySQL服务）
- Redis 5.0+（可选，如果使用Redis服务）

### 3.2 快速安装

```bash
# 克隆仓库
git clone https://github.com/your-org/mcp-platform.git

# 进入项目目录
cd mcp-platform

# 编译和启动项目
./rebuild-start-jdk8.sh
```

### 3.3 使用REST API

```bash
# 调用天气服务
curl -X GET "http://localhost:8081/api/v1/weather?city=Beijing" \
  -H "Content-Type: application/json"
```

### 3.4 使用MCP协议

```bash
# 调用天气服务
curl -X POST "http://localhost:8081/api/v1/mcp/weather/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "city": "Beijing"
  }'
```

### 3.5 创建新服务

```bash
# 创建配置文件模板
java -jar mcp-generator.jar create-template -o my-service-config.yml --enhanced

# 编辑配置文件

# 生成服务代码
java -jar mcp-generator.jar generate-enhanced -c my-service-config.yml
```

## 4. 与大模型集成

MCP平台设计用于与大型语言模型（如GPT、Claude等）集成，使模型能够调用外部工具执行特定任务。所有MCP服务都自动暴露为MCP协议端点，无需单独开发AI类型服务。

### 4.1 在Cline中配置

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL，例如：`http://localhost:8081/api/v1/mcp`
5. 点击"Save"按钮

配置完成后，Cline会自动发现并使用MCP服务作为工具。

### 4.2 使用示例

当用户在Cline中询问天气信息时，Cline会自动调用天气服务的MCP协议接口：

**用户提问**:
```
上海今天的天气怎么样？
```

**Cline处理流程**:
1. 识别用户需要查询上海的天气
2. 调用天气服务的MCP协议接口，参数为 `{"city": "Shanghai"}`
3. 获取天气信息
4. 将天气信息整合到回复中

**回复示例**:
```
上海今天的天气是阴天，温度22.5°C，湿度56%，气压1013百帕，风速5.2米/秒。
```

## 5. 系统要求

- JDK 8
- Maven 3.6+
- MySQL 5.7+（可选，如果使用MySQL服务）
- Redis 5.0+（可选，如果使用Redis服务）

## 6. 版本历史

- **1.0.0**：初始版本，支持基本的服务管理和调用
- **1.1.0**：添加代码生成器
- **1.2.0**：添加MCP协议支持
- **1.3.0**：添加与大模型集成支持
- **1.4.0**：添加更多服务类型支持
- **1.5.0**：优化文档和接口

## 7. 贡献指南

欢迎贡献代码和文档！请遵循以下步骤：

1. Fork代码库
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 8. 联系我们

- **邮箱**：<EMAIL>
- **GitHub**：https://github.com/your-org/mcp-platform
- **官网**：https://example.com/mcp-platform
