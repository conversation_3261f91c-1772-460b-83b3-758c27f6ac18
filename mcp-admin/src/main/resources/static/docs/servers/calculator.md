# 计算器服务

## 1. 服务概述

- **服务ID**: calculator
- **版本**: 1.0.0
- **类型**: API
- **生成时间**: 2025-04-26T10:49:57.279387

提供基本的数学计算功能，支持加减乘除、幂运算、平方根等基本数学运算，可以处理精确到指定小数位的计算结果。

## 2. 功能说明

计算器服务提供以下核心功能：

1. **加法运算** - 计算两个数字的和
2. **减法运算** - 计算两个数字的差
3. **乘法运算** - 计算两个数字的积
4. **除法运算** - 计算两个数字的商（除数不能为0）
5. **幂运算** - 计算一个数的幂（如平方、立方等）
6. **平方根运算** - 计算一个数的平方根

所有计算结果都会根据配置的精度进行四舍五入处理，并且对输入数字有最大值限制，以确保计算的准确性和性能。

## 3. 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| operation | string | 是 | - | 操作类型，可选值：add（加法）, subtract（减法）, multiply（乘法）, divide（除法）, power（幂运算）, sqrt（平方根） |
| a | double | 是 | - | 第一个数字 |
| b | double | 否 | - | 第二个数字，对于sqrt操作可选 |

## 4. 输出说明

| 字段名 | 类型 | 说明 |
|-------|------|------|
| result | Double | 计算结果 |
| operation | String | 执行的操作类型 |
| a | Double | 第一个数字 |
| b | Double | 第二个数字（如果适用） |
| success | Boolean | 操作是否成功 |
| message | String | 结果描述或错误信息 |

## 5. 配置说明

| 配置项 | 默认值 | 说明 |
|-------|--------|------|
| calculator.precision | ${CALCULATOR_PRECISION:2} | 计算结果的精度（小数位数），决定结果保留的小数位数 |
| calculator.max_value | ${CALCULATOR_MAX_VALUE:1000000} | 允许的最大计算值，超过此值将返回错误 |

**注意**：
- 计算结果会根据 calculator.precision 配置的精度进行四舍五入处理
- 输入数字的绝对值不能超过 calculator.max_value，否则会返回错误
- 这些配置可以通过环境变量 CALCULATOR_PRECISION 和 CALCULATOR_MAX_VALUE 进行覆盖

## 6. REST API接口

### 6.1 基本信息

- **路径**: /api/v1/calculator
- **方法**: GET
- **说明**: 提供基本的数学计算功能

### 6.2 API参数说明


**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| operation | String | 是 | 操作类型 |
| a | String | 是 | 第一个数字 |
| b | String | 否 | 第二个数字 |


### 6.3 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "result": "示例值",
    "operation": "示例值",
    "a": "示例值",
    "b": "示例值",
    "success": "示例值",
    "message": "示例值"
  }
}
```

### 6.4 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 6.5 REST API调用示例

#### 6.5.1 加法运算示例

```bash
# 使用curl调用加法运算
curl -X GET "http://localhost:8080/api/v1/calculator?operation=add&a=10.5&b=5.2" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "operation": "add",
    "a": 10.5,
    "b": 5.2,
    "result": 15.7
  }
}
```

#### 6.5.2 除法运算示例

```bash
# 使用curl调用除法运算
curl -X GET "http://localhost:8080/api/v1/calculator?operation=divide&a=10&b=3" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "operation": "divide",
    "a": 10.0,
    "b": 3.0,
    "result": 3.33
  }
}
```

#### 6.5.3 平方根运算示例

```bash
# 使用curl调用平方根运算
curl -X GET "http://localhost:8080/api/v1/calculator?operation=sqrt&a=16" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "operation": "sqrt",
    "a": 16.0,
    "result": 4.0
  }
}
```

#### 6.5.4 错误示例（除数为0）

```bash
# 使用curl调用除法运算，除数为0
curl -X GET "http://localhost:8080/api/v1/calculator?operation=divide&a=10&b=0" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 1000,
  "message": "参数错误",
  "data": {
    "success": false,
    "message": "除数不能为0"
  }
}
```


## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/calculator/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `calculator`
- **描述**: `提供基本的数学计算功能`
- **方法**:
  - **add_numbers**: 执行加法运算
    - **first_number**: 第一个数字 (double, 必填)
    - **second_number**: 第二个数字 (double, 必填)
  - **subtract_numbers**: 执行减法运算
    - **first_number**: 第一个数字 (double, 必填)
    - **second_number**: 第二个数字 (double, 必填)
  - **multiply_numbers**: 执行乘法运算
    - **first_number**: 第一个数字 (double, 必填)
    - **second_number**: 第二个数字 (double, 必填)
  - **divide_numbers**: 执行除法运算
    - **first_number**: 第一个数字 (double, 必填)
    - **second_number**: 第二个数字 (double, 必填)
  - **power**: 执行幂运算
    - **base**: 底数 (double, 必填)
    - **exponent**: 指数 (double, 必填)
  - **sqrt**: 执行平方根运算
    - **number**: 要计算平方根的数字 (double, 必填)

### 7.4 MCP协议调用示例

#### 7.4.1 加法运算示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/calculator/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行加法运算
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "add_numbers",
        params: {
            "first_number": 10.5,
            "second_number": 5.2
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "success": true,
    //   "operation": "add",
    //   "a": 10.5,
    //   "b": 5.2,
    //   "result": 15.7
    // }
});
```

#### 7.4.2 平方根运算示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/calculator/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行平方根运算
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "sqrt",
        params: {
            "number": 16
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "success": true,
    //   "operation": "sqrt",
    //   "a": 16.0,
    //   "result": 4.0
    // }
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/calculator/sse`（仅访问计算器服务服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用计算器服务服务的请求，例如：`使用计算器服务服务`
3. Cline会自动调用MCP服务，并返回结果


## 8. 常见问题

1. **问题**: 如何配置计算器服务的精度？
   **回答**: 可以通过修改`application.properties`或`application.yml`文件中的`calculator.precision`配置项来设置计算结果的精度（小数位数）。也可以通过环境变量`CALCULATOR_PRECISION`来覆盖此配置。

2. **问题**: 计算器服务支持哪些数据格式？
   **回答**: 服务支持JSON格式的数据交换，所有数值参数和结果都使用双精度浮点数（Double）类型。

3. **问题**: 计算器服务有哪些限制？
   **回答**: 输入数字的绝对值不能超过配置的最大值（默认为1,000,000），除法运算中除数不能为0，平方根运算的输入不能为负数。

4. **问题**: 如何处理计算错误？
   **回答**: 当发生计算错误时（如除以零、参数超出范围等），服务会返回一个包含错误信息的响应，其中`success`字段为`false`，`message`字段包含具体的错误描述。

5. **问题**: 计算器服务支持哪些运算？
   **回答**: 当前版本支持加法（add_numbers）、减法（subtract_numbers）、乘法（multiply_numbers）、除法（divide_numbers）、幂运算（power）和平方根（sqrt）六种基本运算。

6. **问题**: 如何在MCP协议中调用不同的计算方法？
   **回答**: 在MCP协议中，每种运算都有对应的方法名，如`add_numbers`、`subtract_numbers`等，调用时需要指定正确的方法名和对应的参数。

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [开发指南](/docs/guides/development)
- [代码生成器指南](/docs/guides/code-generator)
