# 机器人发消息服务

通过机器人发消息服务，当前默认使用物流交易小蜜机器人

## 基本信息

- 服务ID：timline
- 版本：1.0.0
- 类型：API
- 使用说明：通过机器人发消息服务，当前默认使用物流交易小蜜机器人

## 1. 服务概述

机器人发消息服务（timline）提供了通过机器人向用户或群组发送消息的能力。服务支持发送普通文本消息和交互式卡片消息，可用于系统通知、业务提醒、信息推送等场景。

## 2. 功能说明

- **发送普通文本消息**：支持向个人或群组发送纯文本消息
- **发送@消息**：支持在群组中@特定用户或@全体成员
- **发送卡片消息**：支持发送包含图片、按钮等富媒体元素的交互式卡片消息

## 3. 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| receiver | String | 是 | - | 接收人，可以是用户erp，也可以是群组id |
| receiverType | int | 否 | 1 | 接收人类型，1-用户 2-群组 |
| messageType | int | 否 | 1 | 消息类型，普通消息类型为1，卡片消息类型为2 |
| messageContent | String | 是 | - | 消息内容，JSON格式 |
| robotId | String | 否 | 00_55e779bf635b4f14 | 机器人id，默认为物流交易小蜜 |

### 3.1 消息内容格式说明

#### 3.1.1 普通文本消息格式 (messageType=1)

```json
{
  "body": {
    "type": "text",
    "content": "这是一条测试消息"
  }
}
```

#### 3.1.2 群组@全体成员消息格式

```json
{
  "body": {
    "type": "text",
    "content": "@全体成员 这是一条全体成员通知",
    "atUsers": [{
      "app": "ee",
      "pin": "all",
      "nickname": "全体成员"
    }]
  }
}
```

#### 3.1.3 群组@特定用户消息格式

```json
{
  "body": {
    "type": "text",
    "content": "@用户名 这是一条@消息测试",
    "atUsers": [{
      "app": "ee",
      "pin": "用户pin",
      "nickname": "用户名"
    }]
  }
}
```

#### 3.1.4 卡片消息格式 (messageType=2)

```json
{
  "data": {
    "templateId": "templateMsgCard",
    "sessionType": 1,
    "templateType": 1,
    "width_mode": "wide",
    "reload": false,
    "summary": "卡片消息摘要",
    "cardData": {
      "elements": [
        {
          "preview": true,
          "img_url": "https://apijoyspace.jd.com/v1/files/IrFPuO2K5qBbUjGA0LiP/link",
          "scale": 2.3,
          "tag": "img"
        },
        {
          "tag": "hr"
        },
        {
          "tag": "footer",
          "content": "来自MCP 平台"
        }
      ],
      "header": {
        "theme": "red",
        "title": {
          "tag": "plain_text",
          "content": "京东价值观"
        }
      }
    }
  }
}


## 配置项

| 配置项 | 默认值 | 描述 |
|--------|--------|------|
| timline_default_message_robot | 00_55e779bf635b4f14 | 默认机器人id，物流交易小蜜机器人id |
| timline_default_message_type | 1 | 默认消息类型，1-普通消息 2-卡片消息 |
| timline_default_receiver_type | 1 | 默认接收人类型，1-用户 2-群组 |

## 使用示例

### sendMessage

```bash
curl -X POST "http://localhost:8080/api/v1/timline" \
  -H "Content-Type: application/json" \
  -d '{
    "receiver": "erp或群组ID",
    "receiverType": 1,
    "messageType": 1,
    "messageContent": "{\"body\":{\"type\":\"text\",\"content\":\"这是一条测试消息\"}}",
    "robotId": "00_55e779bf635b4f14"
  }'
```

响应：

```json
{
  "success": true,
  "result": "",
  "jsonData": "{\"code\":0,\"message\":\"success\",\"data\":{...}}"
}
```


## API接口

该服务已启用REST API接口，详见第6节。

## 1. 服务概述

机器人发消息服务（timline）提供了通过机器人向用户或群组发送消息的能力。服务支持发送普通文本消息和交互式卡片消息，可用于系统通知、业务提醒、信息推送等场景。

## 2. 功能说明

- **发送普通文本消息**：支持向个人或群组发送纯文本消息
- **发送@消息**：支持在群组中@特定用户或@全体成员
- **发送卡片消息**：支持发送包含图片、按钮等富媒体元素的交互式卡片消息

## 3. 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| receiver | String | 是 | - | 接收人，可以是用户erp，也可以是群组id |
| receiverType | int | 否 | 1 | 接收人类型，1-用户 2-群组 |
| messageType | int | 否 | 1 | 消息类型，普通消息类型为1，卡片消息类型为2 |
| messageContent | String | 是 | - | 消息内容，JSON格式 |
| robotId | String | 否 | 00_55e779bf635b4f14 | 机器人id，默认为物流交易小蜜 |

## 4. 输出说明

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 是否成功 true/false |
| result | String | 结果描述，成功时为空，失败时有具体错误信息 |
| jsonData | String | 完整的接口返回原始json数据 |

## 5. 配置说明

| 配置项 | 默认值 | 说明 |
|-------|--------|------|
| timline_default_message_robot | 00_55e779bf635b4f14 | 默认机器人id，物流交易小蜜机器人id |
| timline_default_message_type | 1 | 默认消息类型，1-普通消息 2-卡片消息 |
| timline_default_receiver_type | 1 | 默认接收人类型，1-用户 2-群组 |

## 6. REST API接口

### 6.1 基本信息

- **路径**: /api/v1/timline
- **方法**: POST
- **说明**: 通过机器人发消息服务，当前默认使用物流交易小蜜机器人

### 6.2 API参数说明

**请求体参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| receiver | String | 是 | 接收人，可以是用户erp，也可以是群组id |
| receiverType | int | 否 | 接收人类型，1-用户 2-群组 |
| messageType | int | 否 | 消息类型，普通消息类型为1，卡片消息类型为2 |
| messageContent | String | 是 | 消息内容，JSON格式 |
| robotId | String | 否 | 机器人id，默认为物流交易小蜜 |

### 6.3 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "result": "",
    "jsonData": "{\"code\":0,\"message\":\"success\",\"data\":{...}}"
  }
}
```

### 6.4 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 6.5 REST API调用示例

#### 6.5.1 请求参数JSON示例

```json
{
  "receiver": "erp或群组ID",
  "receiverType": 1,
  "messageType": 1,
  "messageContent": "{\"body\":{\"type\":\"text\",\"content\":\"这是一条测试消息\"}}",
  "robotId": "00_55e779bf635b4f14"
}
```

#### 6.5.2 curl调用示例

```bash
curl -X POST "/api/v1/timline" \
  -H "Content-Type: application/json" \
  -d '{
    "receiver": "erp或群组ID",
    "receiverType": 1,
    "messageType": 1,
    "messageContent": "{\"body\":{\"type\":\"text\",\"content\":\"这是一条测试消息\"}}",
    "robotId": "00_55e779bf635b4f14"
  }'
```

## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/timline/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `timline`
- **描述**: `通过机器人发消息服务，当前默认使用物流交易小蜜机器人`
- **参数**:
  - **receiver**: 接收人，可以是用户erp，也可以是群组id (String, 必填)
  - **receiverType**: 接收人类型，1-用户 2-群组 (int, 可选)
  - **messageType**: 消息类型，普通消息类型为1，卡片消息类型为2 (int, 可选)
  - **messageContent**: 消息内容，JSON格式 (String, 必填)
  - **robotId**: 机器人id，默认为物流交易小蜜 (String, 可选)

### 7.4 MCP协议调用示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/timline/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行工具
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "timline",
        params: {
            "receiver": "erp或群组ID",
            "receiverType": 1,
            "messageType": 1,
            "messageContent": "{\"body\":{\"type\":\"text\",\"content\":\"这是一条测试消息\"}}",
            "robotId": "00_55e779bf635b4f14"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/timline/sse`（仅访问机器人发消息服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用机器人发消息服务的请求，例如：`使用机器人发送消息`
3. Cline会自动调用MCP服务，并返回结果

## 8. 常见问题

1. **问题**: 如何配置机器人发消息服务？
   **回答**: 可以通过修改`application.properties`或`application.yml`文件中的相关配置项来配置服务。

2. **问题**: 机器人发消息服务支持哪些数据格式？
   **回答**: 服务支持JSON格式的数据交换。

3. **问题**: 如何在群组中@特定用户？
   **回答**: 需要在消息内容中设置atUsers字段，并指定用户的app、pin和nickname信息。

4. **问题**: 卡片消息支持哪些元素？
   **回答**: 卡片消息支持图片、文本、分割线、按钮、页脚等多种元素，可以根据需要组合使用。

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [开发指南](/docs/guides/development)
- [代码生成器指南](/docs/guides/code-generator)