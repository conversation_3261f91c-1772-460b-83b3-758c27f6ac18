# 天气服务

## 1. 服务概述

- **服务ID**: weather
- **版本**: 1.0.0
- **类型**: API
- **生成时间**: 2025-04-24T18:29:19.117652

提供全球城市天气查询服务，支持查询多个城市的实时天气数据，包括温度、湿度、气压、风速等信息。

## 2. 功能说明

查询指定城市的天气信息，参数：city - 城市名称（如：Beijing, Shanghai, New York等）。

服务支持以下城市的精确天气数据：
- Beijing（北京）
- Shanghai（上海）
- Guangzhou（广州）
- Shenzhen（深圳）
- New York（纽约）
- London（伦敦）
- Tokyo（东京）

对于其他城市，服务将返回随机生成的天气数据。

## 3. 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| city | string | 是 | - | 城市名称，如：Beijing, Shanghai, New York等 |

## 4. 输出说明

| 字段名 | 类型 | 说明 |
|-------|------|------|
| city | String | 城市名称 |
| temperature | Double | 温度，单位：摄氏度 |
| humidity | Integer | 湿度，单位：百分比 |
| pressure | Integer | 气压，单位：百帕 |
| windSpeed | Double | 风速，单位：米/秒 |
| description | String | 天气描述 |
| timestamp | Long | 时间戳，单位：毫秒 |

## 5. 配置说明

| 配置项 | 默认值 | 说明 |
|-------|--------|------|
| weather.api.key | ${WEATHER_API_KEY:your-api-key} | API密钥，当前版本使用模拟数据，此配置项暂未使用 |
| weather.api.base_url | ${WEATHER_API_BASE_URL:https://api.openweathermap.org/data/2.5/weather} | API基础URL，当前版本使用模拟数据，此配置项暂未使用 |
| weather.units | metric | 温度单位：metric（摄氏度）、imperial（华氏度）、standard（开尔文） |
| weather.lang | zh_cn | 语言 |
| weather.timeout | 5000 | 超时时间，单位：毫秒 |
| weather.cache.time | 1800 | 缓存时间，单位：秒 |

**注意**：当前版本的天气服务使用模拟数据，不会实际调用外部API。配置项保留以便未来集成真实的天气API。

## 6. REST API接口

### 6.1 基本信息

- **路径**: /api/v1/weather
- **方法**: GET
- **说明**: 提供全球城市天气查询服务

### 6.2 API参数说明


**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| city | String | 是 | 城市名称 |


### 6.3 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "city": "示例值",
    "temperature": "示例值",
    "humidity": "示例值",
    "pressure": "示例值",
    "windSpeed": "示例值",
    "description": "示例值",
    "timestamp": "示例值"
  }
}
```

### 6.4 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 6.5 REST API调用示例

#### 6.5.1 请求参数JSON示例

以下是可以直接复制使用的请求参数JSON：

```json
{
  "city": "Beijing"
}
```

#### 6.5.2 curl调用示例

```bash
# 使用curl调用REST API
curl -X GET "/api/v1/weather?city=Beijing" \
  -H "Content-Type: application/json"
```

#### 6.5.3 响应示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "city": "Beijing",
    "temperature": 25.5,
    "humidity": 45,
    "pressure": 1013,
    "windSpeed": 3.5,
    "description": "晴朗",
    "timestamp": 1714899234567
  }
}
```


## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/weather/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `weather`
- **描述**: `提供全球城市天气查询服务`
- **参数**:
  - **city**: 城市名称，如：Beijing, Shanghai, New York等 (string, 必填)

### 7.4 MCP协议调用示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/weather/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行工具
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "weather",
        params: {
            "city": "Beijing"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "city": "Beijing",
    //   "temperature": 25.5,
    //   "humidity": 45,
    //   "pressure": 1013,
    //   "windSpeed": 3.5,
    //   "description": "晴朗",
    //   "timestamp": 1714899234567
    // }
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/weather/sse`（仅访问天气服务服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用天气服务服务的请求，例如：`查询北京的天气`
3. Cline会自动调用MCP服务，并返回结果


## 8. 常见问题

1. **问题**: 如何配置天气服务？
   **回答**: 可以通过修改`application.properties`或`application.yml`文件中的相关配置项来配置服务。也可以通过环境变量设置配置项，例如`WEATHER_API_KEY`。

2. **问题**: 天气服务支持哪些数据格式？
   **回答**: 服务支持JSON格式的数据交换。

3. **问题**: 天气服务是否使用真实的天气数据？
   **回答**: 当前版本使用模拟数据，对于特定城市（如北京、上海等）提供固定的模拟数据，对于其他城市则生成随机数据。未来版本计划集成真实的天气API。

4. **问题**: 如何扩展天气服务支持更多城市的精确数据？
   **回答**: 可以在`WeatherMcpTool.java`文件中的`switch`语句中添加更多城市的case分支，为每个城市提供特定的天气数据。

5. **问题**: 天气服务的数据更新频率是多少？
   **回答**: 当前版本使用模拟数据，每次请求都会生成新的时间戳，但温度、湿度等数据对于特定城市是固定的。集成真实API后，数据更新频率将取决于缓存配置（默认30分钟）。

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [开发指南](/docs/guides/development)
- [代码生成器指南](/docs/guides/code-generator)
