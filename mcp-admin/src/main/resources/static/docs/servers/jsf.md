# JSF开放平台服务

JSF开放平台MCP服务提供了对JSF（JD Service Framework）开放API的访问能力，支持接口查询、方法信息获取、别名查询等功能。

## 服务概述

JSF是京东内部的服务框架，提供了丰富的开放API来查询和管理服务接口。本MCP服务封装了JSF开放平台的核心功能，使用户能够通过统一的MCP协议访问JSF服务。

## 主要功能

### 1. 接口信息查询 (getInterfaceInfo)
根据接口名称查询JSF接口的详细信息，包括接口基本信息、提供者和消费者统计等。

**参数说明：**
- `interfaceName` (必填): JSF接口的完整类名，如：`com.jd.example.service.ExampleService`
- `operator` (必填): 操作人的ERP账号

**返回结果：**
- `success`: 操作是否成功
- `code`: JSF API返回码
- `message`: 返回消息
- `data`: 接口详细信息对象

### 2. 方法信息查询 (getMethodInfo)
获取接口方法的详细信息，包含入参和出参信息。此功能通过telnet到存活的Provider获取方法信息。

**参数说明：**
- `interfaceName` (必填): JSF接口的完整类名
- `methodName` (可选): 要查询的方法名称，如果为空则返回所有方法信息
- `alias` (可选): 指定的别名
- `ip` (可选): Provider的IP地址
- `port` (可选): Provider的端口号
- `operator` (必填): 操作人的ERP账号

**返回结果：**
- `success`: 操作是否成功
- `code`: JSF API返回码
- `message`: 返回消息
- `data`: 方法详细信息的JSON字符串

### 3. 方法列表查询 (getMethodList)
获取接口的所有方法列表。

**参数说明：**
- `interfaceName` (必填): JSF接口的完整类名
- `alias` (可选): 指定的别名
- `ip` (可选): Provider的IP地址
- `port` (可选): Provider的端口号
- `operator` (必填): 操作人的ERP账号

**返回结果：**
- `success`: 操作是否成功
- `code`: JSF API返回码
- `message`: 返回消息
- `data`: 方法名称列表
- `total`: 方法总数

### 4. 别名查询 (getAliasByInterfaceName)
根据接口名称获取所有可用的别名列表。

**参数说明：**
- `interfaceName` (必填): JSF接口的完整类名
- `operator` (必填): 操作人的ERP账号

**返回结果：**
- `success`: 操作是否成功
- `code`: JSF API返回码
- `message`: 返回消息
- `data`: 别名列表
- `total`: 别名总数

## 配置说明

服务配置文件位于 `servers/jsf.properties`，包含以下配置项：

```properties
# JSF开放API配置
jsf.openapi.appKey=jdos_ofw-outbound
jsf.openapi.token=Jk7J2Lp9XmN4QwR8vT3sB6yH1gF5dE0z
jsf.openapi.index=test.i.jsf.jd.local

# 默认配置
jsf.default.operator=system
jsf.default.timeout=5000
jsf.default.retries=3
```

## 使用示例

### MCP协议调用示例

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "jsf/getInterfaceInfo",
    "arguments": {
      "interfaceName": "com.jd.example.service.ExampleService",
      "operator": "testuser"
    }
  }
}
```

### API调用示例

```bash
curl -X POST http://localhost:8080/api/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "toolName": "jsf/getMethodList",
    "params": {
      "interfaceName": "erp.ql.station.api.service.gangao.CustomsClearanceApi",
      "operator": "testuser"
    }
  }'
```

## 注意事项

1. **网络连接**: 方法信息查询功能需要通过telnet连接到Provider，在测试环境中可能会出现连接超时的情况。

2. **权限要求**: 使用JSF开放API需要有效的appKey和token，请确保配置正确。

3. **接口继承**: 如果接口存在继承关系，可能无法获取到父接口的方法信息。

4. **环境配置**: 不同环境需要配置不同的注册中心地址，测试环境使用 `test.i.jsf.jd.local`，生产环境使用 `i.jsf.jd.com`。

## 错误处理

服务提供了完善的错误处理机制：

- **参数验证错误**: 当必填参数为空时返回参数错误信息
- **JSF API错误**: 当JSF API调用失败时返回具体的错误码和错误信息
- **网络连接错误**: 当无法连接到JSF服务时返回连接错误信息
- **系统异常**: 提供详细的异常堆栈信息用于问题排查

## 技术架构

- **框架**: Spring Boot + JSF开放API
- **认证**: 基于appKey和token的签名认证
- **通信**: HTTP + JSF协议
- **数据格式**: JSON

## 版本信息

- **当前版本**: 1.0.0
- **JSF API版本**: 2.2.85
- **支持的JSF版本**: 1.7.8+
