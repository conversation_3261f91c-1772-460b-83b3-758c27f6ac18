# 读取JoySpace服务

## 1. 服务概述

- **服务ID**: joyspace
- **版本**: 1.0.0
- **类型**: API
- **生成时间**: 2025-05-01T20:32:39.000000

读取JoySpace服务，支持读取子文件夹列表、文件列表、页面信息、页面内容，提供对JoySpace文档协作平台的数据访问能力。

## 2. 功能说明

JoySpace服务提供以下核心功能：

1. **获取子文件夹列表** - 获取指定文件夹下的所有子文件夹列表，支持按更新时间等字段排序
2. **获取文件列表** - 获取指定文件夹下的所有文件列表，支持分页和排序
3. **获取页面信息** - 获取指定页面的基本信息，如标题、创建者、更新时间等
4. **获取页面内容** - 获取指定页面的详细内容，并支持将内容转换为Markdown格式

服务通过调用JoySpace开放API实现数据访问，支持对企业内部知识库的高效检索和内容获取。

## 3. 参数说明

### 3.1 getFolderList 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| folderurl | String | 是 | - | 文件夹唯一标识符，**仅需提供链接最后的ID部分**，例如：`0q9HlwuJQu5zYMKYQ5Hx`（而非完整URL） |
| sort | String | 否 | offset | 排序字段，可选值：offset（默认，按位置升序）、updated_at（按更新时间升序）、-updated_at（按更新时间降序） |

> **重要说明**：folderurl 参数只需要提供 JoySpace 链接最后的唯一标识符，而不是完整的 URL。
> 例如，对于链接 `https://joyspace.jd.com/teams/b10vXr-LbPQ60n1pb7D3/0q9HlwuJQu5zYMKYQ5Hx`，
> 只需提供 `0q9HlwuJQu5zYMKYQ5Hx`。

### 3.2 getFileList 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| folderurl | String | 是 | - | 文件夹唯一标识符，**仅需提供链接最后的ID部分**，例如：`0q9HlwuJQu5zYMKYQ5Hx`（而非完整URL） |
| sort | String | 否 | offset | 排序字段，可选值：offset（默认，按位置升序）、updated_at（按更新时间升序）、-updated_at（按更新时间降序） |
| start | String | 否 | 0 | 起始位置，用于分页查询 |
| length | String | 否 | 100 | 返回的最大条目数，用于分页查询 |

### 3.3 getPageInfo 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| pageurl | String | 是 | - | 页面唯一标识符，**仅需提供链接最后的ID部分**，例如：`DPgHDxqCMEYdGtwwQ9ju`（而非完整URL） |

### 3.4 getPageContent 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| pageurl | String | 是 | - | 页面唯一标识符，**仅需提供链接最后的ID部分**，例如：`DPgHDxqCMEYdGtwwQ9ju`（而非完整URL） |

> **重要说明**：pageurl 参数只需要提供 JoySpace 链接最后的唯一标识符，而不是完整的 URL。
> 例如，对于链接 `https://joyspace.jd.com/page/DPgHDxqCMEYdGtwwQ9ju`，
> 只需提供 `DPgHDxqCMEYdGtwwQ9ju`。

## 4. 输出说明

### 4.1 getFolderList 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | Boolean | 操作是否成功，true表示成功，false表示失败 |
| jsonData | String | 完整的原始JSON数据，包含子文件夹列表的详细信息 |
| error | String | 错误信息，仅当success为false时返回 |

### 4.2 getFileList 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | Boolean | 操作是否成功，true表示成功，false表示失败 |
| jsonData | String | 完整的原始JSON数据，包含文件列表的详细信息 |
| error | String | 错误信息，仅当success为false时返回 |

### 4.3 getPageInfo 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | Boolean | 操作是否成功，true表示成功，false表示失败 |
| jsonData | String | 完整的原始JSON数据，包含页面基本信息 |
| error | String | 错误信息，仅当success为false时返回 |

### 4.4 getPageContent 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | Boolean | 操作是否成功，true表示成功，false表示失败 |
| markdownData | String | 转换后的Markdown格式内容，方便在各种环境中展示和使用 |
| error | String | 错误信息，仅当success为false时返回 |


## 5. 配置说明

| 配置项 | 默认值 | 说明 |
|-------|--------|------|
| joyspace.teamId | b10vXr-LbPQ60n1pb7D3 | JoySpace团队ID，用于标识要访问的JoySpace团队 |

**注意**：
- 团队ID是访问JoySpace内容的重要凭证，请确保配置正确
- 团队ID可以从JoySpace团队URL中获取，通常是URL中的一段字符串
- 此配置项可以通过环境变量或配置文件进行覆盖

## 6. REST API接口

### 6.1 基本信息

- **路径**: /api/v1/joyspace
- **方法**: GET
- **说明**: 读取JoySpace服务，支持读取子文件夹列表、文件列表、页面信息、页面内容

### 6.2 API参数说明

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| method | String | 是 | 方法名称，可选值：getFolderList, getFileList, getPageInfo, getPageContent |
| folderurl | String | 否 | 文件夹唯一标识符，**仅需提供链接最后的ID部分**，当method为getFolderList或getFileList时必填 |
| sort | String | 否 | 排序字段，当method为getFolderList或getFileList时可选 |
| start | String | 否 | 起始位置，当method为getFileList时可选 |
| length | String | 否 | 返回的最大条目数，当method为getFileList时可选 |
| pageurl | String | 否 | 页面唯一标识符，**仅需提供链接最后的ID部分**，当method为getPageInfo或getPageContent时必填 |

> **重要说明**：folderurl 和 pageurl 参数只需要提供 JoySpace 链接最后的唯一标识符，而不是完整的 URL。

### 6.3 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "jsonData": "{\\"code\\":0,\\"message\\":\\"success\\",\\"data\\":[...]}",
    "markdownData": "# 标题\n\n内容..."
  }
}
```

### 6.4 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 6.5 REST API调用示例

#### 6.5.1 获取子文件夹列表示例

```bash
# 使用curl获取子文件夹列表（注意：folderurl只需提供链接最后的ID部分）
curl -X GET "http://localhost:8080/api/v1/joyspace?method=getFolderList&folderurl=0q9HlwuJQu5zYMKYQ5Hx&sort=updated_at" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "jsonData": "{\"code\":0,\"message\":\"success\",\"data\":[{\"id\":\"folder1\",\"name\":\"产品文档\",\"created_at\":\"2025-01-01T10:00:00Z\",\"updated_at\":\"2025-01-10T15:30:00Z\"},{\"id\":\"folder2\",\"name\":\"技术文档\",\"created_at\":\"2025-01-02T09:00:00Z\",\"updated_at\":\"2025-01-12T11:20:00Z\"}]}"
  }
}
```

#### 6.5.2 获取文件列表示例

```bash
# 使用curl获取文件列表（注意：folderurl只需提供链接最后的ID部分）
curl -X GET "http://localhost:8080/api/v1/joyspace?method=getFileList&folderurl=0q9HlwuJQu5zYMKYQ5Hx&sort=updated_at&start=0&length=10" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "jsonData": "{\"code\":0,\"message\":\"success\",\"data\":[{\"id\":\"page1\",\"title\":\"产品需求文档\",\"type\":13,\"created_at\":\"2025-01-05T10:00:00Z\",\"updated_at\":\"2025-01-15T15:30:00Z\"},{\"id\":\"page2\",\"title\":\"技术方案设计\",\"type\":13,\"created_at\":\"2025-01-06T09:00:00Z\",\"updated_at\":\"2025-01-16T11:20:00Z\"}]}"
  }
}
```

#### 6.5.3 获取页面信息示例

```bash
# 使用curl获取页面信息（注意：pageurl只需提供链接最后的ID部分）
curl -X GET "http://localhost:8080/api/v1/joyspace?method=getPageInfo&pageurl=DPgHDxqCMEYdGtwwQ9ju" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "jsonData": "{\"code\":0,\"message\":\"success\",\"data\":[{\"id\":\"DPgHDxqCMEYdGtwwQ9ju\",\"title\":\"产品需求文档\",\"creator\":\"张三\",\"created_at\":\"2025-01-05T10:00:00Z\",\"updated_at\":\"2025-01-15T15:30:00Z\",\"type\":13,\"folder_id\":\"folder1\"}]}"
  }
}
```

#### 6.5.4 获取页面内容示例

```bash
# 使用curl获取页面内容（注意：pageurl只需提供链接最后的ID部分）
curl -X GET "http://localhost:8080/api/v1/joyspace?method=getPageContent&pageurl=jKnRdFujF3nfmz9mLsd7u" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "markdownData": "# 产品需求文档\n\n## 1. 背景\n\n本文档描述了产品的需求背景和功能要点。\n\n## 2. 功能列表\n\n- 功能1：用户登录\n- 功能2：数据查询\n- 功能3：报表导出\n\n## 3. 详细设计\n\n### 3.1 用户登录\n\n支持账号密码登录和扫码登录两种方式..."
  }
}


## API接口

该服务未启用API接口。

## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/joyspace/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `joyspace`
- **描述**: `读取JoySpace服务，支持读取子文件夹列表、文件列表、页面信息、页面内容`
- **方法**:
  - **getFolderList**: 获取指定文件夹下的子文件夹列表
    - **folderurl**: 文件夹URL，JoySpace文件夹的完整URL地址 (string, 必填)
    - **sort**: 排序字段，可选值：offset（默认，按位置升序）、updated_at（按更新时间升序）、-updated_at（按更新时间降序） (string, 可选)
  - **getFileList**: 获取指定文件夹下的文件列表
    - **folderurl**: 文件夹URL，JoySpace文件夹的完整URL地址 (string, 必填)
    - **sort**: 排序字段，可选值：offset（默认，按位置升序）、updated_at（按更新时间升序）、-updated_at（按更新时间降序） (string, 可选)
    - **start**: 起始位置，用于分页查询 (string, 可选)
    - **length**: 返回的最大条目数，用于分页查询 (string, 可选)
  - **getPageInfo**: 获取指定页面的基本信息
    - **pageurl**: 页面URL，JoySpace页面的完整URL地址 (string, 必填)
  - **getPageContent**: 获取指定页面的详细内容
    - **pageurl**: 页面URL，JoySpace页面的完整URL地址 (string, 必填)

### 7.4 MCP协议调用示例

#### 7.4.1 获取子文件夹列表示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/joyspace/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行获取子文件夹列表操作
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "getFolderList",
        params: {
            // 注意：folderurl只需提供链接最后的ID部分
            "folderurl": "0q9HlwuJQu5zYMKYQ5Hx",
            "sort": "updated_at"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "success": true,
    //   "jsonData": "{\"code\":0,\"message\":\"success\",\"data\":[{\"id\":\"folder1\",\"name\":\"产品文档\",\"created_at\":\"2025-01-01T10:00:00Z\",\"updated_at\":\"2025-01-10T15:30:00Z\"},{\"id\":\"folder2\",\"name\":\"技术文档\",\"created_at\":\"2025-01-02T09:00:00Z\",\"updated_at\":\"2025-01-12T11:20:00Z\"}]}"
    // }
});
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/joyspace/sse`（仅访问JoySpace服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用JoySpace服务的请求，例如：`获取JoySpace文档内容`
3. Cline会自动调用MCP服务，并返回结果

## 8. 常见问题

1. **问题**: 如何配置JoySpace服务？
   **回答**: 可以通过修改`servers/joyspace.properties`文件中的相关配置项来配置服务，主要是设置正确的团队ID。

2. **问题**: 如何获取JoySpace文件夹URL？
   **回答**: 在JoySpace网页版中打开需要访问的文件夹，然后从浏览器地址栏复制链接最后的ID部分。例如，对于链接 `https://joyspace.jd.com/teams/b10vXr-LbPQ60n1pb7D3/0q9HlwuJQu5zYMKYQ5Hx`，只需提取 `0q9HlwuJQu5zYMKYQ5Hx` 作为 folderurl 参数。

3. **问题**: 如何获取JoySpace页面URL？
   **回答**: 在JoySpace网页版中打开需要访问的页面，然后从浏览器地址栏复制链接最后的ID部分。例如，对于链接 `https://joyspace.jd.com/page/DPgHDxqCMEYdGtwwQ9ju`，只需提取 `DPgHDxqCMEYdGtwwQ9ju` 作为 pageurl 参数。

4. **问题**: 为什么folderurl和pageurl参数只需要提供ID部分？
   **回答**: 这是为了简化API调用，服务内部会自动构建完整的URL。只需提供唯一标识符部分，服务就能准确定位到相应的文件夹或页面。

5. **问题**: 为什么获取文件列表只返回文档类型？
   **回答**: 服务默认只过滤出文档类型的页面（pageType=13），这是为了提高查询效率和结果的可用性。

6. **问题**: 如何处理返回的JSON数据？
   **回答**: 服务返回的jsonData字段包含完整的原始JSON数据，可以通过JSON.parse()解析后使用。对于页面内容，服务还提供了转换后的markdownData字段，可以直接用于显示。

7. **问题**: 如何在分页获取大量文件？
   **回答**: 使用getFileList方法时，可以通过start和length参数实现分页获取，每次获取后增加start值继续获取下一页数据。

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [JoySpace开发者文档](https://joyspace.jd.com/docs)

## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/joyspace/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `joyspace`
- **描述**: `读取JoySpace服务，支持读取子文件夹列表、文件列表、页面信息、页面内容`
- **方法**:
  - **getFolderList**: 获取指定文件夹下的子文件夹列表
    - **folderurl**: 文件夹唯一标识符，**仅需提供链接最后的ID部分** (string, 必填)
    - **sort**: 排序字段，可选值：offset（默认，按位置升序）、updated_at（按更新时间升序）、-updated_at（按更新时间降序） (string, 可选)
  - **getFileList**: 获取指定文件夹下的文件列表
    - **folderurl**: 文件夹唯一标识符，**仅需提供链接最后的ID部分** (string, 必填)
    - **sort**: 排序字段，可选值：offset（默认，按位置升序）、updated_at（按更新时间升序）、-updated_at（按更新时间降序） (string, 可选)
    - **start**: 起始位置，用于分页查询 (string, 可选)
    - **length**: 返回的最大条目数，用于分页查询 (string, 可选)
  - **getPageInfo**: 获取指定页面的基本信息
    - **pageurl**: 页面唯一标识符，**仅需提供链接最后的ID部分** (string, 必填)
  - **getPageContent**: 获取指定页面的详细内容
    - **pageurl**: 页面唯一标识符，**仅需提供链接最后的ID部分** (string, 必填)

### 7.4 MCP协议调用示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/joyspace/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行获取子文件夹列表操作
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "getFolderList",
        params: {
            // 注意：folderurl只需提供链接最后的ID部分
            "folderurl": "0q9HlwuJQu5zYMKYQ5Hx",
            "sort": "updated_at"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/joyspace/sse`（仅访问读取JoySpace服务服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用读取JoySpace服务服务的请求，例如：`使用读取JoySpace服务服务`
3. Cline会自动调用MCP服务，并返回结果

## 8. 常见问题

1. **问题**: 如何配置读取JoySpace服务服务？
   **回答**: 可以通过修改`application.properties`或`application.yml`文件中的相关配置项来配置服务。

2. **问题**: 读取JoySpace服务服务支持哪些数据格式？
   **回答**: 服务支持JSON格式的数据交换。

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [开发指南](/docs/guides/development)
- [代码生成器指南](/docs/guides/code-generator)