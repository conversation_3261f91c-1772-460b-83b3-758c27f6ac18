# 时间服务

## 1. 服务概述

- **服务ID**: time
- **版本**: 1.0.0
- **类型**: API
- **生成时间**: 2025-05-01T19:09:42.000000

提供时间和时区转换服务，支持获取当前时间和在不同时区之间转换时间。服务可以返回格式化的日期时间、时间戳、时区偏移量等信息，并支持夏令时判断。

## 2. 功能说明

时间服务提供以下核心功能：

1. **获取当前时间** - 获取指定时区或默认时区的当前时间，返回多种格式的时间信息
2. **时区转换** - 在不同时区之间转换时间，计算时差，并提供源时区和目标时区的详细信息

## 3. 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| timezone | string | 否 | Asia/Shanghai | 时区ID，如：Asia/Shanghai, America/New_York等 |
| source_timezone | string | 是 | - | 源时区ID，如：Asia/Shanghai |
| target_timezone | string | 是 | - | 目标时区ID，如：America/New_York |
| time | string | 是 | - | 要转换的时间，格式为HH:mm |

## 4. 输出说明

### get_current_time 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | Boolean | 操作是否成功 |
| timezone | String | 时区ID |
| datetime | String | 完整的日期时间，格式由配置项 time.datetime_format 决定 |
| date | String | 日期部分，格式由配置项 time.date_format 决定 |
| time | String | 时间部分，格式由配置项 time.time_format 决定 |
| timestamp | Long | 时间戳，单位：毫秒 |
| is_dst | Boolean | 是否处于夏令时 |
| zone_offset | String | 时区偏移量 |
| error | String | 错误信息，仅当success为false时返回 |

### convert_time 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | Boolean | 操作是否成功 |
| source | Map | 源时间信息，包含时区、时间等详细信息 |
| target | Map | 目标时间信息，包含时区、时间等详细信息 |
| time_difference | String | 时差，格式为"+/-Xh"，如："+8h" |
| error | String | 错误信息，仅当success为false时返回 |

### source/target 字段详情

| 字段名 | 类型 | 说明 |
|-------|------|------|
| timezone | String | 时区ID |
| time | String | 时间，格式由配置项 time.time_format 决定 |
| date | String | 日期，格式由配置项 time.date_format 决定 |
| datetime | String | 完整的日期时间，格式由配置项 time.datetime_format 决定 |
| is_dst | Boolean | 是否处于夏令时 |
| zone_offset | String | 时区偏移量 |


## 5. 配置说明

| 配置项 | 默认值 | 说明 |
|-------|--------|------|
| time.default_timezone | Asia/Shanghai | 默认时区，当未指定时区时使用此值 |
| time.use_24hour_format | true | 是否使用24小时制，影响时间格式的显示方式 |
| time.date_format | yyyy-MM-dd | 日期格式，用于格式化日期部分 |
| time.time_format | HH:mm:ss | 时间格式，用于格式化时间部分 |
| time.datetime_format | yyyy-MM-dd'T'HH:mm:ssXXX | 日期时间格式，用于格式化完整的日期时间 |

**注意**：
- 时间格式使用 Java 的 DateTimeFormatter 格式，详见 [Java DateTimeFormatter 文档](https://docs.oracle.com/javase/8/docs/api/java/time/format/DateTimeFormatter.html)
- 当 time.use_24hour_format 为 false 时，时间格式会自动调整为 "hh:mm:ss a"（12小时制）

## 6. REST API接口

### 6.1 基本信息

- **路径**: /api/v1/time
- **方法**: GET
- **说明**: 提供时间和时区转换服务

### 6.2 API参数说明

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| method | String | 是 | 方法名称，可选值：get_current_time, convert_time |
| timezone | String | 否 | 时区ID，如：Asia/Shanghai, America/New_York等 |
| source_timezone | String | 是 | 源时区ID，如：Asia/Shanghai（仅当method=convert_time时需要） |
| target_timezone | String | 是 | 目标时区ID，如：America/New_York（仅当method=convert_time时需要） |
| time | String | 是 | 要转换的时间，格式为HH:mm（仅当method=convert_time时需要） |

### 6.3 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
    // get_current_time 方法响应
    "success": true,
    "timezone": "Asia/Shanghai",
    "datetime": "2025-05-01T15:30:45+08:00",
    "date": "2025-05-01",
    "time": "15:30:45",
    "timestamp": 1745889045000,
    "is_dst": false,
    "zone_offset": "+08:00"

    // 或 convert_time 方法响应
    "success": true,
    "source": {
      "timezone": "Asia/Shanghai",
      "time": "14:30:00",
      "date": "2025-05-01",
      "datetime": "2025-05-01T14:30:00+08:00",
      "is_dst": false,
      "zone_offset": "+08:00"
    },
    "target": {
      "timezone": "America/New_York",
      "time": "02:30:00",
      "date": "2025-05-01",
      "datetime": "2025-05-01T02:30:00-04:00",
      "is_dst": true,
      "zone_offset": "-04:00"
    },
    "time_difference": "-12h"
  }
}
```

### 6.4 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 6.5 REST API调用示例

#### 6.5.1 获取当前时间示例

```bash
# 使用curl调用REST API获取当前时间
curl -X GET "http://localhost:8080/api/v1/time?method=get_current_time&timezone=Asia/Shanghai" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "timezone": "Asia/Shanghai",
    "datetime": "2025-05-01T15:30:45+08:00",
    "date": "2025-05-01",
    "time": "15:30:45",
    "timestamp": 1745889045000,
    "is_dst": false,
    "zone_offset": "+08:00"
  }
}
```

#### 6.5.2 时区转换示例

```bash
# 使用curl调用REST API进行时区转换
curl -X GET "http://localhost:8080/api/v1/time?method=convert_time&source_timezone=Asia/Shanghai&target_timezone=America/New_York&time=14:30" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "source": {
      "timezone": "Asia/Shanghai",
      "time": "14:30:00",
      "date": "2025-05-01",
      "datetime": "2025-05-01T14:30:00+08:00",
      "is_dst": false,
      "zone_offset": "+08:00"
    },
    "target": {
      "timezone": "America/New_York",
      "time": "02:30:00",
      "date": "2025-05-01",
      "datetime": "2025-05-01T02:30:00-04:00",
      "is_dst": true,
      "zone_offset": "-04:00"
    },
    "time_difference": "-12h"
  }
}


## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/time/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `time`
- **描述**: `提供时间和时区转换服务`
- **方法**:
  - **get_current_time**: 获取当前时间，如果不指定时区则使用配置的默认时区
    - **timezone**: 时区ID，如：Asia/Shanghai, America/New_York等 (string, 可选)
  - **convert_time**: 在不同时区之间转换时间
    - **source_timezone**: 源时区ID，如：Asia/Shanghai (string, 必填)
    - **target_timezone**: 目标时区ID，如：America/New_York (string, 必填)
    - **time**: 要转换的时间，格式为HH:mm (string, 必填)

### 7.4 MCP协议调用示例

#### 7.4.1 获取当前时间

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/time/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行get_current_time方法
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "get_current_time",
        params: {
            timezone: "Asia/Shanghai"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "success": true,
    //   "timezone": "Asia/Shanghai",
    //   "datetime": "2025-05-01T15:30:45+08:00",
    //   "date": "2025-05-01",
    //   "time": "15:30:45",
    //   "timestamp": 1745889045000,
    //   "is_dst": false,
    //   "zone_offset": "+08:00"
    // }
});
```

#### 7.4.2 时区转换

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/time/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行convert_time方法
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "convert_time",
        params: {
            source_timezone: "Asia/Shanghai",
            target_timezone: "America/New_York",
            time: "14:30"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "success": true,
    //   "source": {
    //     "timezone": "Asia/Shanghai",
    //     "time": "14:30:00",
    //     "date": "2025-05-01",
    //     "datetime": "2025-05-01T14:30:00+08:00",
    //     "is_dst": false,
    //     "zone_offset": "+08:00"
    //   },
    //   "target": {
    //     "timezone": "America/New_York",
    //     "time": "02:30:00",
    //     "date": "2025-05-01",
    //     "datetime": "2025-05-01T02:30:00-04:00",
    //     "is_dst": true,
    //     "zone_offset": "-04:00"
    //   },
    //   "time_difference": "-12h"
    // }
});
```
```

## 8. 常见问题

1. **问题**: 如何配置时间服务？
   **回答**: 可以通过修改`application.properties`或`application.yml`文件中的相关配置项来配置服务。例如，设置默认时区、日期时间格式等。

2. **问题**: 时间服务支持哪些时区？
   **回答**: 时间服务支持 Java 的 ZoneId 类支持的所有时区，包括 Asia/Shanghai、America/New_York、Europe/London 等。可以使用 `ZoneId.getAvailableZoneIds()` 获取完整的时区列表。

3. **问题**: 如何处理夏令时？
   **回答**: 时间服务自动处理夏令时，通过 `is_dst` 字段指示当前时间是否处于夏令时。时区转换时也会考虑夏令时的影响。

4. **问题**: 为什么时区转换后的日期可能不同？
   **回答**: 当跨越国际日期变更线或时差较大时，转换后的日期可能会不同。例如，从亚洲转换到美洲的时间，可能会导致日期减少一天。

5. **问题**: 如何获取两个时区之间的时差？
   **回答**: 使用 `convert_time` 方法可以获取两个时区之间的时差，结果在 `time_difference` 字段中返回，格式为 "+/-Xh"。

6. **问题**: 时间服务是否支持历史时间的转换？
   **回答**: 当前版本仅支持当前时间的获取和时区转换，不支持历史时间的转换。未来版本可能会添加此功能。

## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/time/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `time`
- **描述**: `提供时间和时区转换服务`
- **方法**:
  - **get_current_time**: 获取当前时间，如果不指定时区则使用配置的默认时区
    - **timezone**: 时区ID，如：Asia/Shanghai, America/New_York等 (string, 可选)
  - **convert_time**: 在不同时区之间转换时间
    - **source_timezone**: 源时区ID，如：Asia/Shanghai (string, 必填)
    - **target_timezone**: 目标时区ID，如：America/New_York (string, 必填)
    - **time**: 要转换的时间，格式为HH:mm (string, 必填)

### 7.4 MCP协议调用示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/time/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行工具
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "time",
        params: {
            "timezone": "Asia/Shanghai",
            "source_timezone": "Asia/Shanghai",
            "target_timezone": "America/New_York",
            "time": "14:30"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/time/sse`（仅访问时间服务服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用时间服务服务的请求，例如：`使用时间服务服务`
3. Cline会自动调用MCP服务，并返回结果

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [开发指南](/docs/guides/development)
- [代码生成器指南](/docs/guides/code-generator)