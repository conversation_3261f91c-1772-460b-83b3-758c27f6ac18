# 待办服务

通过待办服务，管理待办任务

## 基本信息

- 服务ID：joywork
- 版本：1.0.0
- 类型：API
- 使用说明：通过待办服务，创建待办任务，更新待办任务，更新待办任务状态，催办待办任务

## 方法列表

### createTask

创建待办任务

#### 参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| title | String | 是 | 任务标题 | 交易618备战-硬件扩容 |
| remark | String | 是 | 任务说明 | 请在5月10日廊坊第一次军演之前完成硬件扩容 |
| starttime | String | 是 | 任务开始时间 | 2025-05-10 10:00:00 |
| endtime | String | 是 | 任务结束时间 | 2025-05-11 10:00:00 |
| executor | String | 是 | 任务执行人ERP，多个用逗号分隔 | bjliandahu,wangwangang |

#### 返回值

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 是否成功 true/false |
| result | String | 结果描述，成功时为空，失败时有具体错误信息 |
| taskId | String | 任务id |
| sourceId | String | 任务来源id，用于创建任务时的唯一标识 |

#### 示例

\`\`\`json
{
  "success": ,
  "result": "value",
  "taskId": "value",
  "sourceId": "value"
}
\`\`\`

### updateTask

更新待办任务

#### 参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| taskid | String | 是 | 任务id | 1234567890 |
| title | String | 是 | 任务标题 | 交易618备战-硬件扩容 |
| remark | String | 是 | 任务说明 | 请在5月10日廊坊第一次军演之前完成硬件扩容 |
| starttime | String | 是 | 任务开始时间 | 2025-05-10 10:00:00 |
| endtime | String | 是 | 任务结束时间 | 2025-05-11 10:00:00 |

#### 返回值

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 是否成功 true/false |
| result | String | 结果描述，成功时为空，失败时有具体错误信息 |
| data | String | 返回结果 |

#### 示例

\`\`\`json
{
  "success": ,
  "result": "value",
  "data": "value"
}
\`\`\`

### updateTaskStatus

更新待办任务状态

#### 参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| taskid | String | 是 | 任务id | 1234567890 |
| taskstatus | String | 是 | 任务状态，taskStatus = 1 未完成；taskStatus = 2 完成 | 1 |

#### 返回值

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 是否成功 true/false |
| result | String | 结果描述，成功时为空，失败时有具体错误信息 |
| data | String | 返回结果 |

#### 示例

\`\`\`json
{
  "success": ,
  "result": "value",
  "data": "value"
}
\`\`\`

### urgeTask

催办待办任务

#### 参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| taskid | String | 是 | 任务id | 1234567890 |
| urgecontent | String | 是 | 催办内容 | 请尽快完成任务 |
| taskusers | String | 是 | 抄送人，多个用逗号分隔 | bjliandahu,wangwangang |

#### 返回值

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 是否成功 true/false |
| result | String | 结果描述，成功时为空，失败时有具体错误信息 |
| data | String | 返回结果 |

#### 示例

\`\`\`json
{
  "success": ,
  "result": "value",
  "data": "value"
}
\`\`\`


## 配置项

| 配置项 | 默认值 | 描述 |
|--------|--------|------|
| joywork_content | http://mcp.jdl.com | pc端跳转链接 |
| joywork_mobile_content | https://mcp.jdl.com | 移动端跳转链接 |
| joywork_sourceDescZh | 请前往MCP平台处理 | 中文任务来源描述 |
| joywork_sourceDescEn | Please go to MCP platform to handle | 英文任务来源描述 |
| joywork_sourceTrusteeship | 1 | 是否能从joywork完成该任务，0否，1是 |

## 使用示例

### createTask

\`\`\`bash
curl -X GET "http://localhost:8080/api/v1/joywork?method=createTask&title=交易618备战-硬件扩容&remark=请在5月10日廊坊第一次军演之前完成硬件扩容&starttime=2025-05-10 10:00:00&endtime=2025-05-11 10:00:00&executor=bjliandahu,wangwangang"
\`\`\`

响应：

\`\`\`json
{
  "success": ,
  "result": "value",
  "taskId": "value",
  "sourceId": "value"
}
\`\`\`

### updateTask

\`\`\`bash
curl -X GET "http://localhost:8080/api/v1/joywork?method=updateTask&taskid=1234567890&title=交易618备战-硬件扩容&remark=请在5月10日廊坊第一次军演之前完成硬件扩容&starttime=2025-05-10 10:00:00&endtime=2025-05-11 10:00:00"
\`\`\`

响应：

\`\`\`json
{
  "success": ,
  "result": "value",
  "data": "value"
}
\`\`\`

### updateTaskStatus

\`\`\`bash
curl -X GET "http://localhost:8080/api/v1/joywork?method=updateTaskStatus&taskid=1234567890&taskstatus=1"
\`\`\`

响应：

\`\`\`json
{
  "success": ,
  "result": "value",
  "data": "value"
}
\`\`\`

### urgeTask

\`\`\`bash
curl -X GET "http://localhost:8080/api/v1/joywork?method=urgeTask&taskid=1234567890&urgecontent=请尽快完成任务&taskusers=bjliandahu,wangwangang"
\`\`\`

响应：

\`\`\`json
{
  "success": ,
  "result": "value",
  "data": "value"
}
\`\`\`


## API接口

该服务未启用API接口。

## MCP协议接口

### 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/joywork/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 工具信息

- **工具ID**: `joywork`
- **描述**: `通过待办服务，管理待办任务`
- **参数**:
  - **title**: 任务标题 (String, 必填)
  - **remark**: 任务说明 (String, 必填)
  - **starttime**: 任务开始时间 (String, 必填)
  - **endtime**: 任务结束时间 (String, 必填)
  - **executor**: 任务执行人ERP，多个用逗号分隔 (String, 必填)

### MCP协议调用示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/joywork/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行工具
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "joywork",
        params: {
            title: "交易618备战-硬件扩容",
            remark: "请在5月10日廊坊第一次军演之前完成硬件扩容",
            starttime: "2025-05-10 10:00:00",
            endtime: "2025-05-11 10:00:00",
            executor: "bjliandahu,wangwangang"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
});
```

## 1. 服务概述

## 2. 功能说明

## 3. 参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| title | String | 是 | - | 任务标题 |
| remark | String | 是 | - | 任务说明 |
| starttime | String | 是 | - | 任务开始时间 |
| endtime | String | 是 | - | 任务结束时间 |
| executor | String | 是 | - | 任务执行人ERP，多个用逗号分隔 |
| taskid | String | 是 | - | 任务id |
| title | String | 是 | - | 任务标题 |
| remark | String | 是 | - | 任务说明 |
| starttime | String | 是 | - | 任务开始时间 |
| endtime | String | 是 | - | 任务结束时间 |
| taskid | String | 是 | - | 任务id |
| taskstatus | String | 是 | - | 任务状态，taskStatus = 1 未完成；taskStatus = 2 完成 |
| taskid | String | 是 | - | 任务id |
| urgecontent | String | 是 | - | 催办内容 |
| taskusers | String | 是 | - | 抄送人，多个用逗号分隔 |

## 4. 输出说明

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 是否成功 true/false |
| result | String | 结果描述，成功时为空，失败时有具体错误信息 |
| taskId | String | 任务id |
| sourceId | String | 任务来源id，用于创建任务时的唯一标识 |
| success | boolean | 是否成功 true/false |
| result | String | 结果描述，成功时为空，失败时有具体错误信息 |
| data | String | 返回结果 |
| success | boolean | 是否成功 true/false |
| result | String | 结果描述，成功时为空，失败时有具体错误信息 |
| data | String | 返回结果 |
| success | boolean | 是否成功 true/false |
| result | String | 结果描述，成功时为空，失败时有具体错误信息 |
| data | String | 返回结果 |

## 5. 配置说明

| 配置项 | 默认值 | 说明 |
|-------|--------|------|
| joywork_content | http://mcp.jdl.com | pc端跳转链接 |
| joywork_mobile_content | https://mcp.jdl.com | 移动端跳转链接 |
| joywork_sourceDescZh | 请前往MCP平台处理 | 中文任务来源描述 |
| joywork_sourceDescEn | Please go to MCP platform to handle | 英文任务来源描述 |
| joywork_sourceTrusteeship | 1 | 是否能从joywork完成该任务，0否，1是 |

## 6. REST API接口

### 6.1 基本信息

- **路径**: /api/v1/joywork
- **方法**: GET
- **说明**: 通过待办服务，管理待办任务

### 6.2 API参数说明

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| title | String | 是 | 任务标题 |
| remark | String | 是 | 任务说明 |
| starttime | String | 是 | 任务开始时间 |
| endtime | String | 是 | 任务结束时间 |
| executor | String | 是 | 任务执行人ERP，多个用逗号分隔 |
| taskid | String | 是 | 任务id |
| title | String | 是 | 任务标题 |
| remark | String | 是 | 任务说明 |
| starttime | String | 是 | 任务开始时间 |
| endtime | String | 是 | 任务结束时间 |
| taskid | String | 是 | 任务id |
| taskstatus | String | 是 | 任务状态，taskStatus = 1 未完成；taskStatus = 2 完成 |
| taskid | String | 是 | 任务id |
| urgecontent | String | 是 | 催办内容 |
| taskusers | String | 是 | 抄送人，多个用逗号分隔 |

### 6.3 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": "示例值",
    "result": "示例值",
    "taskId": "示例值",
    "sourceId": "示例值"
    "success": "示例值",
    "result": "示例值",
    "data": "示例值"
    "success": "示例值",
    "result": "示例值",
    "data": "示例值"
    "success": "示例值",
    "result": "示例值",
    "data": "示例值"
  }
}
```

### 6.4 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 6.5 REST API调用示例

#### 6.5.1 请求参数JSON示例

```json
{
  "title": "交易618备战-硬件扩容",
  "remark": "请在5月10日廊坊第一次军演之前完成硬件扩容",
  "starttime": "2025-05-10 10:00:00",
  "endtime": "2025-05-11 10:00:00",
  "executor": "bjliandahu,wangwangang"
  "taskid": "1234567890",
  "title": "交易618备战-硬件扩容",
  "remark": "请在5月10日廊坊第一次军演之前完成硬件扩容",
  "starttime": "2025-05-10 10:00:00",
  "endtime": "2025-05-11 10:00:00"
  "taskid": "1234567890",
  "taskstatus": "1"
  "taskid": "1234567890",
  "urgecontent": "请尽快完成任务",
  "taskusers": "bjliandahu,wangwangang"
}
```

#### 6.5.2 curl调用示例

```bash
curl -X GET "/api/v1/joywork?title=value&remark=value&starttime=value&endtime=value&executor=valuetaskid=value&title=value&remark=value&starttime=value&endtime=valuetaskid=value&taskstatus=valuetaskid=value&urgecontent=value&taskusers=value" \
  -H "Content-Type: application/json"
```

## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/joywork/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `joywork`
- **描述**: `通过待办服务，管理待办任务`
- **参数**:
  - **title**: 任务标题 (String, 必填)
  - **remark**: 任务说明 (String, 必填)
  - **starttime**: 任务开始时间 (String, 必填)
  - **endtime**: 任务结束时间 (String, 必填)
  - **executor**: 任务执行人ERP，多个用逗号分隔 (String, 必填)
  - **taskid**: 任务id (String, 必填)
  - **title**: 任务标题 (String, 必填)
  - **remark**: 任务说明 (String, 必填)
  - **starttime**: 任务开始时间 (String, 必填)
  - **endtime**: 任务结束时间 (String, 必填)
  - **taskid**: 任务id (String, 必填)
  - **taskstatus**: 任务状态，taskStatus = 1 未完成；taskStatus = 2 完成 (String, 必填)
  - **taskid**: 任务id (String, 必填)
  - **urgecontent**: 催办内容 (String, 必填)
  - **taskusers**: 抄送人，多个用逗号分隔 (String, 必填)

### 7.4 MCP协议调用示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/joywork/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行工具
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "joywork",
        params: {
            "title": "交易618备战-硬件扩容",
            "remark": "请在5月10日廊坊第一次军演之前完成硬件扩容",
            "starttime": "2025-05-10 10:00:00",
            "endtime": "2025-05-11 10:00:00",
            "executor": "bjliandahu,wangwangang"
            "taskid": "1234567890",
            "title": "交易618备战-硬件扩容",
            "remark": "请在5月10日廊坊第一次军演之前完成硬件扩容",
            "starttime": "2025-05-10 10:00:00",
            "endtime": "2025-05-11 10:00:00"
            "taskid": "1234567890",
            "taskstatus": "1"
            "taskid": "1234567890",
            "urgecontent": "请尽快完成任务",
            "taskusers": "bjliandahu,wangwangang"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/joywork/sse`（仅访问待办服务服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用待办服务服务的请求，例如：`使用待办服务服务`
3. Cline会自动调用MCP服务，并返回结果

## 8. 常见问题

1. **问题**: 如何配置待办服务服务？
   **回答**: 可以通过修改`application.properties`或`application.yml`文件中的相关配置项来配置服务。

2. **问题**: 待办服务服务支持哪些数据格式？
   **回答**: 服务支持JSON格式的数据交换。

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [开发指南](/docs/guides/development)
- [代码生成器指南](/docs/guides/code-generator) 