# 多集群Redis操作服务

## 1. 服务概述

- **服务ID**: common_redis
- **版本**: 1.0.0
- **类型**: API
- **生成时间**: 2025-04-26T15:19:43.417577

提供多集群Redis数据库操作功能，支持字符串、哈希、键值等操作，可以连接和管理多个Redis集群，提供统一的接口进行数据访问和操作。通过简单的配置文件修改，可以轻松扩展管理新的Redis集群，无需修改代码。

## 2. 功能说明

多集群Redis操作服务提供以下核心功能：

1. **字符串操作** - 支持获取(GET)、设置(SET)、删除(DEL)等字符串操作
2. **哈希操作** - 支持获取(HGET)、设置(HSET)、删除(HDEL)等哈希操作
3. **键操作** - 支持检查键是否存在(EXISTS)、设置过期时间(EXPIRE)等键操作
4. **服务器操作** - 支持获取服务器信息(INFO)、测试服务器连接(PING)等服务器操作

服务支持多个Redis集群的配置，每个集群可以连接到不同的Redis服务器，具有独立的连接池配置。为了保证数据安全，服务限制了一些危险命令的执行，如FLUSHALL、FLUSHDB等。

## 3. 参数说明

### 3.1 字符串操作参数

#### get_string 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | string | 是 | cache | Redis集群名称，对应配置中的集群标识符（如main、cache、session） |
| key | string | 是 | - | Redis键，要获取的字符串键名 |

#### set_string 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | string | 是 | cache | Redis集群名称，对应配置中的集群标识符（如main、cache、session） |
| key | string | 是 | - | Redis键，要设置的字符串键名 |
| value | string | 是 | - | Redis值，要设置的字符串值 |
| expireSeconds | integer | 否 | - | 过期时间（秒），如果提供则设置键的过期时间 |

### 3.2 哈希操作参数

#### get_hash 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | string | 是 | cache | Redis集群名称，对应配置中的集群标识符（如main、cache、session） |
| key | string | 是 | - | Redis键，哈希表的键名 |
| field | string | 是 | - | Redis哈希字段，要获取的字段名 |

#### set_hash 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | string | 是 | cache | Redis集群名称，对应配置中的集群标识符（如main、cache、session） |
| key | string | 是 | - | Redis键，哈希表的键名 |
| field | string | 是 | - | Redis哈希字段，要设置的字段名 |
| value | string | 是 | - | Redis值，要设置的字段值 |

### 3.3 键操作参数

#### exists_key 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | string | 是 | cache | Redis集群名称，对应配置中的集群标识符（如main、cache、session） |
| key | string | 是 | - | Redis键，要检查是否存在的键名 |

#### expire_key 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | string | 是 | cache | Redis集群名称，对应配置中的集群标识符（如main、cache、session） |
| key | string | 是 | - | Redis键，要设置过期时间的键名 |
| seconds | integer | 是 | - | 过期时间（秒） |

### 3.4 服务器操作参数

#### get_server_info 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | string | 是 | cache | Redis集群名称，对应配置中的集群标识符（如main、cache、session） |
| section | string | 否 | - | 服务器信息部分，如server、clients、memory等，不提供则返回所有信息 |

#### ping_server 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | string | 是 | cache | Redis集群名称，对应配置中的集群标识符（如main、cache、session） |

## 4. 输出说明

### 4.1 通用输出字段

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 操作是否成功，true表示成功，false表示失败 |
| result | object/string | 操作结果，根据不同的操作类型返回不同的结果 |
| message | string | 操作消息，成功时通常包含操作成功的描述，失败时为空 |
| error | string | 错误信息，仅当success为false时返回，包含具体的错误描述 |

### 4.2 字符串操作输出

#### get_string 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 操作是否成功 |
| result | string | 获取到的字符串值，如果键不存在则为null |
| message | string | 操作消息 |
| error | string | 错误信息，仅当success为false时返回 |

#### set_string 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 操作是否成功 |
| result | string | 设置结果，通常为"OK" |
| message | string | 操作消息 |
| error | string | 错误信息，仅当success为false时返回 |

### 4.3 哈希操作输出

#### get_hash 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 操作是否成功 |
| result | string | 获取到的哈希字段值，如果键或字段不存在则为null |
| message | string | 操作消息 |
| error | string | 错误信息，仅当success为false时返回 |

#### set_hash 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 操作是否成功 |
| result | integer | 设置结果，1表示新建字段，0表示更新字段 |
| message | string | 操作消息 |
| error | string | 错误信息，仅当success为false时返回 |

### 4.4 键操作输出

#### exists_key 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 操作是否成功 |
| result | boolean | 键是否存在，true表示存在，false表示不存在 |
| message | string | 操作消息 |
| error | string | 错误信息，仅当success为false时返回 |

#### expire_key 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 操作是否成功 |
| result | integer | 设置结果，1表示成功设置过期时间，0表示键不存在 |
| message | string | 操作消息 |
| error | string | 错误信息，仅当success为false时返回 |

### 4.5 服务器操作输出

#### get_server_info 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 操作是否成功 |
| result | string | 服务器信息，包含多行文本格式的服务器状态信息 |
| message | string | 操作消息 |
| error | string | 错误信息，仅当success为false时返回 |

#### ping_server 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| success | boolean | 操作是否成功 |
| result | string | 服务器响应，通常为"PONG" |
| message | string | 操作消息 |
| error | string | 错误信息，仅当success为false时返回 |

## 5. 配置说明

| 配置项 | 默认值 | 说明 |
|-------|--------|------|
| redis.clusters | ${REDIS_CLUSTERS:main,cache,session} | Redis集群列表，以逗号分隔 |
| redis.main.alias | ${REDIS_MAIN_ALIAS:主数据集群} | main集群别名 |
| redis.main.host | ${REDIS_MAIN_HOST:localhost} | main集群Redis服务器地址 |
| redis.main.port | ${REDIS_MAIN_PORT:6379} | main集群Redis服务器端口 |
| redis.main.password | ${REDIS_MAIN_PASSWORD:} | main集群Redis服务器密码 |
| redis.main.database | ${REDIS_MAIN_DATABASE:0} | main集群Redis数据库索引 |
| redis.main.timeout | ${REDIS_MAIN_TIMEOUT:2000} | main集群Redis连接超时时间（毫秒） |
| redis.main.pool.max-total | ${REDIS_MAIN_POOL_MAX_TOTAL:8} | main集群Redis连接池最大连接数 |
| redis.main.pool.max-idle | ${REDIS_MAIN_POOL_MAX_IDLE:8} | main集群Redis连接池最大空闲连接数 |
| redis.main.pool.min-idle | ${REDIS_MAIN_POOL_MIN_IDLE:0} | main集群Redis连接池最小空闲连接数 |
| redis.cache.alias | ${REDIS_CACHE_ALIAS:缓存集群} | cache集群别名 |
| redis.cache.host | ${REDIS_CACHE_HOST:localhost} | cache集群Redis服务器地址 |
| redis.cache.port | ${REDIS_CACHE_PORT:6380} | cache集群Redis服务器端口 |
| redis.cache.password | ${REDIS_CACHE_PASSWORD:} | cache集群Redis服务器密码 |
| redis.cache.database | ${REDIS_CACHE_DATABASE:0} | cache集群Redis数据库索引 |
| redis.cache.timeout | ${REDIS_CACHE_TIMEOUT:2000} | cache集群Redis连接超时时间（毫秒） |
| redis.cache.pool.max-total | ${REDIS_CACHE_POOL_MAX_TOTAL:16} | cache集群Redis连接池最大连接数 |
| redis.cache.pool.max-idle | ${REDIS_CACHE_POOL_MAX_IDLE:8} | cache集群Redis连接池最大空闲连接数 |
| redis.cache.pool.min-idle | ${REDIS_CACHE_POOL_MIN_IDLE:0} | cache集群Redis连接池最小空闲连接数 |
| redis.session.alias | ${REDIS_SESSION_ALIAS:会话集群} | session集群别名 |
| redis.session.host | ${REDIS_SESSION_HOST:localhost} | session集群Redis服务器地址 |
| redis.session.port | ${REDIS_SESSION_PORT:6381} | session集群Redis服务器端口 |
| redis.session.password | ${REDIS_SESSION_PASSWORD:} | session集群Redis服务器密码 |
| redis.session.database | ${REDIS_SESSION_DATABASE:0} | session集群Redis数据库索引 |
| redis.session.timeout | ${REDIS_SESSION_TIMEOUT:2000} | session集群Redis连接超时时间（毫秒） |
| redis.session.pool.max-total | ${REDIS_SESSION_POOL_MAX_TOTAL:8} | session集群Redis连接池最大连接数 |
| redis.session.pool.max-idle | ${REDIS_SESSION_POOL_MAX_IDLE:8} | session集群Redis连接池最大空闲连接数 |
| redis.session.pool.min-idle | ${REDIS_SESSION_POOL_MIN_IDLE:0} | session集群Redis连接池最小空闲连接数 |
| redis.dangerous-commands | ${REDIS_DANGEROUS_COMMANDS:FLUSHALL,FLUSHDB,DEL,KEYS,CONFIG,EVAL,EVALSHA,SAVE,BGSAVE,DEBUG} | Redis危险命令列表，以逗号分隔 |

## 6. REST API接口

### 6.1 基本信息

- **路径**: /api/v1/redis
- **方法**: POST
- **说明**: 提供多集群Redis数据库操作功能，包括字符串、哈希、列表、集合、有序集合等操作

### 6.2 API参数说明



**请求体参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| cluster | Object | 是 | Redis集群名称，对应配置中的集群标识符（如main、cache、session） |
| command | Object | 是 | Redis命令 |
| key | Object | 否 | Redis键 |
| value | Object | 否 | Redis值 |
| field | Object | 否 | Redis哈希字段 |

### 6.3 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": "true",
    "result": "{}",
    "message": "示例值",
    "error": "示例值"
  }
}
```

### 6.4 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 6.5 REST API调用示例

#### 6.5.1 获取字符串值示例

```bash
# 使用curl获取字符串值
curl -X GET "http://localhost:8080/api/v1/redis?method=get_string&cluster=main&key=mykey" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "result": "Hello, Redis!",
    "message": "成功获取字符串值"
  }
}
```

#### 6.5.2 设置字符串值示例

```bash
# 使用curl设置字符串值
curl -X GET "http://localhost:8080/api/v1/redis?method=set_string&cluster=main&key=mykey&value=Hello%2C%20Redis!&expireSeconds=3600" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "result": "OK",
    "message": "成功设置字符串值"
  }
}
```

#### 6.5.3 获取哈希字段值示例

```bash
# 使用curl获取哈希字段值
curl -X GET "http://localhost:8080/api/v1/redis?method=get_hash&cluster=main&key=user:1001&field=name" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "result": "张三",
    "message": "成功获取哈希字段值"
  }
}
```

#### 6.5.4 设置哈希字段值示例

```bash
# 使用curl设置哈希字段值
curl -X GET "http://localhost:8080/api/v1/redis?method=set_hash&cluster=main&key=user:1001&field=name&value=%E5%BC%A0%E4%B8%89" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "result": 1,
    "message": "成功设置哈希字段值"
  }
}
```

#### 6.5.5 检查键是否存在示例

```bash
# 使用curl检查键是否存在
curl -X GET "http://localhost:8080/api/v1/redis?method=exists_key&cluster=main&key=mykey" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "result": true,
    "message": "键存在"
  }
}
```

#### 6.5.6 测试服务器连接示例

```bash
# 使用curl测试服务器连接
curl -X GET "http://localhost:8080/api/v1/redis?method=ping_server&cluster=main" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "result": "PONG",
    "message": "服务器响应: PONG"
  }
}
```


## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/common_redis/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `common_redis`
- **描述**: `提供多集群Redis数据库操作功能，支持字符串、哈希、键值等操作`
- **方法**:
  - **get_string**: 获取字符串值
    - **cluster**: Redis集群名称，对应配置中的集群标识符（如main、cache、session） (string, 必填)
    - **key**: Redis键，要获取的字符串键名 (string, 必填)
  - **set_string**: 设置字符串值
    - **cluster**: Redis集群名称，对应配置中的集群标识符（如main、cache、session） (string, 必填)
    - **key**: Redis键，要设置的字符串键名 (string, 必填)
    - **value**: Redis值，要设置的字符串值 (string, 必填)
    - **expireSeconds**: 过期时间（秒），如果提供则设置键的过期时间 (integer, 可选)
  - **get_hash**: 获取哈希字段值
    - **cluster**: Redis集群名称，对应配置中的集群标识符（如main、cache、session） (string, 必填)
    - **key**: Redis键，哈希表的键名 (string, 必填)
    - **field**: Redis哈希字段，要获取的字段名 (string, 必填)
  - **set_hash**: 设置哈希字段值
    - **cluster**: Redis集群名称，对应配置中的集群标识符（如main、cache、session） (string, 必填)
    - **key**: Redis键，哈希表的键名 (string, 必填)
    - **field**: Redis哈希字段，要设置的字段名 (string, 必填)
    - **value**: Redis值，要设置的字段值 (string, 必填)
  - **exists_key**: 检查键是否存在
    - **cluster**: Redis集群名称，对应配置中的集群标识符（如main、cache、session） (string, 必填)
    - **key**: Redis键，要检查是否存在的键名 (string, 必填)
  - **expire_key**: 设置键过期时间
    - **cluster**: Redis集群名称，对应配置中的集群标识符（如main、cache、session） (string, 必填)
    - **key**: Redis键，要设置过期时间的键名 (string, 必填)
    - **seconds**: 过期时间（秒） (integer, 必填)
  - **get_server_info**: 获取服务器信息
    - **cluster**: Redis集群名称，对应配置中的集群标识符（如main、cache、session） (string, 必填)
    - **section**: 服务器信息部分，如server、clients、memory等，不提供则返回所有信息 (string, 可选)
  - **ping_server**: 测试服务器连接
    - **cluster**: Redis集群名称，对应配置中的集群标识符（如main、cache、session） (string, 必填)

### 7.4 MCP协议调用示例

#### 7.4.1 获取字符串值示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/common_redis/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行获取字符串值操作
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "get_string",
        params: {
            "cluster": "main",
            "key": "mykey"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "success": true,
    //   "result": "Hello, Redis!",
    //   "message": "成功获取字符串值"
    // }
});
```

#### 7.4.2 设置哈希字段值示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/common_redis/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行设置哈希字段值操作
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "set_hash",
        params: {
            "cluster": "main",
            "key": "user:1001",
            "field": "name",
            "value": "张三"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "success": true,
    //   "result": 1,
    //   "message": "成功设置哈希字段值"
    // }
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/commonredis/sse`（仅访问多集群Redis操作服务服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用多集群Redis操作服务服务的请求，例如：`使用多集群Redis操作服务服务`
3. Cline会自动调用MCP服务，并返回结果


## 5.1 集群扩展机制详解

common_redis服务设计为可扩展的多集群管理服务，通过简单的配置文件修改即可添加和管理新的Redis集群，无需修改代码。以下是详细的扩展机制说明：

### 5.1.1 扩展原理

服务在启动时会从配置文件`servers/commonredis.properties`中读取集群列表和各集群的配置信息：

1. **集群列表定义**：通过`redis.clusters`配置项定义所有可用的Redis集群，多个集群名称以逗号分隔
2. **集群配置映射**：每个集群的配置项都以`redis.{cluster_name}.`为前缀，如`redis.main.host`、`redis.cache.port`等
3. **动态连接池创建**：服务会为每个定义的集群创建独立的Jedis连接池
4. **命令安全控制**：通过`redis.dangerous_commands`配置项定义禁用的危险命令，适用于所有集群

### 5.1.2 添加新集群的步骤

以下是添加新Redis集群的详细步骤：

1. **编辑配置文件**：打开`servers/commonredis.properties`文件
2. **更新集群列表**：在`redis.clusters`配置项中添加新集群名称，例如：
   ```properties
   # 原配置
   redis.clusters=main,cache,session

   # 添加新集群"queue"后
   redis.clusters=main,cache,session,queue
   ```
3. **添加集群基本配置**：为新集群添加必要的配置项：
   ```properties
   # queue集群别名
   redis.queue.alias=队列服务集群
   # queue集群Redis服务器地址
   redis.queue.host=queue-redis.example.com
   # queue集群Redis服务器端口
   redis.queue.port=6379
   # queue集群Redis服务器密码
   redis.queue.password=your_password
   # queue集群Redis数据库索引
   redis.queue.database=0
   # queue集群Redis连接超时时间（毫秒）
   redis.queue.timeout=2000
   ```
4. **添加连接池配置**：为新集群配置连接池参数：
   ```properties
   # queue集群Redis连接池最大连接数
   redis.queue.pool.max_total=32
   # queue集群Redis连接池最大空闲连接数
   redis.queue.pool.max_idle=16
   # queue集群Redis连接池最小空闲连接数
   redis.queue.pool.min_idle=4
   ```
5. **重启服务**：配置修改完成后，重启服务使新配置生效

### 5.1.3 配置项说明

每个Redis集群需要配置的核心参数包括：

| 配置项 | 必填 | 说明 | 示例值 |
|-------|------|------|--------|
| redis.{cluster}.alias | 是 | 集群别名，用于显示 | 队列服务集群 |
| redis.{cluster}.host | 是 | Redis服务器地址 | redis.example.com |
| redis.{cluster}.port | 是 | Redis服务器端口 | 6379 |
| redis.{cluster}.password | 否 | Redis服务器密码，无密码则留空 | password |
| redis.{cluster}.database | 是 | Redis数据库索引 | 0 |
| redis.{cluster}.timeout | 否 | 连接超时时间（毫秒） | 2000 |
| redis.{cluster}.pool.max_total | 否 | 连接池最大连接数 | 8 |
| redis.{cluster}.pool.max_idle | 否 | 连接池最大空闲连接数 | 8 |
| redis.{cluster}.pool.min_idle | 否 | 连接池最小空闲连接数 | 0 |

### 5.1.4 环境变量支持

所有配置项都支持通过环境变量进行覆盖，格式为`${ENV_VAR:default_value}`，例如：

```properties
redis.main.host=${REDIS_MAIN_HOST:localhost}
redis.main.port=${REDIS_MAIN_PORT:6379}
redis.main.password=${REDIS_MAIN_PASSWORD:}
```

这允许在不同环境（开发、测试、生产）中使用不同的Redis配置，而无需修改配置文件。

### 5.1.5 特殊配置：Redis URI支持

除了标准的host/port配置方式外，服务还支持使用Redis URI格式进行配置，特别适用于云服务提供的Redis实例：

```properties
# 使用Redis URI配置
redis.main.host=redis://:<EMAIL>:6379
```

URI格式支持包含密码、主机名和端口号，格式为：`redis://[:password@]host[:port]`

### 5.1.6 最佳实践

1. **安全性考虑**：
   - 为每个Redis实例设置强密码
   - 使用`redis.dangerous_commands`限制危险命令
   - 在生产环境中使用环境变量传递敏感信息

2. **连接池优化**：
   - 根据实际负载调整连接池参数
   - 高并发场景增加`max_total`和`max_idle`值
   - 稳定服务场景可适当增加`min_idle`值保持连接

3. **集群命名与组织**：
   - 使用有意义的集群名称，反映其用途
   - 相关功能的Redis实例可以使用命名前缀分组
   - 在配置文件中使用注释清晰标记每个集群的用途

4. **监控与维护**：
   - 定期使用`get_server_info`方法检查集群状态
   - 使用`ping_server`方法验证连接可用性
   - 监控连接池使用情况，及时调整配置

## 8. 常见问题

1. **问题**: 如何配置多集群Redis操作服务？
   **回答**: 可以通过修改`servers/commonredis.properties`文件中的相关配置项来配置服务。每个集群的配置都以`redis.{cluster_name}.`为前缀，例如`redis.main.host`、`redis.main.port`等。详细配置方法请参考"5.1 集群扩展机制详解"章节。

2. **问题**: 多集群Redis操作服务支持哪些数据类型？
   **回答**: 服务支持Redis的主要数据类型，包括字符串（String）、哈希（Hash）等。目前的实现主要关注这些基本类型的操作。

3. **问题**: 如何添加新的Redis集群？
   **回答**: 在`servers/commonredis.properties`文件中，首先更新`redis.clusters`配置项添加新集群名称，然后添加该集群的所有配置项，包括host、port、password、database等。详细步骤请参考"5.1.2 添加新集群的步骤"章节。

4. **问题**: 为什么某些Redis命令被禁用？
   **回答**: 为了保证数据安全，服务限制了一些危险命令的执行，如FLUSHALL、FLUSHDB、DEL、KEYS等。这些命令可能会导致数据丢失或性能问题。禁用的命令列表在`redis.dangerous_commands`配置项中定义。

5. **问题**: 如何处理Redis连接池配置？
   **回答**: 每个Redis集群都有独立的连接池配置，可以通过`redis.{cluster_name}.pool.max_total`、`redis.{cluster_name}.pool.max_idle`和`redis.{cluster_name}.pool.min_idle`等配置项来调整连接池的大小和行为。

6. **问题**: 如何测试Redis服务器连接？
   **回答**: 可以使用`ping_server`方法测试与Redis服务器的连接。如果连接正常，服务器会返回"PONG"响应。这是检查Redis服务器是否可用的最简单方法。

7. **问题**: 添加新集群后需要重启服务吗？
   **回答**: 是的，当修改配置文件添加新集群后，需要重启服务才能使新配置生效。服务在启动时会读取配置文件并初始化所有集群的连接池。

8. **问题**: 服务是否支持Redis集群模式？
   **回答**: 当前版本主要支持单节点Redis实例。如需连接Redis集群，可以通过连接集群的代理节点来实现，或者为不同的集群节点配置单独的连接。

9. **问题**: 如何在不同环境中使用不同的Redis配置？
   **回答**: 可以使用环境变量覆盖配置文件中的值，格式为`${ENV_VAR:default_value}`。这样可以在不同环境中使用不同的Redis配置，而无需修改配置文件。

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [开发指南](/docs/guides/development)
- [代码生成器指南](/docs/guides/code-generator)
