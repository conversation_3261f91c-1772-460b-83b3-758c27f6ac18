# 多集群MySQL数据库服务

## 1. 服务概述

- **服务ID**: common_mysql
- **版本**: 1.0.0
- **类型**: DATABASE
- **生成时间**: 2025-04-26T17:48:09.997626

提供多集群MySQL数据库操作功能，支持连接不同服务器上的数据库，仅允许执行SELECT、SHOW、DESCRIBE等查询语句，确保数据安全性的同时提供灵活的数据查询能力。通过简单的配置文件修改，可以轻松扩展管理新的MySQL集群，无需修改代码。

## 2. 功能说明

多集群MySQL数据库服务提供以下核心功能：

1. **执行SQL查询** - 在指定集群上执行SQL查询，支持SELECT、SHOW、DESCRIBE等只读操作
2. **获取集群列表** - 获取系统中配置的所有MySQL集群信息
3. **获取数据库列表** - 获取指定集群中的所有数据库列表
4. **获取表列表** - 获取指定数据库中的所有表列表

服务支持多个MySQL集群的配置，每个集群可以连接到不同的MySQL服务器，具有独立的连接池配置和默认数据库设置。为了保证数据安全，服务仅允许执行只读操作，并且SELECT查询必须包含LIMIT子句以防止返回过大的结果集。

## 3. 参数说明

### 3.1 execute_query 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | String | 是 | - | MySQL集群名称，用于指定要连接的数据库服务器 |
| sql | String | 是 | - | SQL语句，仅支持SELECT、SHOW、DESCRIBE命令，SELECT语句必须包含LIMIT子句 |
| database | String | 否 | 集群默认数据库 | 数据库名称，不指定则使用集群默认数据库 |

### 3.2 list_clusters 方法参数

此方法不需要参数。

### 3.3 list_databases 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | String | 是 | - | MySQL集群名称，用于指定要连接的数据库服务器 |

### 3.4 list_tables 方法参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|------|------|--------|------|
| cluster | String | 是 | - | MySQL集群名称，用于指定要连接的数据库服务器 |
| database | String | 是 | - | 数据库名称 |

## 4. 输出说明

### 4.1 execute_query 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| rows | List | 查询结果行，每行是一个包含列值的Map |
| columns | List | 列名列表，包含查询结果的所有列名 |
| rowCount | Integer | 查询返回的行数 |
| sql | String | 执行的SQL语句 |
| database | String | 使用的数据库名称 |
| cluster | String | 使用的MySQL集群名称 |
| success | Boolean | 操作是否成功 |
| message | String | 操作结果消息，成功时为空，失败时包含错误信息 |

### 4.2 list_clusters 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| clusters | List | 集群信息列表，每个元素包含id和alias两个字段 |
| success | Boolean | 操作是否成功 |
| message | String | 操作结果消息，成功时为空，失败时包含错误信息 |

### 4.3 list_databases 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| rows | List | 数据库列表，每行包含一个数据库名称 |
| columns | List | 列名列表，通常只有一列，如"Database" |
| rowCount | Integer | 数据库数量 |
| cluster | String | 使用的MySQL集群名称 |
| success | Boolean | 操作是否成功 |
| message | String | 操作结果消息，成功时为空，失败时包含错误信息 |

### 4.4 list_tables 方法输出

| 字段名 | 类型 | 说明 |
|-------|------|------|
| rows | List | 表列表，每行包含一个表名称 |
| columns | List | 列名列表，通常只有一列，如"Tables_in_{database}" |
| rowCount | Integer | 表数量 |
| cluster | String | 使用的MySQL集群名称 |
| database | String | 使用的数据库名称 |
| success | Boolean | 操作是否成功 |
| message | String | 操作结果消息，成功时为空，失败时包含错误信息 |

## 5. 配置说明

| 配置项 | 默认值 | 说明 |
|-------|--------|------|
| mysql.clusters | ${MYSQL_CLUSTERS:main,analytics,reporting} | MySQL集群列表，逗号分隔 |
| mysql.allowed-commands | SELECT,SHOW,DESCRIBE,DESC | 允许执行的SQL命令列表，逗号分隔 |
| mysql.pool.max-active | 10 | 连接池最大连接数（所有集群默认值） |
| mysql.pool.max-idle | 5 | 连接池最大空闲连接数（所有集群默认值） |
| mysql.pool.min-idle | 2 | 连接池最小空闲连接数（所有集群默认值） |
| mysql.pool.initial-size | 5 | 连接池初始连接数（所有集群默认值） |
| mysql.pool.max-wait | 10000 | 连接池最大等待时间（毫秒）（所有集群默认值） |
| mysql.pool.validation-query | SELECT 1 | 连接有效性检查SQL（所有集群默认值） |
| mysql.pool.test-on-borrow | true | 借用连接时检查有效性（所有集群默认值） |
| mysql.pool.test-while-idle | true | 空闲时检查连接有效性（所有集群默认值） |
| mysql.main.alias | 主数据库集群 | main集群别名 |
| mysql.main.url | ${MYSQL_MAIN_URL:***************************************************************************************} | main集群连接URL |
| mysql.main.username | ${MYSQL_MAIN_USERNAME:root} | main集群用户名 |
| mysql.main.password | ${MYSQL_MAIN_PASSWORD:password} | main集群密码 |
| mysql.main.driver-class-name | com.mysql.cj.jdbc.Driver | main集群驱动类名 |
| mysql.main.default-database | information_schema | main集群默认数据库 |
| mysql.analytics.alias | 数据分析集群 | analytics集群别名 |
| mysql.analytics.url | ${MYSQL_ANALYTICS_URL:********************************************************************************************} | analytics集群连接URL |
| mysql.analytics.username | ${MYSQL_ANALYTICS_USERNAME:analytics_user} | analytics集群用户名 |
| mysql.analytics.password | ${MYSQL_ANALYTICS_PASSWORD:password} | analytics集群密码 |
| mysql.analytics.driver-class-name | com.mysql.cj.jdbc.Driver | analytics集群驱动类名 |
| mysql.analytics.default-database | analytics_db | analytics集群默认数据库 |
| mysql.reporting.alias | 报表数据集群 | reporting集群别名 |
| mysql.reporting.url | ${MYSQL_REPORTING_URL:********************************************************************************************} | reporting集群连接URL |
| mysql.reporting.username | ${MYSQL_REPORTING_USERNAME:reporting_user} | reporting集群用户名 |
| mysql.reporting.password | ${MYSQL_REPORTING_PASSWORD:password} | reporting集群密码 |
| mysql.reporting.driver-class-name | com.mysql.cj.jdbc.Driver | reporting集群驱动类名 |
| mysql.reporting.default-database | reporting_db | reporting集群默认数据库 |

## 6. REST API接口

### 6.1 基本信息

- **路径**: /api/v1/commonmysql
- **方法**: GET
- **说明**: 提供多集群MySQL数据库操作功能，支持连接不同服务器上的数据库，仅允许执行SELECT、SHOW、DESCRIBE等查询语句

### 6.2 API参数说明


**查询参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| cluster | String | 是 | MySQL集群名称，用于指定要连接的数据库服务器 |
| sql | String | 是 | SQL语句，仅支持SELECT、SHOW、DESCRIBE命令 |
| database | String | 否 | 数据库名称 |


### 6.3 响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "rows": "示例值",
    "columns": "示例值",
    "rowCount": "示例值",
    "sql": "示例值",
    "database": "示例值",
    "cluster": "示例值",
    "success": "示例值",
    "message": "示例值"
  }
}
```

### 6.4 错误码

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1000 | 参数错误 |
| 1001 | 服务内部错误 |
| 1002 | 资源不存在 |

### 6.5 REST API调用示例

#### 6.5.1 执行SQL查询示例

```bash
# 使用curl执行SQL查询
curl -X GET "http://localhost:8080/api/v1/commonmysql?method=execute_query&cluster=main&sql=SELECT%20*%20FROM%20users%20LIMIT%2010&database=test_db" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "rows": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "created_at": "2025-01-01 00:00:00"
      },
      {
        "id": 2,
        "username": "user1",
        "email": "<EMAIL>",
        "created_at": "2025-01-02 10:30:00"
      }
    ],
    "columns": ["id", "username", "email", "created_at"],
    "rowCount": 2,
    "sql": "SELECT * FROM users LIMIT 10",
    "database": "test_db",
    "cluster": "main"
  }
}
```

#### 6.5.2 获取集群列表示例

```bash
# 使用curl获取集群列表
curl -X GET "http://localhost:8080/api/v1/commonmysql?method=list_clusters" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "clusters": [
      {
        "id": "main",
        "alias": "主数据库集群"
      },
      {
        "id": "analytics",
        "alias": "数据分析集群"
      },
      {
        "id": "reporting",
        "alias": "报表数据集群"
      }
    ]
  }
}
```

#### 6.5.3 获取数据库列表示例

```bash
# 使用curl获取数据库列表
curl -X GET "http://localhost:8080/api/v1/commonmysql?method=list_databases&cluster=main" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "rows": [
      {"Database": "information_schema"},
      {"Database": "mysql"},
      {"Database": "performance_schema"},
      {"Database": "sys"},
      {"Database": "test_db"}
    ],
    "columns": ["Database"],
    "rowCount": 5,
    "cluster": "main"
  }
}
```

#### 6.5.4 获取表列表示例

```bash
# 使用curl获取表列表
curl -X GET "http://localhost:8080/api/v1/commonmysql?method=list_tables&cluster=main&database=test_db" \
  -H "Content-Type: application/json"
```

响应：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "rows": [
      {"Tables_in_test_db": "users"},
      {"Tables_in_test_db": "products"},
      {"Tables_in_test_db": "orders"}
    ],
    "columns": ["Tables_in_test_db"],
    "rowCount": 3,
    "cluster": "main",
    "database": "test_db"
  }
}
```


## 7. MCP协议接口

### 7.1 统一SSE端点

MCP平台提供了统一的SSE端点，用于暴露所有可用的McpServer：

- **URL**: `/mcp/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.2 单独SSE端点

每个MCP服务也提供了单独的SSE端点：

- **URL**: `/mcp/common_mysql/sse`
- **方法**: GET
- **内容类型**: text/event-stream

### 7.3 工具信息

- **工具ID**: `common_mysql`
- **描述**: `提供多集群MySQL数据库操作功能，支持连接不同服务器上的数据库，仅允许执行SELECT、SHOW、DESCRIBE等查询语句`
- **方法**:
  - **execute_query**: 执行SQL查询
    - **cluster**: MySQL集群名称，用于指定要连接的数据库服务器 (string, 必填)
    - **sql**: SQL语句，仅支持SELECT、SHOW、DESCRIBE命令 (string, 必填)
    - **database**: 数据库名称，不指定则使用集群默认数据库 (string, 可选)
  - **list_clusters**: 获取可用的MySQL集群列表
    - 无参数
  - **list_databases**: 获取指定集群的数据库列表
    - **cluster**: MySQL集群名称 (string, 必填)
  - **list_tables**: 获取指定数据库的表列表
    - **cluster**: MySQL集群名称 (string, 必填)
    - **database**: 数据库名称 (string, 必填)

### 7.4 MCP协议调用示例

#### 7.4.1 执行SQL查询示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/common_mysql/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 执行SQL查询
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "execute_query",
        params: {
            "cluster": "main",
            "sql": "SELECT * FROM users LIMIT 10",
            "database": "test_db"
        }
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "success": true,
    //   "rows": [
    //     {
    //       "id": 1,
    //       "username": "admin",
    //       "email": "<EMAIL>",
    //       "created_at": "2025-01-01 00:00:00"
    //     },
    //     ...
    //   ],
    //   "columns": ["id", "username", "email", "created_at"],
    //   "rowCount": 10,
    //   "sql": "SELECT * FROM users LIMIT 10",
    //   "database": "test_db",
    //   "cluster": "main"
    // }
});
```

#### 7.4.2 获取集群列表示例

```javascript
// 假设已经建立了SSE连接
const eventSource = new EventSource('/mcp/common_mysql/sse');

// 监听工具列表事件
eventSource.addEventListener('tools_available', function(event) {
    const data = JSON.parse(event.data);
    console.log('可用工具:', data.tools);

    // 获取集群列表
    const requestId = generateUUID();
    const request = {
        jsonrpc: "2.0",
        id: requestId,
        method: "list_clusters",
        params: {}
    };

    // 发送请求到服务器
    fetch('/mcp/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
    });
});

// 监听工具执行结果
eventSource.addEventListener('response', function(event) {
    const response = JSON.parse(event.data);
    console.log('执行结果:', response.result);
    // 执行结果示例:
    // {
    //   "success": true,
    //   "clusters": [
    //     {
    //       "id": "main",
    //       "alias": "主数据库集群"
    //     },
    //     {
    //       "id": "analytics",
    //       "alias": "数据分析集群"
    //     },
    //     {
    //       "id": "reporting",
    //       "alias": "报表数据集群"
    //     }
    //   ]
    // }
});
```

### 7.5 在Cline中配置和使用

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL：
   - 使用统一端点：`http://localhost:8081/mcp/sse`（可访问所有服务）
   - 使用单独端点：`http://localhost:8081/mcp/commonmysql/sse`（仅访问多集群MySQL数据库服务服务）
5. 点击"Save"按钮

使用MCP服务：
1. 在Cline中创建新的聊天
2. 输入需要使用多集群MySQL数据库服务服务的请求，例如：`使用多集群MySQL数据库服务服务`
3. Cline会自动调用MCP服务，并返回结果


## 5.1 集群扩展机制详解

common_mysql服务设计为可扩展的多集群管理服务，通过简单的配置文件修改即可添加和管理新的MySQL集群，无需修改代码。以下是详细的扩展机制说明：

### 5.1.1 扩展原理

服务在启动时会从配置文件`servers/commonmysql.properties`中读取集群列表和各集群的配置信息：

1. **集群列表定义**：通过`mysql.clusters`配置项定义所有可用的MySQL集群，多个集群名称以逗号分隔
2. **集群配置映射**：每个集群的配置项都以`mysql.{cluster_name}.`为前缀，如`mysql.main.url`、`mysql.analytics.username`等
3. **动态连接池创建**：服务会为每个定义的集群创建独立的数据源和连接池
4. **默认值继承**：如果某个集群的特定配置项未定义，会使用全局默认配置

### 5.1.2 添加新集群的步骤

以下是添加新MySQL集群的详细步骤：

1. **编辑配置文件**：打开`servers/commonmysql.properties`文件
2. **更新集群列表**：在`mysql.clusters`配置项中添加新集群名称，例如：
   ```properties
   # 原配置
   mysql.clusters=main,analytics,reporting

   # 添加新集群"warehouse"后
   mysql.clusters=main,analytics,reporting,warehouse
   ```
3. **添加集群基本配置**：为新集群添加必要的配置项：
   ```properties
   # warehouse集群别名
   mysql.warehouse.alias=仓库数据集群
   # warehouse集群连接URL
   mysql.warehouse.url=***********************************************************************************************************
   # warehouse集群用户名
   mysql.warehouse.username=warehouse_user
   # warehouse集群密码
   mysql.warehouse.password=password
   # warehouse集群驱动类名
   mysql.warehouse.driver-class-name=com.mysql.cj.jdbc.Driver
   # warehouse集群默认数据库
   mysql.warehouse.default-database=warehouse_db
   ```
4. **添加连接池配置（可选）**：如果需要为新集群配置特定的连接池参数，可以添加以下配置：
   ```properties
   # warehouse集群连接池最大连接数
   mysql.warehouse.pool.max-active=20
   # warehouse集群连接池最大空闲连接数
   mysql.warehouse.pool.max-idle=10
   # warehouse集群连接池最小空闲连接数
   mysql.warehouse.pool.min-idle=5
   # warehouse集群连接池初始连接数
   mysql.warehouse.pool.initial-size=10
   ```
5. **重启服务**：配置修改完成后，重启服务使新配置生效

### 5.1.3 配置项说明

每个MySQL集群需要配置的核心参数包括：

| 配置项 | 必填 | 说明 | 示例值 |
|-------|------|------|--------|
| mysql.{cluster}.alias | 是 | 集群别名，用于显示 | 仓库数据集群 |
| mysql.{cluster}.url | 是 | JDBC连接URL | ************************************************************************ |
| mysql.{cluster}.username | 是 | 数据库用户名 | db_user |
| mysql.{cluster}.password | 是 | 数据库密码 | password |
| mysql.{cluster}.driver-class-name | 是 | JDBC驱动类名 | com.mysql.cj.jdbc.Driver |
| mysql.{cluster}.default-database | 是 | 默认数据库名 | information_schema |
| mysql.{cluster}.pool.max-active | 否 | 连接池最大连接数 | 10 |
| mysql.{cluster}.pool.max-idle | 否 | 连接池最大空闲连接数 | 5 |
| mysql.{cluster}.pool.min-idle | 否 | 连接池最小空闲连接数 | 2 |
| mysql.{cluster}.pool.initial-size | 否 | 连接池初始连接数 | 5 |
| mysql.{cluster}.pool.max-wait | 否 | 连接池最大等待时间(毫秒) | 10000 |

### 5.1.4 环境变量支持

所有配置项都支持通过环境变量进行覆盖，格式为`${ENV_VAR:default_value}`，例如：

```properties
mysql.main.url=${MYSQL_MAIN_URL:***************************************************************************************}
```

这允许在不同环境（开发、测试、生产）中使用不同的数据库配置，而无需修改配置文件。

### 5.1.5 最佳实践

1. **安全性考虑**：为每个集群使用只读权限的数据库用户，特别是在生产环境中
2. **连接池优化**：根据实际负载调整连接池参数，避免资源浪费或连接不足
3. **集群命名**：使用有意义的集群名称，便于识别和管理
4. **配置分离**：在不同环境中使用环境变量覆盖敏感配置，如密码
5. **定期检查**：定期使用`list_clusters`方法检查集群连接状态

## 8. 常见问题

1. **问题**: 如何配置多集群MySQL数据库服务？
   **回答**: 可以通过修改`servers/commonmysql.properties`文件中的相关配置项来配置服务。每个集群的配置都以`mysql.{cluster_name}.`为前缀，例如`mysql.main.url`、`mysql.main.username`等。详细配置方法请参考"5.1 集群扩展机制详解"章节。

2. **问题**: 多集群MySQL数据库服务支持哪些SQL命令？
   **回答**: 服务仅支持只读操作，包括SELECT、SHOW、DESCRIBE、DESC等命令。这些允许的命令在`mysql.allowed-commands`配置项中定义。

3. **问题**: 为什么SELECT查询必须包含LIMIT子句？
   **回答**: 为了防止返回过大的结果集导致性能问题，服务要求所有SELECT查询必须包含LIMIT子句来限制返回的行数。

4. **问题**: 如何添加新的MySQL集群？
   **回答**: 在`servers/commonmysql.properties`文件中，首先更新`mysql.clusters`配置项添加新集群名称，然后添加该集群的所有配置项，包括url、username、password、driver-class-name等。详细步骤请参考"5.1.2 添加新集群的步骤"章节。

5. **问题**: 如何查看可用的MySQL集群列表？
   **回答**: 可以使用`list_clusters`方法获取所有可用的MySQL集群列表，包括集群ID和别名。

6. **问题**: 如何处理SQL注入风险？
   **回答**: 服务通过限制允许的SQL命令类型（仅允许只读操作）和验证SQL语句类型来降低风险。在生产环境中，应该进一步限制数据库用户的权限，只授予只读权限。

7. **问题**: 添加新集群后需要重启服务吗？
   **回答**: 是的，当修改配置文件添加新集群后，需要重启服务才能使新配置生效。服务在启动时会读取配置文件并初始化所有集群的连接池。

8. **问题**: 如何处理不同环境（开发、测试、生产）的集群配置？
   **回答**: 可以使用环境变量覆盖配置文件中的值，格式为`${ENV_VAR:default_value}`。这样可以在不同环境中使用不同的数据库配置，而无需修改配置文件。

## 9. 更多信息

- [MCP平台文档中心](/docs)
- [REST API文档](/docs/api/rest-api)
- [MCP协议文档](/docs/api/mcp-protocol)
- [开发指南](/docs/guides/development)
- [代码生成器指南](/docs/guides/code-generator)
