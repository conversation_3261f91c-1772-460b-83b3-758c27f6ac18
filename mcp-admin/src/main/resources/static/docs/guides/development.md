# MCP平台开发指南

## 1. 概述

本文档提供了MCP平台的开发指南，包括环境搭建、项目结构、服务开发、API开发、测试和部署等内容。通过本指南，您可以快速上手MCP平台的开发工作，创建自己的服务和功能。

## 2. 开发环境搭建

### 2.1 开发工具

推荐使用以下开发工具：

- **IDE**：IntelliJ IDEA（推荐）、Eclipse、VS Code
- **构建工具**：Maven 3.8.0+
- **版本控制**：Git
- **API测试**：Postman、curl
- **数据库工具**：MySQL Workbench、Redis Desktop Manager

### 2.2 环境配置

#### 2.2.1 Java环境

安装JDK 17或更高版本，并配置环境变量：

```bash
# 检查Java版本
java -version

# 配置JAVA_HOME环境变量
export JAVA_HOME=/path/to/jdk
export PATH=$JAVA_HOME/bin:$PATH
```

#### 2.2.2 Maven配置

安装Maven 3.8.0或更高版本，并配置环境变量：

```bash
# 检查Maven版本
mvn -version

# 配置Maven环境变量
export M2_HOME=/path/to/maven
export PATH=$M2_HOME/bin:$PATH
```

#### 2.2.3 IDE配置

以IntelliJ IDEA为例，进行以下配置：

1. 安装Lombok插件
2. 启用注解处理
3. 配置Maven设置
4. 配置Java编译器版本为17
5. 配置文件编码为UTF-8

### 2.3 获取源码

从GitHub克隆源码：

```bash
git clone https://github.com/your-organization/mcp-platform.git
cd mcp-platform
```

### 2.4 导入项目

以IntelliJ IDEA为例，导入项目：

1. 选择"Open"或"Import Project"
2. 选择项目根目录
3. 选择"Import project from external model" > "Maven"
4. 勾选"Import Maven projects automatically"
5. 点击"Finish"

### 2.5 编译项目

使用Maven编译项目：

```bash
mvn clean compile
```

## 3. 项目结构

MCP平台采用模块化设计，主要包含以下模块：

### 3.1 mcp-core

核心模块，定义了MCP平台的基础接口和抽象类：

```
mcp-core/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── mcp/
│   │   │           └── core/
│   │   │               ├── McpServer.java              # 服务接口
│   │   │               ├── AbstractMcpServer.java      # 服务抽象类
│   │   │               ├── McpServerRegistry.java      # 服务注册表
│   │   │               ├── McpServerManager.java       # 服务管理器
│   │   │               ├── McpServerStatus.java        # 服务状态枚举
│   │   │               └── protocol/                   # 协议相关类
│   │   └── resources/
│   └── test/
└── pom.xml
```

### 3.2 mcp-common

通用模块，提供了工具类、常量和通用配置：

```
mcp-common/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── mcp/
│   │   │           └── common/
│   │   │               ├── utils/                      # 工具类
│   │   │               ├── constants/                  # 常量定义
│   │   │               └── config/                     # 通用配置
│   │   └── resources/
│   └── test/
└── pom.xml
```

### 3.3 mcp-server

Web服务器模块，提供了HTTP API接口：

```
mcp-server/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── mcp/
│   │   │           └── server/
│   │   │               ├── McpServerApplication.java   # 应用入口
│   │   │               ├── config/                     # 配置类
│   │   │               ├── controller/                 # 控制器
│   │   │               ├── service/                    # 服务类
│   │   │               └── servers/                    # 具体服务实现
│   │   └── resources/
│   │       ├── application.properties                  # 主配置文件
│   │       └── servers/                                # 服务配置文件
│   └── test/
└── pom.xml
```

### 3.4 mcp-admin

管理后台模块，提供了Web界面管理MCP服务：

```
mcp-admin/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── mcp/
│   │   │           └── admin/
│   │   │               ├── McpPlatformApplication.java # 应用入口
│   │   │               ├── config/                     # 配置类
│   │   │               ├── controller/                 # 控制器
│   │   │               ├── service/                    # 服务类
│   │   │               └── client/                     # 客户端
│   │   └── resources/
│   │       ├── application.properties                  # 主配置文件
│   │       ├── static/                                 # 静态资源
│   │       └── templates/                              # 模板文件
│   └── test/
└── pom.xml
```

### 3.5 mcp-generator

代码生成器模块，用于生成服务代码模板：

```
mcp-generator/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── mcp/
│   │   │           └── generator/
│   │   │               ├── McpGeneratorApplication.java # 应用入口
│   │   │               ├── config/                      # 配置类
│   │   │               ├── service/                     # 服务类
│   │   │               └── template/                    # 模板文件
│   │   └── resources/
│   └── test/
└── pom.xml
```

## 4. 服务开发

### 4.1 创建新服务

#### 4.1.1 使用代码生成器

MCP平台提供了代码生成器，可以快速创建新服务：

```bash
cd mcp-generator
mvn spring-boot:run -Dspring-boot.run.arguments="--service=myservice --description='My Custom Service' --version=1.0.0"
```

#### 4.1.2 手动创建

如果您想手动创建服务，可以按照以下步骤进行：

1. 在`mcp-server/src/main/java/com/mcp/server/servers`目录下创建新的包
2. 创建服务实现类，继承`AbstractMcpServer`
3. 在`mcp-server/src/main/resources/servers`目录下创建配置文件
4. 创建单元测试类

### 4.2 服务实现

以下是一个简单的服务实现示例：

```java
package com.jdl.mcp.server.servers.myservice;

import com.jdl.mcp.core.AbstractMcpServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Component
public class MyServiceMcpServer extends AbstractMcpServer {

    private static final Logger logger = LoggerFactory.getLogger(MyServiceMcpServer.class);
    
    // 配置文件路径
    private static final String CONFIG_PATH = "servers/myservice.properties";
    
    // 业务配置属性
    private String customProperty;
    
    /**
     * 构造函数
     */
    public MyServiceMcpServer() {
        super("myservice", "My Custom Service", "1.0.0");
        setType("API");
        setUsage("自定义服务，参数：param1 - 参数1，param2 - 参数2（可选）");
        
        // 加载配置文件
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        Properties props = loadProperties(CONFIG_PATH);
        
        // 读取配置项
        customProperty = props.getProperty("myservice.custom.property", "default_value");
        
        logger.info("成功加载自定义服务配置文件: {}", CONFIG_PATH);
    }
    
    @Override
    protected Object doExecute(Map<String, Object> params) {
        // 参数验证
        if (params == null) {
            throw new IllegalArgumentException("参数错误: params不能为空");
        }
        
        // 验证必填参数
        if (!params.containsKey("param1")) {
            throw new IllegalArgumentException("参数错误: param1参数不能为空");
        }
        String param1 = (String) params.get("param1");
        
        // 可选参数
        String param2 = params.containsKey("param2") ? (String) params.get("param2") : null;
        
        try {
            logger.info("执行服务: myservice, 参数: {}", params);
            
            // TODO: 实现服务逻辑
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("param1", param1);
            if (param2 != null) {
                result.put("param2", param2);
            }
            result.put("customProperty", customProperty);
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("服务执行成功: {}", result);
            return result;
            
        } catch (Exception e) {
            logger.error("服务执行失败: {}", e.getMessage(), e);
            throw new RuntimeException("服务执行失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Map<String, Object> getParametersDescription() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        Map<String, Object> param1Property = new HashMap<>();
        param1Property.put("type", "string");
        param1Property.put("description", "参数1");
        properties.put("param1", param1Property);
        
        Map<String, Object> param2Property = new HashMap<>();
        param2Property.put("type", "string");
        param2Property.put("description", "参数2（可选）");
        properties.put("param2", param2Property);
        
        schema.put("properties", properties);
        
        return schema;
    }
    
    @Override
    public Map<String, Object> getResultDescription() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        Map<String, Object> param1Property = new HashMap<>();
        param1Property.put("type", "string");
        param1Property.put("description", "参数1");
        properties.put("param1", param1Property);
        
        Map<String, Object> param2Property = new HashMap<>();
        param2Property.put("type", "string");
        param2Property.put("description", "参数2");
        properties.put("param2", param2Property);
        
        Map<String, Object> customPropertyProperty = new HashMap<>();
        customPropertyProperty.put("type", "string");
        customPropertyProperty.put("description", "自定义属性");
        properties.put("customProperty", customPropertyProperty);
        
        Map<String, Object> timestampProperty = new HashMap<>();
        timestampProperty.put("type", "integer");
        timestampProperty.put("description", "时间戳");
        properties.put("timestamp", timestampProperty);
        
        schema.put("properties", properties);
        
        return schema;
    }
}
```

### 4.3 配置文件

为服务创建配置文件`mcp-server/src/main/resources/servers/myservice.properties`：

```properties
# 自定义服务配置

# 自定义属性
myservice.custom.property=custom_value

# 其他配置项
myservice.option1=value1
myservice.option2=value2
```

### 4.4 单元测试

为服务创建单元测试类：

```java
package com.jdl.mcp.server.servers.myservice;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class MyServiceMcpServerTest {

    private MyServiceMcpServer myServiceMcpServer;

    @BeforeEach
    public void setUp() {
        myServiceMcpServer = new MyServiceMcpServer();
    }

    @Test
    public void testExecute_Success() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        params.put("param1", "value1");
        params.put("param2", "value2");

        // 执行测试
        Object result = myServiceMcpServer.execute(params);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> resultMap = (Map<String, Object>) result;
        
        assertEquals("value1", resultMap.get("param1"));
        assertEquals("value2", resultMap.get("param2"));
        assertNotNull(resultMap.get("customProperty"));
        assertNotNull(resultMap.get("timestamp"));
    }

    @Test
    public void testExecute_MissingParam1() {
        // 准备测试数据
        Map<String, Object> params = new HashMap<>();
        // 缺少param1参数

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> myServiceMcpServer.execute(params)
        );

        // 验证异常消息
        assertTrue(exception.getMessage().contains("param1参数不能为空"));
    }

    @Test
    public void testExecute_NullParams() {
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> myServiceMcpServer.execute(null)
        );

        // 验证异常消息
        assertTrue(exception.getMessage().contains("params不能为空"));
    }
}
```

## 5. API开发

### 5.1 REST API

MCP平台支持REST API，您可以为服务创建REST API控制器：

```java
package com.jdl.mcp.server.controller.api;

import com.jdl.mcp.core.McpServer;
import com.jdl.mcp.core.McpServerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/myservice")
public class MyServiceController {

    @Autowired
    private McpServerRegistry mcpServerRegistry;

    @PostMapping
    public ResponseEntity<Map<String, Object>> execute(@RequestBody Map<String, Object> params) {
        try {
            // 获取服务
            McpServer server = mcpServerRegistry.getServer("myservice");
            if (server == null) {
                return ResponseEntity.badRequest().body(createErrorResponse("服务不存在"));
            }

            // 执行服务
            Object result = server.execute(params);

            // 返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "操作成功");
            response.put("data", result);
            response.put("code", 0);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }

    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("data", null);
        response.put("code", -1);
        return response;
    }
}
```

### 5.2 MCP协议

MCP协议由`McpToolsController`自动处理，无需为每个服务单独创建控制器。

## 6. 前端开发

### 6.1 服务详情页面

为服务创建详情页面`mcp-admin/src/main/resources/templates/servers/myservice.html`：

```html
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/default :: html}">
<head>
    <title>自定义服务</title>
    <th:block id="pageTitle">自定义服务</th:block>
    <th:block id="pageStyles">
        <style>
            .form-group {
                margin-bottom: 1rem;
            }
            .result-container {
                margin-top: 2rem;
                padding: 1rem;
                background-color: #f8f9fa;
                border-radius: 0.25rem;
            }
            pre {
                margin: 0;
            }
        </style>
    </th:block>
</head>
<body>
    <div id="pageContent">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">自定义服务</h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h3>服务信息</h3>
                        <table class="table table-bordered">
                            <tr>
                                <th>ID</th>
                                <td th:text="${server.id}">myservice</td>
                            </tr>
                            <tr>
                                <th>名称</th>
                                <td th:text="${server.name}">自定义服务</td>
                            </tr>
                            <tr>
                                <th>描述</th>
                                <td th:text="${server.description}">自定义服务描述</td>
                            </tr>
                            <tr>
                                <th>版本</th>
                                <td th:text="${server.version}">1.0.0</td>
                            </tr>
                            <tr>
                                <th>类型</th>
                                <td th:text="${server.type}">API</td>
                            </tr>
                            <tr>
                                <th>状态</th>
                                <td>
                                    <span th:if="${server.running}" class="badge bg-success">运行中</span>
                                    <span th:unless="${server.running}" class="badge bg-danger">已停止</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h3>服务测试</h3>
                        <form id="serviceForm">
                            <div class="form-group">
                                <label for="param1">参数1</label>
                                <input type="text" class="form-control" id="param1" name="param1" required>
                            </div>
                            <div class="form-group">
                                <label for="param2">参数2（可选）</label>
                                <input type="text" class="form-control" id="param2" name="param2">
                            </div>
                            <button type="submit" class="btn btn-primary">执行</button>
                        </form>
                        
                        <div class="result-container" id="resultContainer" style="display: none;">
                            <h4>执行结果</h4>
                            <pre id="resultJson"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <th:block id="pageScripts">
        <script>
            $(function() {
                // 表单提交
                $('#serviceForm').on('submit', function(e) {
                    e.preventDefault();
                    
                    // 获取表单数据
                    const param1 = $('#param1').val();
                    const param2 = $('#param2').val();
                    
                    // 构建请求参数
                    const params = {
                        param1: param1
                    };
                    
                    if (param2) {
                        params.param2 = param2;
                    }
                    
                    // 发送请求
                    $.ajax({
                        url: '/api/myservice',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(params),
                        success: function(response) {
                            // 显示结果
                            $('#resultJson').text(JSON.stringify(response, null, 2));
                            $('#resultContainer').show();
                        },
                        error: function(xhr) {
                            // 显示错误
                            let errorMessage = '请求失败';
                            try {
                                const response = JSON.parse(xhr.responseText);
                                errorMessage = response.message || errorMessage;
                            } catch (e) {
                                console.error(e);
                            }
                            
                            $('#resultJson').text(JSON.stringify({ error: errorMessage }, null, 2));
                            $('#resultContainer').show();
                        }
                    });
                });
            });
        </script>
    </th:block>
</body>
</html>
```

## 7. 测试

### 7.1 单元测试

使用JUnit 5编写单元测试：

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=MyServiceMcpServerTest

# 运行特定测试方法
mvn test -Dtest=MyServiceMcpServerTest#testExecute_Success
```

### 7.2 集成测试

使用Spring Boot Test编写集成测试：

```java
@SpringBootTest
@AutoConfigureMockMvc
public class MyServiceControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testExecute_Success() throws Exception {
        // 准备测试数据
        String requestBody = "{\"param1\":\"value1\",\"param2\":\"value2\"}";

        // 执行测试
        mockMvc.perform(post("/api/myservice")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.param1").value("value1"))
                .andExpect(jsonPath("$.data.param2").value("value2"));
    }

    @Test
    public void testExecute_MissingParam1() throws Exception {
        // 准备测试数据
        String requestBody = "{\"param2\":\"value2\"}";

        // 执行测试
        mockMvc.perform(post("/api/myservice")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(containsString("param1参数不能为空")));
    }
}
```

### 7.3 API测试

使用Postman或curl测试API：

```bash
# 使用curl测试REST API
curl -X POST "http://localhost:8081/api/myservice" \
     -H "Content-Type: application/json" \
     -d '{"param1":"value1","param2":"value2"}'

# 使用curl测试MCP协议
curl -X POST "http://localhost:8081/api/mcp/tools/myservice/execute" \
     -H "Content-Type: application/json" \
     -d '{"parameters":{"param1":"value1","param2":"value2"}}'
```

## 8. 部署

### 8.1 打包

使用Maven打包项目：

```bash
mvn clean package
```

### 8.2 运行

运行打包后的JAR文件：

```bash
# 运行管理后台
java -jar mcp-admin/target/mcp-admin-0.0.1-SNAPSHOT.jar

# 运行服务器（如果需要单独运行）
java -jar mcp-server/target/mcp-server-0.0.1-SNAPSHOT.jar
```

### 8.3 Docker部署

使用Docker部署：

```bash
# 构建Docker镜像
docker build -t mcp-admin:latest -f mcp-admin/Dockerfile .

# 运行Docker容器
docker run -d -p 8081:8081 --name mcp-admin mcp-admin:latest
```

## 9. 最佳实践

### 9.1 代码规范

- 遵循Java编码规范
- 使用统一的命名约定
- 添加适当的注释和文档
- 使用lombok简化代码

### 9.2 异常处理

- 使用自定义异常类
- 在控制器层统一处理异常
- 提供友好的错误消息
- 记录详细的错误日志

### 9.3 日志记录

- 使用SLF4J和Logback记录日志
- 使用不同的日志级别（DEBUG, INFO, WARN, ERROR）
- 记录关键操作和异常
- 避免记录敏感信息

### 9.4 安全性

- 验证和清理输入数据
- 使用HTTPS保护通信
- 实现适当的认证和授权
- 保护敏感配置信息

### 9.5 性能优化

- 使用缓存减少重复计算
- 优化数据库查询
- 使用异步处理长时间运行的任务
- 监控和调优JVM参数
