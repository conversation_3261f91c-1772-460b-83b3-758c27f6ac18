# MCP代码生成器使用指南

## 1. 概述

MCP代码生成器是一个用于快速生成MCP服务及相关组件的工具，它可以帮助开发人员快速创建新的MCP服务，并自动生成相关的API控制器、MCP协议控制器、配置文件、单元测试和文档。

本文档提供了MCP代码生成器的详细使用说明，包括配置、使用、生成文件清单和扩展等方面的内容。

## 2. 功能特点

- **完整的服务生成**：生成MCP服务类、API控制器、MCP协议控制器和配置文件
- **双重接口支持**：同时生成REST API和MCP协议接口，支持不同的调用方式
- **参数验证自动生成**：根据配置文件自动生成参数验证代码
- **业务逻辑框架**：提供业务逻辑框架，开发者只需实现核心业务逻辑
- **配置文件管理**：生成服务配置文件，支持从配置文件加载参数
- **文档自动生成**：生成服务文档、API文档和MCP协议文档
- **JSON配置支持**：使用JSON格式的配置文件，更易于编写和维护

## 3. 安装与配置

### 3.1 安装

MCP代码生成器是MCP平台的一个模块，通常不需要单独安装。如果需要单独使用，可以按照以下步骤进行安装：

1. 克隆MCP平台代码库
2. 进入mcp-generator目录
3. 使用Maven编译打包

```bash
git clone https://github.com/your-org/mcp-platform.git
cd mcp-platform/mcp-generator
mvn clean package
```

### 3.2 配置文件格式

MCP代码生成器使用JSON格式的配置文件来定义服务的各种属性。配置文件包含以下几个主要部分：

- **server**: 服务基本信息，包括id、name、description、version和type等
- **params**: 服务参数定义，包括参数名称、类型、是否必填、描述和示例等
- **output**: 服务输出定义，包括输出字段的名称、类型和描述等
- **api**: API控制器配置，包括路径、标签和描述等
- **business**: 业务配置，包括业务类型和其他业务相关配置

## 4. 使用方法

### 4.1 创建配置文件

首先，需要创建一个 JSON 格式的配置文件，定义服务的各种属性。以下是一个示例配置文件：

```json
{
  "server": {
    "id": "example",
    "name": "示例服务",
    "description": "这是一个示例服务",
    "version": "1.0.0",
    "type": "API"
  },
  "params": [
    {
      "name": "name",
      "type": "String",
      "required": true,
      "description": "名称参数",
      "example": "World"
    }
  ],
  "output": {
    "type": "Map",
    "fields": [
      {
        "name": "message",
        "type": "String",
        "description": "返回消息"
      }
    ]
  },
  "api": {
    "enabled": true,
    "path": "/api/v1/example",
    "tag": "示例服务",
    "description": "提供示例功能"
  },
  "business": {
    "type": "API",
    "properties": [
      {
        "key": "example.greeting",
        "value": "Hello, World!",
        "description": "问候语"
      }
    ]
  }
}
```

### 4.2 生成代码

使用以下命令生成MCP服务及相关组件：

```bash
# 使用增强版生成器（推荐）
java -jar mcp-generator.jar generate-enhanced -c example-config.yml

# 或者使用Maven运行
cd mcp-generator
mvn spring-boot:run -Dspring-boot.run.arguments="generate-enhanced -c example-config.yml"
```

### 4.2.1 创建配置文件模板

可以使用以下命令创建配置文件模板：

```bash
# 创建标准模板
java -jar mcp-generator.jar create-template -o example-config.yml

# 创建增强版模板（推荐）
java -jar mcp-generator.jar create-template -o example-config.yml --enhanced
```

### 4.3 实现业务逻辑

生成代码后，需要实现服务的业务逻辑。主要需要修改的是 `executeBusinessLogic` 方法：

```java
protected Map<String, Object> executeBusinessLogic(Map<String, Object> params) {
    Map<String, Object> result = new HashMap<>();

    // 获取参数
    String name = (String) params.get("name");

    // 实现业务逻辑
    result.put("message", "Hello, " + name + "!");

    return result;
}
```

## 5. 生成文件清单

代码生成器会生成以下文件：

### 5.1 服务实现类

```
mcp-server/src/main/java/com/mcp/server/servers/{serviceId}/{ServiceId}McpServer.java
```

该文件包含服务的核心实现，包括参数验证、业务逻辑、参数描述和结果描述等。

### 5.2 API控制器

```
mcp-admin/src/main/java/com/mcp/admin/controller/api/{ServiceId}ApiController.java
```

该文件包含 REST API 控制器，提供 HTTP 接口供外部系统调用。

### 5.3 MCP协议控制器

```
mcp-admin/src/main/java/com/mcp/admin/controller/mcp/{ServiceId}McpServerMcpProtocolController.java
```

该文件包含 MCP 协议控制器，提供 MCP 协议接口供 Cline 等工具调用。

### 5.4 配置文件

```
mcp-admin/src/main/resources/servers/{serviceId}.properties
```

该文件包含服务的配置项，可以在运行时修改配置而不需要重新编译代码。

## 6. 配置文件详解

### 6.1 服务基本信息

```yaml
# 服务基本信息
server:
  # 服务ID，必填，用于唯一标识服务
  id: "example"

  # 服务名称，必填，用于显示
  name: "示例服务"

  # 服务描述，选填
  description: "这是一个示例服务"

  # 服务版本，选填，默认为1.0.0
  version: "1.0.0"

  # 服务类型，选填，默认为API
  type: "API"
```

服务类型支持以下几种：
- API：通用API服务
- DATABASE：数据库服务，如MySQL服务
- CACHE：缓存服务，如Redis服务

### 6.2 服务参数

```yaml
# 服务参数
params:
  # 参数列表，用于定义服务接受的参数
  - name: "name"         # 参数名称，必填
    type: "String"       # 参数类型，必填
    required: true        # 是否必填，选填，默认为false
    description: "名称参数" # 参数描述，选填
    example: "World"      # 参数示例，选填
```

参数类型支持以下几种：
- String：字符串
- Integer：整数
- Double：浮点数
- Boolean：布尔值
- Array：数组
- Map：映射

### 6.3 服务输出

```yaml
# 服务输出
output:
  # 输出类型，必填
  type: "Map"

  # 输出字段列表，选填
  fields:
    - name: "message"     # 字段名称，必填
      type: "String"     # 字段类型，必填
      description: "返回消息" # 字段描述，选填
```

输出类型支持以下几种：
- String：字符串
- Integer：整数
- Double：浮点数
- Boolean：布尔值
- Array：数组
- Map：映射

### 6.4 API控制器配置

```yaml
# API控制器配置
api:
  # 是否启用API控制器，选填，默认为true
  enabled: true

  # API路径，选填，默认为/api/v1/{serviceId}
  path: "/api/v1/example"

  # API标签，选填，默认为服务名称
  tag: "示例服务"

  # API描述，选填，默认为服务描述
  description: "提供示例功能"
```

### 6.5 业务配置

```yaml
# 业务配置
business:
  # 业务类型，选填，默认为服务类型
  type: "API"

  # 配置项列表，选填
  properties:
    - key: "example.greeting" # 配置项键，必填
      value: "Hello, World!" # 配置项值，必填
      description: "问候语" # 配置项描述，选填
```

### 6.6 支持的服务类型

MCP代码生成器支持以下服务类型：

- **API**：通用API服务，提供REST API和MCP协议接口
- **DATABASE**：数据库服务，如MySQL服务，提供数据库操作功能
- **CACHE**：缓存服务，如Redis服务，提供缓存操作功能
- **RPC**：远程过程调用服务，如JSF服务，提供远程服务调用功能
- **FILE**：文件服务，提供文件操作功能
- **NETWORK**：网络服务，提供网络操作功能

所有服务类型都同时支持REST API和MCP协议端点，可以通过HTTP请求直接调用，也可以集成到大模型中作为工具使用。

## 7. 业务扩展说明

### 7.1 服务实现类结构

生成的MCP服务类包含以下几个主要部分：

1. **构造函数**：初始化服务的基本信息，如ID、名称、版本和类型等
2. **validateParameters 方法**：验证输入参数，确保必填参数存在且类型正确
3. **executeBusinessLogic 方法**：实现具体的业务逻辑，这是开发人员需要实现的主要方法
4. **getParametersDescription 方法**：返回参数描述，用于生成MCP协议接口的参数架构
5. **getResultDescription 方法**：返回结果描述，用于生成MCP协议接口的结果架构

### 7.2 实现业务逻辑

开发人员主要需要实现 `doExecute` 方法，该方法在服务被调用时执行，用于实现具体的业务逻辑。以下是一个示例：

```java
@Component
public class ExampleMcpServer extends AbstractMcpServer {

    @Value("${example.greeting:Hello, World!}")
    private String greeting;

    public ExampleMcpServer() {
        super("example", "示例服务", "1.0.0");
        setType("API");
    }

    @Override
    protected Object doExecute(Map<String, Object> params) {
        try {
            // 1. 参数验证
            Map<String, Object> validatedParams = validateParameters(params);

            // 2. 执行业务逻辑
            Map<String, Object> result = executeBusinessLogic(validatedParams);

            return result;
        } catch (Exception e) {
            return handleError(e);
        }
    }

    protected Map<String, Object> validateParameters(Map<String, Object> params) {
        // 验证参数
        if (!params.containsKey("name") || params.get("name") == null) {
            throw new IllegalArgumentException("参数 'name' 不能为空");
        }
        return params;
    }

    protected Map<String, Object> executeBusinessLogic(Map<String, Object> params) {
        // 获取参数
        String name = (String) params.get("name");

        // 实现业务逻辑
        Map<String, Object> result = new HashMap<>();
        result.put("message", greeting + ", " + name + "!");

        return result;
    }
}
```

### 7.3 使用配置文件

生成的配置文件位于 `mcp-admin/src/main/resources/servers/{serviceId}.properties`，可以在运行时修改配置而不需要重新编译代码。使用 `@Value` 注解可以注入配置项：

```java
@Value("${example.greeting:Hello, World!}")
private String greeting;
```

配置文件示例：

```properties
# 示例服务配置
example.greeting=Hello, World!
```

## 8. 常见问题

### 8.1 生成的代码无法编译

问题：生成的代码无法编译，报编译错误。

解决方法：
1. 检查配置文件是否正确，包括参数类型、必填项等
2. 检查生成的代码是否存在语法错误
3. 检查是否缺少必要的依赖

### 8.2 生成的服务无法注册

问题：生成的服务无法注册，在服务列表中看不到。

解决方法：
1. 检查服务类是否有 `@Component` 注解
2. 检查服务类是否在正确的包路径下
3. 检查服务类是否正确继承了 `AbstractMcpServer` 类

### 8.3 生成的API控制器无法访问

问题：生成的API控制器无法访问，访问时报404错误。

解决方法：
1. 检查API控制器类是否有 `@RestController` 注解
2. 检查API控制器类是否在正确的包路径下
3. 检查API路径是否正确

## 9. 与大模型集成

MCP平台的所有服务都自动暴露为MCP协议端点，可以无缝集成到大模型中作为工具使用。不需要为不同的服务类型创建不同的模板，一个统一的模板可以支持所有服务类型。

### 9.1 MCP协议端点

代码生成器会自动生成MCP协议端点控制器，该控制器提供以下接口：

- **获取工具信息**：`GET /api/v1/mcp/{serviceId}/info`
- **获取工具参数**：`GET /api/v1/mcp/{serviceId}/parameters`
- **执行工具**：`POST /api/v1/mcp/{serviceId}/execute`

### 9.2 在Cline中配置

1. 打开Cline设置
2. 选择"Connected MCP Servers"选项卡
3. 点击"Add Server"按钮
4. 输入服务URL，例如：`http://localhost:8081/api/v1/mcp`
5. 点击"Save"按钮

配置完成后，Cline会自动发现并使用MCP服务作为工具。

### 9.3 使用示例

当用户在Cline中询问信息时，Cline会自动调用相应的MCP服务：

**用户提问**:
```
查询上海的天气
```

**Cline处理流程**:
1. 识别用户需要查询天气
2. 调用天气服务的MCP协议接口，参数为 `{"city": "Shanghai"}`
3. 获取天气信息
4. 将天气信息整合到回复中

## 10. 相关链接

- [MCP平台文档中心](/docs)
- [MCP协议文档](/docs/api/mcp-protocol)
- [REST API文档](/docs/api/rest-api)
- [开发指南](/docs/guides/development)
