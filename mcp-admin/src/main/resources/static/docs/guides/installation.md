# MCP平台安装指南

## 1. 概述

本文档提供了MCP平台的详细安装和部署说明，包括环境准备、源码获取、编译构建、部署配置和启动运行等步骤。

## 2. 环境准备

### 2.1 硬件要求

- **CPU**：2核或更高
- **内存**：至少4GB RAM
- **存储**：至少1GB可用空间

### 2.2 软件要求

- **操作系统**：
  - Windows 10+
  - macOS 10.15+
  - Linux (Ubuntu 20.04+, CentOS 8+)

- **Java环境**：
  - JDK 17或更高版本
  - 设置JAVA_HOME环境变量

- **构建工具**：
  - Maven 3.8.0或更高版本

- **数据库**（可选）：
  - MySQL 8.0+（用于MySQL服务）
  - Redis 6.0+（用于Redis服务）

- **其他工具**：
  - Git（用于获取源码）
  - curl或Postman（用于测试API）

### 2.3 环境检查

在开始安装之前，请确认环境是否正确配置：

```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查Git版本
git --version

# 检查MySQL版本（如果使用）
mysql --version

# 检查Redis版本（如果使用）
redis-cli --version
```

## 3. 获取源码

### 3.1 从GitHub克隆

```bash
git clone https://github.com/your-organization/mcp-platform.git
cd mcp-platform
```

### 3.2 下载发布包

您也可以从发布页面下载最新的发布包：

```bash
wget https://github.com/your-organization/mcp-platform/releases/download/v1.0.0/mcp-platform-1.0.0.zip
unzip mcp-platform-1.0.0.zip
cd mcp-platform-1.0.0
```

## 4. 编译构建

### 4.1 使用Maven构建

```bash
# 构建整个项目
mvn clean package

# 跳过测试构建
mvn clean package -DskipTests
```

### 4.2 构建结果

构建成功后，您将在各个模块的target目录中找到构建结果：

- `mcp-admin/target/mcp-admin-0.0.1-SNAPSHOT.jar`：管理后台模块
- `mcp-server/target/mcp-server-0.0.1-SNAPSHOT.jar`：服务器模块
- `mcp-generator/target/mcp-generator-0.0.1-SNAPSHOT.jar`：代码生成器模块

## 5. 配置

### 5.1 基本配置

MCP平台的配置文件位于各个模块的`src/main/resources`目录中：

- `application.properties`：主配置文件
- `application-dev.properties`：开发环境配置
- `application-prod.properties`：生产环境配置

### 5.2 数据库配置

如果您使用MySQL服务，需要配置数据库连接：

```properties
# MySQL配置
spring.datasource.url=***************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
```

### 5.3 Redis配置

如果您使用Redis服务，需要配置Redis连接：

```properties
# Redis配置
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.database=0
```

### 5.4 服务配置

各个服务的配置文件位于`mcp-server/src/main/resources/servers`目录中：

- `weather.properties`：天气服务配置
- `mysql.properties`：MySQL服务配置
- `redis.properties`：Redis服务配置

### 5.5 日志配置

日志配置文件位于`src/main/resources/logback.xml`，您可以根据需要调整日志级别和输出方式。

## 6. 部署

### 6.1 单机部署

#### 6.1.1 直接运行

```bash
# 启动管理后台
java -jar mcp-admin/target/mcp-admin-0.0.1-SNAPSHOT.jar

# 启动服务器（如果需要单独启动）
java -jar mcp-server/target/mcp-server-0.0.1-SNAPSHOT.jar
```

#### 6.1.2 使用Maven运行

```bash
# 启动管理后台
cd mcp-admin
mvn spring-boot:run

# 启动服务器（如果需要单独启动）
cd mcp-server
mvn spring-boot:run
```

### 6.2 Docker部署

#### 6.2.1 构建Docker镜像

```bash
# 构建管理后台镜像
cd mcp-admin
docker build -t mcp-admin:latest .

# 构建服务器镜像
cd mcp-server
docker build -t mcp-server:latest .
```

#### 6.2.2 运行Docker容器

```bash
# 运行管理后台容器
docker run -d -p 8081:8081 --name mcp-admin mcp-admin:latest

# 运行服务器容器
docker run -d -p 8083:8083 --name mcp-server mcp-server:latest
```

### 6.3 Kubernetes部署

#### 6.3.1 创建Deployment

```yaml
# mcp-admin-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mcp-admin
  template:
    metadata:
      labels:
        app: mcp-admin
    spec:
      containers:
      - name: mcp-admin
        image: mcp-admin:latest
        ports:
        - containerPort: 8081
```

#### 6.3.2 创建Service

```yaml
# mcp-admin-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: mcp-admin
spec:
  selector:
    app: mcp-admin
  ports:
  - port: 8081
    targetPort: 8081
  type: ClusterIP
```

#### 6.3.3 应用配置

```bash
kubectl apply -f mcp-admin-deployment.yaml
kubectl apply -f mcp-admin-service.yaml
```

## 7. 验证安装

### 7.1 检查服务状态

```bash
# 检查管理后台状态
curl http://localhost:8081/health

# 检查服务器状态
curl http://localhost:8083/health
```

### 7.2 访问管理界面

打开浏览器，访问管理界面：

```
http://localhost:8081
```

### 7.3 测试API

测试天气服务API：

```bash
curl -X POST "http://localhost:8081/api/weather" -H "Content-Type: application/json" -d '{
  "city": "Beijing"
}'
```

## 8. 常见问题

### 8.1 端口冲突

如果端口已被占用，您可以修改配置文件中的端口：

```properties
# 修改管理后台端口
server.port=8082

# 修改服务器端口
server.port=8084
```

### 8.2 数据库连接失败

如果数据库连接失败，请检查以下几点：

- 确保数据库服务已启动
- 检查数据库用户名和密码是否正确
- 检查数据库URL是否正确

### 8.3 内存不足

如果遇到内存不足的问题，可以调整JVM参数：

```bash
java -Xms512m -Xmx1024m -jar mcp-admin/target/mcp-admin-0.0.1-SNAPSHOT.jar
```

## 9. 升级指南

### 9.1 备份数据

在升级之前，请备份重要的数据和配置文件：

```bash
# 备份配置文件
cp -r mcp-admin/src/main/resources /backup/mcp-admin-config
cp -r mcp-server/src/main/resources /backup/mcp-server-config

# 备份数据库（如果使用）
mysqldump -u root -p mcp > /backup/mcp-db.sql
```

### 9.2 获取新版本

获取新版本的源码或发布包：

```bash
git pull
```

### 9.3 重新构建

按照第4节的步骤重新构建项目。

### 9.4 更新配置

将备份的配置文件与新版本的配置文件进行比较，并更新必要的配置。

### 9.5 重新部署

按照第6节的步骤重新部署项目。
