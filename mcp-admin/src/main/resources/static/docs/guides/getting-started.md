# MCP平台入门指南

## 1. 概述

MCP (Modular Computing Platform) 是一个基于Java Spring Boot和Spring AI的模块化计算平台，提供了一套灵活的服务注册、管理和执行框架。本指南将帮助您快速上手MCP平台，了解其基本功能和使用方法。

## 2. 系统要求

在开始使用MCP平台之前，请确保您的系统满足以下要求：

- **操作系统**：Windows 10+、macOS 10.15+、Linux (Ubuntu 20.04+, CentOS 8+)
- **Java**：JDK 8或更高版本
- **Maven**：3.8.0或更高版本
- **内存**：至少4GB RAM
- **存储**：至少1GB可用空间
- **网络**：互联网连接（用于下载依赖）

## 3. 快速开始

### 3.1 获取源码

首先，从GitHub仓库克隆MCP平台源码：

```bash
git clone https://github.com/your-organization/mcp-platform.git
cd mcp-platform
```

### 3.2 编译项目

使用Maven编译项目：

```bash
mvn clean package
```

### 3.3 启动服务

启动MCP平台服务：

```bash
cd mcp-admin
mvn spring-boot:run
```

服务启动后，您可以通过浏览器访问管理界面：

```
http://localhost:8081
```

## 4. 基本功能

### 4.1 查看服务列表

在管理界面首页，您可以看到所有已注册的MCP服务，包括服务名称、描述、版本、类型和状态等信息。

### 4.2 管理服务

您可以通过管理界面对服务进行管理，包括：

- **启动服务**：点击"启动"按钮，启动特定服务
- **停止服务**：点击"停止"按钮，停止特定服务
- **查看详情**：点击"详情"按钮，查看服务的详细信息

### 4.3 调用服务

MCP平台提供了两种方式调用服务：

#### 4.3.1 通过REST API调用

```bash
curl -X POST "http://localhost:8081/api/{serviceId}" -H "Content-Type: application/json" -d '{
  "param1": "value1",
  "param2": "value2"
}'
```

#### 4.3.2 通过MCP协议调用

```bash
curl -X POST "http://localhost:8081/api/mcp/tools/{serviceId}/execute" -H "Content-Type: application/json" -d '{
  "parameters": {
    "param1": "value1",
    "param2": "value2"
  }
}'
```

### 4.4 查看API文档

MCP平台提供了详细的API文档，您可以通过以下方式访问：

- **Swagger UI**：访问 `http://localhost:8081/swagger-ui/index.html`
- **API文档页面**：访问 `http://localhost:8081/api-docs`

## 5. 示例服务

MCP平台内置了几个示例服务，您可以通过这些服务了解平台的基本功能：

### 5.1 天气查询服务

天气查询服务提供了基于城市名称的天气查询功能，支持全球主要城市。

**调用示例**：

```bash
curl -X POST "http://localhost:8081/api/weather" -H "Content-Type: application/json" -d '{
  "city": "Beijing"
}'
```

### 5.2 MySQL服务

MySQL服务提供了基本的数据库操作功能，包括查询、插入、更新和删除等。

**调用示例**：

```bash
curl -X POST "http://localhost:8081/api/mysql" -H "Content-Type: application/json" -d '{
  "sql": "SELECT * FROM users LIMIT 10"
}'
```

### 5.3 Redis服务

Redis服务提供了基本的Redis操作功能，包括设置、获取、删除等。

**调用示例**：

```bash
curl -X POST "http://localhost:8081/api/redis" -H "Content-Type: application/json" -d '{
  "command": "GET",
  "key": "user:1"
}'
```

## 6. 下一步

完成入门指南后，您可以继续阅读以下文档，深入了解MCP平台：

- [安装指南](installation.md)：详细的安装和部署说明
- [配置指南](configuration.md)：平台配置选项和说明
- [开发指南](development.md)：如何开发自定义服务
- [API文档](../api/rest-api.md)：REST API接口文档
- [MCP协议](../api/mcp-protocol.md)：MCP协议接口文档

## 7. 常见问题

### 7.1 服务无法启动

如果服务无法启动，请检查以下几点：

- 确保端口未被占用
- 检查日志文件中的错误信息
- 确保配置文件正确

### 7.2 服务调用失败

如果服务调用失败，请检查以下几点：

- 确保服务已启动
- 检查参数格式是否正确
- 检查服务ID是否正确

### 7.3 找不到服务

如果找不到服务，请检查以下几点：

- 确保服务已注册
- 检查服务ID是否正确
- 检查服务是否已启动
