# MCP平台配置指南

## 1. 概述

本文档提供了MCP平台的详细配置说明，包括系统配置、服务配置、数据库配置、缓存配置、日志配置等。通过正确配置MCP平台，您可以根据自己的需求定制平台的行为和功能。

## 2. 配置文件结构

MCP平台的配置文件主要分为以下几类：

- **系统配置**：`application.properties`、`application-dev.properties`、`application-prod.properties`
- **服务配置**：`servers/*.properties`
- **日志配置**：`logback.xml`
- **Web配置**：`application-web.properties`
- **安全配置**：`application-security.properties`

## 3. 系统配置

系统配置文件位于各个模块的`src/main/resources`目录中，主要包括以下配置项：

### 3.1 基本配置

```properties
# 应用名称
spring.application.name=mcp-platform

# 服务器端口
server.port=8081

# 上下文路径
server.servlet.context-path=/

# 字符编码
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 会话超时时间（分钟）
server.servlet.session.timeout=30m

# 临时文件目录
server.tomcat.basedir=./temp
```

### 3.2 配置文件激活

```properties
# 激活的配置文件
spring.profiles.active=dev

# 包含的配置文件
spring.profiles.include=web,security
```

### 3.3 环境特定配置

#### 开发环境配置（application-dev.properties）

```properties
# 开发环境配置
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# 调试模式
debug=true

# 显示SQL语句
spring.jpa.show-sql=true
```

#### 生产环境配置（application-prod.properties）

```properties
# 生产环境配置
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false

# 调试模式
debug=false

# 隐藏SQL语句
spring.jpa.show-sql=false
```

## 4. 服务配置

服务配置文件位于`mcp-server/src/main/resources/servers`目录中，每个服务都有一个对应的配置文件。

### 4.1 天气服务配置（weather.properties）

```properties
# 天气API密钥
weather.api.key=your_api_key

# 天气API地址
weather.api.url=https://api.example.com/weather

# 缓存配置
weather.cache.enabled=true
weather.cache.ttl=3600

# 默认城市
weather.default.city=Beijing

# 温度单位（celsius或fahrenheit）
weather.temperature.unit=celsius
```

### 4.2 MySQL服务配置（mysql.properties）

```properties
# 数据库连接配置
mysql.jdbc.url=*****************************************************************
mysql.jdbc.username=root
mysql.jdbc.password=root
mysql.jdbc.driver=com.mysql.cj.jdbc.Driver

# 连接池配置
mysql.pool.initial-size=5
mysql.pool.min-idle=5
mysql.pool.max-active=20
mysql.pool.max-wait=60000

# 查询超时时间（秒）
mysql.query.timeout=30

# 最大结果集大小
mysql.result.max-rows=1000
```

### 4.3 Redis服务配置（redis.properties）

```properties
# Redis连接配置
redis.host=localhost
redis.port=6379
redis.password=
redis.database=0

# 连接池配置
redis.pool.max-active=8
redis.pool.max-idle=8
redis.pool.min-idle=0
redis.pool.max-wait=-1

# 命令超时时间（毫秒）
redis.command.timeout=5000

# 最大结果集大小
redis.result.max-items=1000
```

## 5. 数据库配置

如果您的应用需要连接数据库，可以在`application.properties`中配置数据库连接：

```properties
# 数据库连接配置
spring.datasource.url=***************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 连接池配置
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=MCP-HikariCP
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.open-in-view=false
```

## 6. 缓存配置

MCP平台使用Spring Cache和Redis实现缓存功能：

```properties
# 缓存类型
spring.cache.type=redis

# Redis缓存配置
spring.cache.redis.time-to-live=3600000
spring.cache.redis.cache-null-values=true
spring.cache.redis.use-key-prefix=true
spring.cache.redis.key-prefix=mcp:cache:
```

## 7. 日志配置

日志配置文件位于`src/main/resources/logback.xml`，您可以根据需要调整日志级别和输出方式：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/mcp.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/mcp.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
    
    <!-- 特定包的日志级别 -->
    <logger name="com.jdl.mcp" level="DEBUG" />
    <logger name="org.springframework" level="INFO" />
    <logger name="org.hibernate" level="INFO" />
</configuration>
```

## 8. Web配置

Web配置文件位于`application-web.properties`，主要包括以下配置项：

```properties
# 静态资源路径
spring.mvc.static-path-pattern=/static/**
spring.web.resources.static-locations=classpath:/static/

# 模板引擎配置
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.cache=false

# 文件上传配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=100MB
```

## 9. 安全配置

安全配置文件位于`application-security.properties`，主要包括以下配置项：

```properties
# 是否启用安全功能
security.enabled=true

# API密钥
security.api.key=your_api_key

# CORS配置
security.cors.allowed-origins=*
security.cors.allowed-methods=GET,POST,PUT,DELETE
security.cors.allowed-headers=*
security.cors.max-age=3600

# CSRF配置
security.csrf.enabled=false

# 会话配置
security.session.timeout=30m
```

## 10. 自定义配置

您可以根据需要添加自定义配置项，并在代码中使用`@Value`注解或`@ConfigurationProperties`注解读取配置值：

```java
@Component
public class MyService {
    
    @Value("${my.custom.property}")
    private String customProperty;
    
    // ...
}
```

或者：

```java
@Component
@ConfigurationProperties(prefix = "my.custom")
public class MyConfig {
    
    private String property;
    
    // Getter and Setter
    public String getProperty() {
        return property;
    }
    
    public void setProperty(String property) {
        this.property = property;
    }
}
```

## 11. 配置最佳实践

### 11.1 使用配置文件分离敏感信息

将敏感信息（如密码、API密钥等）放在单独的配置文件中，并在版本控制中忽略这些文件：

```properties
# application-secrets.properties
mysql.jdbc.password=your_password
weather.api.key=your_api_key
```

### 11.2 使用环境变量覆盖配置

在生产环境中，使用环境变量覆盖配置文件中的值：

```bash
export MYSQL_JDBC_PASSWORD=your_password
export WEATHER_API_KEY=your_api_key
```

### 11.3 使用配置服务器

在分布式环境中，使用Spring Cloud Config Server集中管理配置：

```properties
# bootstrap.properties
spring.application.name=mcp-platform
spring.cloud.config.uri=http://config-server:8888
spring.cloud.config.fail-fast=true
```

### 11.4 配置验证

使用`@Validated`和`@Valid`注解验证配置属性：

```java
@Component
@ConfigurationProperties(prefix = "mysql")
@Validated
public class MySqlConfig {
    
    @NotEmpty
    private String jdbcUrl;
    
    @NotEmpty
    private String jdbcUsername;
    
    @NotEmpty
    private String jdbcPassword;
    
    // Getters and Setters
}
```

## 12. 配置示例

### 12.1 开发环境配置示例

```properties
# application-dev.properties
server.port=8081
spring.datasource.url=***********************************
spring.datasource.username=root
spring.datasource.password=root
logging.level.com.jdl.mcp=DEBUG
```

### 12.2 生产环境配置示例

```properties
# application-prod.properties
server.port=80
spring.datasource.url=**********************************
spring.datasource.username=${MYSQL_USERNAME}
spring.datasource.password=${MYSQL_PASSWORD}
logging.level.com.jdl.mcp=INFO
```

### 12.3 Docker环境配置示例

```properties
# application-docker.properties
server.port=8080
spring.datasource.url=***************************
spring.datasource.username=root
spring.datasource.password=root
redis.host=redis
```
