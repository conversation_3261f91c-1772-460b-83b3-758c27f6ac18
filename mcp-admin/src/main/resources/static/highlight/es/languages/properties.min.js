/*! `properties` grammar compiled for Highlight.js 11.10.0 */
var hljsGrammar=(()=>{"use strict";return e=>{
const n="[ \\t\\f]*",t=n+"[:=]"+n,a="[ \\t\\f]+",s="([^\\\\:= \\t\\f\\n]|\\\\.)+",r={
end:"("+t+"|"+a+")",relevance:0,starts:{className:"string",end:/$/,relevance:0,
contains:[{begin:"\\\\\\\\"},{begin:"\\\\\\n"}]}};return{name:".properties",
disableAutodetect:!0,case_insensitive:!0,illegal:/\S/,
contains:[e.COMMENT("^\\s*[!#]","$"),{returnBegin:!0,variants:[{begin:s+t},{
begin:s+a}],contains:[{className:"attr",begin:s,endsParent:!0}],starts:r},{
className:"attr",begin:s+n+"$"}]}}})();export default hljsGrammar;