/*! `nix` grammar compiled for Highlight.js 11.10.0 */
var hljsGrammar=(()=>{"use strict";return e=>{const a={
keyword:["rec","with","let","in","inherit","assert","if","else","then"],
literal:["true","false","or","and","null"],
built_in:["import","abort","baseNameOf","dirOf","isNull","builtins","map","removeAttrs","throw","toString","derivation"]
},n={className:"subst",begin:/\$\{/,end:/\}/,keywords:a},r={className:"string",
contains:[{className:"char.escape",begin:/''\$/},n],variants:[{begin:"''",
end:"''"},{begin:'"',end:'"'}]
},s=[e.NUMBER_MODE,e.HASH_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,r,{
begin:/[a-zA-Z0-9-_]+(\s*=)/,returnBegin:!0,relevance:0,contains:[{
className:"attr",begin:/\S+/,relevance:.2}]}];return n.contains=s,{name:"Nix",
aliases:["nixos"],keywords:a,contains:s}}})();export default hljsGrammar;