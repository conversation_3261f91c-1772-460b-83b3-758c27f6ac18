/*! `rsl` grammar compiled for Highlight.js 11.10.0 */
var hljsGrammar=(()=>{"use strict";return e=>{const t={
match:[/(surface|displacement|light|volume|imager)/,/\s+/,e.IDENT_RE],scope:{
1:"keyword",3:"title.class"}};return{name:"RenderMan RSL",keywords:{
keyword:["while","for","if","do","return","else","break","extern","continue"],
built_in:["abs","acos","ambient","area","asin","atan","atmosphere","attribute","calculatenormal","ceil","cellnoise","clamp","comp","concat","cos","degrees","depth","Deriv","diffuse","distance","Du","Dv","environment","exp","faceforward","filterstep","floor","format","fresnel","incident","length","lightsource","log","match","max","min","mod","noise","normalize","ntransform","opposite","option","phong","pnoise","pow","printf","ptlined","radians","random","reflect","refract","renderinfo","round","setcomp","setxcomp","setycomp","setzcomp","shadow","sign","sin","smoothstep","specular","specularbrdf","spline","sqrt","step","tan","texture","textureinfo","trace","transform","vtransform","xcomp","ycomp","zcomp"],
type:["matrix","float","color","point","normal","vector"]},illegal:"</",
contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,e.C_NUMBER_MODE,{
className:"meta",begin:"#",end:"$"},t,{
beginKeywords:"illuminate illuminance gather",end:"\\("}]}}})()
;export default hljsGrammar;