/*! `fsharp` grammar compiled for Highlight.js 11.10.0 */
var hljsGrammar=(()=>{"use strict";function e(e){
return RegExp(e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"m")}function n(e){
return e?"string"==typeof e?e:e.source:null}function t(e){return i("(?=",e,")")}
function i(...e){return e.map((e=>n(e))).join("")}function a(...e){const t=(e=>{
const n=e[e.length-1]
;return"object"==typeof n&&n.constructor===Object?(e.splice(e.length-1,1),n):{}
})(e);return"("+(t.capture?"":"?:")+e.map((e=>n(e))).join("|")+")"}return n=>{
const r={scope:"keyword",match:/\b(yield|return|let|do|match|use)!/
},o=["bool","byte","sbyte","int8","int16","int32","uint8","uint16","uint32","int","uint","int64","uint64","nativeint","unativeint","decimal","float","double","float32","single","char","string","unit","bigint","option","voption","list","array","seq","byref","exn","inref","nativeptr","obj","outref","voidptr","Result"],s={
keyword:["abstract","and","as","assert","base","begin","class","default","delegate","do","done","downcast","downto","elif","else","end","exception","extern","finally","fixed","for","fun","function","global","if","in","inherit","inline","interface","internal","lazy","let","match","member","module","mutable","namespace","new","of","open","or","override","private","public","rec","return","static","struct","then","to","try","type","upcast","use","val","void","when","while","with","yield"],
literal:["true","false","null","Some","None","Ok","Error","infinity","infinityf","nan","nanf"],
built_in:["not","ref","raise","reraise","dict","readOnlyDict","set","get","enum","sizeof","typeof","typedefof","nameof","nullArg","invalidArg","invalidOp","id","fst","snd","ignore","lock","using","box","unbox","tryUnbox","printf","printfn","sprintf","eprintf","eprintfn","fprintf","fprintfn","failwith","failwithf"],
"variable.constant":["__LINE__","__SOURCE_DIRECTORY__","__SOURCE_FILE__"]},c={
variants:[n.COMMENT(/\(\*(?!\))/,/\*\)/,{contains:["self"]
}),n.C_LINE_COMMENT_MODE]},l={scope:"variable",begin:/``/,end:/``/
},u=/\B('|\^)/,p={scope:"symbol",variants:[{match:i(u,/``.*?``/)},{
match:i(u,n.UNDERSCORE_IDENT_RE)}],relevance:0},f=({includeEqual:n})=>{let r
;r=n?"!%&*+-/<=>@^|~?":"!%&*+-/<>@^|~?"
;const o=i("[",...Array.from(r).map(e),"]"),s=a(o,/\./),c=i(s,t(s)),l=a(i(c,s,"*"),i(o,"+"))
;return{scope:"operator",match:a(l,/:\?>/,/:\?/,/:>/,/:=/,/::?/,/\$/),
relevance:0}},d=f({includeEqual:!0}),b=f({includeEqual:!1}),m=(e,r)=>({
begin:i(e,t(i(/\s*/,a(/\w/,/'/,/\^/,/#/,/``/,/\(/,/{\|/)))),beginScope:r,
end:t(a(/\n/,/=/)),relevance:0,keywords:n.inherit(s,{type:o}),
contains:[c,p,n.inherit(l,{scope:null}),b]
}),g=m(/:/,"operator"),h=m(/\bof\b/,"keyword"),y={
begin:[/(^|\s+)/,/type/,/\s+/,/[a-zA-Z_](\w|')*/],beginScope:{2:"keyword",
4:"title.class"},end:t(/\(|=|$/),keywords:s,contains:[c,n.inherit(l,{scope:null
}),p,{scope:"operator",match:/<|>/},g]},E={scope:"computation-expression",
match:/\b[_a-z]\w*(?=\s*\{)/},_={
begin:[/^\s*/,i(/#/,a("if","else","endif","line","nowarn","light","r","i","I","load","time","help","quit")),/\b/],
beginScope:{2:"meta"},end:t(/\s|$/)},v={
variants:[n.BINARY_NUMBER_MODE,n.C_NUMBER_MODE]},w={scope:"string",begin:/"/,
end:/"/,contains:[n.BACKSLASH_ESCAPE]},A={scope:"string",begin:/@"/,end:/"/,
contains:[{match:/""/},n.BACKSLASH_ESCAPE]},S={scope:"string",begin:/"""/,
end:/"""/,relevance:2},C={scope:"subst",begin:/\{/,end:/\}/,keywords:s},O={
scope:"string",begin:/\$"/,end:/"/,contains:[{match:/\{\{/},{match:/\}\}/
},n.BACKSLASH_ESCAPE,C]},x={scope:"string",begin:/(\$@|@\$)"/,end:/"/,
contains:[{match:/\{\{/},{match:/\}\}/},{match:/""/},n.BACKSLASH_ESCAPE,C]},R={
scope:"string",begin:/\$"""/,end:/"""/,contains:[{match:/\{\{/},{match:/\}\}/
},C],relevance:2},k={scope:"string",
match:i(/'/,a(/[^\\']/,/\\(?:.|\d{3}|x[a-fA-F\d]{2}|u[a-fA-F\d]{4}|U[a-fA-F\d]{8})/),/'/)
};return C.contains=[x,O,A,w,k,r,c,l,g,E,_,v,p,d],{name:"F#",
aliases:["fs","f#"],keywords:s,illegal:/\/\*/,classNameAliases:{
"computation-expression":"keyword"},contains:[r,{variants:[R,x,O,S,A,w,k]
},c,l,y,{scope:"meta",begin:/\[</,end:/>\]/,relevance:2,contains:[l,S,A,w,k,v]
},h,g,E,_,v,p,d]}}})();export default hljsGrammar;