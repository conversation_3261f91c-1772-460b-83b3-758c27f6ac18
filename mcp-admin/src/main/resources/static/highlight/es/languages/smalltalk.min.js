/*! `smalltalk` grammar compiled for Highlight.js 11.10.0 */
var hljsGrammar=(()=>{"use strict";return e=>{const a="[a-z][a-zA-Z0-9_]*",n={
className:"string",begin:"\\$.{1}"},s={className:"symbol",
begin:"#"+e.UNDERSCORE_IDENT_RE};return{name:"Smalltalk",aliases:["st"],
keywords:["self","super","nil","true","false","thisContext"],
contains:[e.COMMENT('"','"'),e.APOS_STRING_MODE,{className:"type",
begin:"\\b[A-Z][A-Za-z0-9_]*",relevance:0},{begin:a+":",relevance:0
},e.C_NUMBER_MODE,s,n,{begin:"\\|[ ]*"+a+"([ ]+"+a+")*[ ]*\\|",returnBegin:!0,
end:/\|/,illegal:/\S/,contains:[{begin:"(\\|[ ]*)?"+a}]},{begin:"#\\(",
end:"\\)",contains:[e.APOS_STRING_MODE,n,e.C_NUMBER_MODE,s]}]}}})()
;export default hljsGrammar;