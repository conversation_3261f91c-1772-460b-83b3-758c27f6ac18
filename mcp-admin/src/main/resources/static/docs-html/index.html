<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP平台文档中心</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- GitHub Markdown CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css@5.1.0/github-markdown.min.css">
    <link rel="stylesheet" href="/css/docs.css">
</head>
<body>
    <div class="docs-container">
        <!-- 侧边栏 -->
        <div class="docs-nav">
            <div class="doc-header">
                <h1><a href="/enhanced-docs">MCP平台文档</a></h1>
            </div>

            <!-- 文档导航 -->
            <ul id="doc-nav" class="doc-nav">
                <li class="doc-nav-item">
                    <a class="doc-nav-link active" href="/enhanced-docs">
                        <i class="fas fa-home"></i> 文档首页
                    </a>
                </li>

                <!-- 动态加载导航 -->
            </ul>
        </div>

        <!-- 主要内容 -->
        <div class="docs-content">
            <!-- 面包屑导航 -->
            <div id="doc-breadcrumb" class="doc-breadcrumb">
                <a href="/dashboard">首页</a> /
                <span>文档中心</span>
            </div>

            <!-- 文档内容 -->
            <div id="doc-content" class="doc-markdown markdown-body">
                <div class="doc-hero">
                    <h1>MCP平台文档中心</h1>
                    <p class="lead">连接服务与大模型的桥梁，让每个服务都能成为AI的得力助手</p>
                </div>

                <div class="doc-intro">
                    <p>欢迎访问MCP (Model Context Protocol) 平台文档中心。MCP平台是一个专为大模型时代设计的服务框架，它能够将各种服务无缝集成到大型语言模型中，使模型能够调用外部工具执行特定任务。</p>

                    <div class="doc-highlights">
                        <div class="highlight-item">
                            <div class="highlight-icon"><i class="fas fa-plug"></i></div>
                            <h3>无缝集成</h3>
                            <p>所有服务自动暴露REST API和MCP协议端点，无需额外开发</p>
                        </div>
                        <div class="highlight-item">
                            <div class="highlight-icon"><i class="fas fa-robot"></i></div>
                            <h3>AI增强</h3>
                            <p>与大模型无缝集成，让每个服务都能成为AI的工具</p>
                        </div>
                        <div class="highlight-item">
                            <div class="highlight-icon"><i class="fas fa-code"></i></div>
                            <h3>快速开发</h3>
                            <p>强大的代码生成器，一键生成完整服务</p>
                        </div>
                    </div>
                </div>

                <h2>平台概述</h2>

                <p>MCP平台是一个基于Java Spring Boot的服务框架，专为大模型时代设计。它提供了一套灵活的服务注册、管理和执行框架，支持多种服务类型，包括API服务、数据库服务、缓存服务、RPC服务等。平台的核心特点是所有服务都同时支持REST API和MCP协议端点，可以通过HTTP请求直接调用，也可以集成到大模型中作为工具使用。</p>

                <div class="doc-architecture">
                    <div class="arch-diagram">
                        <img src="/images/mcp-architecture.png" alt="MCP平台架构图" onerror="this.style.display='none'">
                    </div>
                    <div class="arch-description">
                        <h3>核心架构</h3>
                        <p>MCP平台采用模块化设计，主要包括以下几个核心模块：</p>
                        <ul>
                            <li><strong>mcp-core</strong>：核心模块，提供基础接口和抽象类</li>
                            <li><strong>mcp-server</strong>：服务模块，包含各种服务的实现</li>
                            <li><strong>mcp-admin</strong>：管理模块，提供REST API和MCP协议端点</li>
                            <li><strong>mcp-generator</strong>：代码生成器模块，用于生成服务代码</li>
                        </ul>
                    </div>
                </div>

                <h2>主要特点</h2>

                <div class="doc-features">
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-cubes"></i></div>
                        <div class="feature-content">
                            <h3>模块化设计</h3>
                            <p>平台分为核心、服务器、管理后台和代码生成器等模块，每个模块职责明确，便于扩展和维护。</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-exchange-alt"></i></div>
                        <div class="feature-content">
                            <h3>双重接口支持</h3>
                            <p>所有服务都同时支持REST API和MCP协议端点，无需单独开发AI类型服务，一个统一的模板可以支持所有服务类型。</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-brain"></i></div>
                        <div class="feature-content">
                            <h3>大模型集成</h3>
                            <p>无缝集成到大模型中，作为工具使用，支持Cline等工具，让大模型能够调用外部服务执行特定任务。</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-magic"></i></div>
                        <div class="feature-content">
                            <h3>代码生成</h3>
                            <p>通过代码生成器快速创建新服务，自动生成REST API和MCP协议端点，大幅提高开发效率。</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-cogs"></i></div>
                        <div class="feature-content">
                            <h3>统一管理</h3>
                            <p>统一管理所有服务，包括注册、发现、调用和监控，提供完整的服务生命周期管理。</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-file-alt"></i></div>
                        <div class="feature-content">
                            <h3>自动化文档</h3>
                            <p>自动生成服务文档、API文档和MCP协议文档，方便开发和使用，提高协作效率。</p>
                        </div>
                    </div>
                </div>

                <h2>快速入门</h2>

                <div class="doc-quickstart">
                    <div class="quickstart-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>安装平台</h3>
                            <div class="code-block">
                                <pre><code>git clone https://github.com/your-org/mcp-platform.git
cd mcp-platform
./rebuild-start-jdk8.sh</code></pre>
                            </div>
                        </div>
                    </div>
                    <div class="quickstart-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>创建服务</h3>
                            <div class="code-block">
                                <pre><code>java -jar mcp-generator.jar create-template -o my-service.yml --enhanced
# 编辑配置文件
java -jar mcp-generator.jar generate-enhanced -c my-service.yml</code></pre>
                            </div>
                        </div>
                    </div>
                    <div class="quickstart-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>调用服务</h3>
                            <div class="code-block">
                                <pre><code># REST API调用
curl -X GET "http://localhost:8081/api/v1/my-service?param=value"

# MCP协议调用
curl -X POST "http://localhost:8081/api/v1/mcp/my-service/execute" \
  -H "Content-Type: application/json" \
  -d '{"param": "value"}'</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <h2>文档导航</h2>

                <div class="doc-navigation">
                    <div class="nav-section">
                        <div class="nav-icon"><i class="fas fa-sitemap"></i></div>
                        <div class="nav-content">
                            <h3>架构文档</h3>
                            <ul>
                                <li><a href="/enhanced-docs/architecture/overview.html">系统架构</a> - MCP平台的整体架构和设计理念</li>
                                <li><a href="/enhanced-docs/architecture/core-concepts.html">核心概念</a> - MCP平台的核心概念和术语解释</li>
                                <li><a href="/enhanced-docs/architecture/modules.html">模块结构</a> - MCP平台的模块结构和职责划分</li>
                            </ul>
                        </div>
                    </div>
                    <div class="nav-section">
                        <div class="nav-icon"><i class="fas fa-book"></i></div>
                        <div class="nav-content">
                            <h3>使用指南</h3>
                            <ul>
                                <li><a href="/enhanced-docs/guides/getting-started.html">快速开始</a> - 快速上手MCP平台</li>
                                <li><a href="/enhanced-docs/guides/installation.html">安装部署</a> - 安装和部署MCP平台</li>
                                <li><a href="/enhanced-docs/guides/configuration.html">配置管理</a> - 配置MCP平台的各项参数</li>
                                <li><a href="/enhanced-docs/guides/development.html">开发指南</a> - 开发MCP服务和扩展功能</li>
                                <li><a href="/enhanced-docs/guides/code-generator.html">代码生成器</a> - 使用代码生成器快速创建MCP服务</li>
                            </ul>
                        </div>
                    </div>
                    <div class="nav-section">
                        <div class="nav-icon"><i class="fas fa-code"></i></div>
                        <div class="nav-content">
                            <h3>API文档</h3>
                            <ul>
                                <li><a href="/enhanced-docs/api/rest-api.html">REST API</a> - REST API接口文档，包含请求参数、响应格式和示例</li>
                                <li><a href="/enhanced-docs/api/mcp-protocol.html">MCP协议</a> - MCP协议接口文档，包含协议格式、参数架构和示例</li>
                            </ul>
                        </div>
                    </div>
                    <div class="nav-section">
                        <div class="nav-icon"><i class="fas fa-server"></i></div>
                        <div class="nav-content">
                            <h3>服务文档</h3>
                            <ul>
                                <li><a href="/enhanced-docs/servers/mysql.html">MySQL服务</a> - MySQL数据库服务，提供SQL查询功能</li>
                                <li><a href="/enhanced-docs/servers/redis.html">Redis服务</a> - Redis缓存服务，提供缓存操作功能</li>
                                <li><a href="/enhanced-docs/servers/weather.html">天气服务</a> - 天气查询服务，提供天气查询功能</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="doc-cta">
                    <h2>开始使用MCP平台</h2>
                    <p>MCP平台让服务开发和大模型集成变得简单高效。立即开始使用，体验无缝集成的魅力！</p>
                    <div class="cta-buttons">
                        <a href="/enhanced-docs/guides/getting-started.html" class="btn btn-primary">快速开始</a>
                        <a href="/enhanced-docs/guides/code-generator.html" class="btn btn-secondary">了解代码生成器</a>
                        <a href="/enhanced-docs/api/mcp-protocol.html" class="btn btn-info">探索MCP协议</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/docs.js"></script>
</body>
</html>