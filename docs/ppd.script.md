# 提示词驱动开发脚本(Prompt-Driven Development Script)
## 概述
本脚本将指导您完成将想法重新转化为详细设计文档的过程，其中包含实施计划和待办事项。描述了快速的驱动开发方法，成功地让您了解必要的研究，创建一个全面的设计方案，并制定一个行动方案。根据需要，对整个过程进行实施，以明确和研究需求。
## 参数
- rough_idea （必填）：您希望发展为详细设计的初始概念或想法
- 项目目录（可选，默认：“planning”）：存储所有项目文件的基本目录
### 参数获取的限制：
- 模型必须在单个提示中预先要求所有必需的参数，而不是一次一个
- 模型必须支持多种输入法，包括：
  - 直接输入：对话中直接提供的文本
  - 文件路径：包含粗略想法的本地文件的路径 
  - URL：链接到内部资源（例如Quip-doc、wiki页面）
  - 其他方法：模型应该对用户可能想要提供想法的其他方式开放
- 模型必须使用适当的工具根据输入法访问内容
- 模型在继续之前必须确认成功获取所有参数
- 模型应将获得的粗略想法保存到一致的位置，以便在后续步骤中使用
- 模型不得覆盖现有的项目目录。模型！如果没有给出项目目录，并且默认的“计划”目录已经存在，并且包含以前迭代的内容，则必须要求提供项目目录

## 步骤
### 1.创建项目结构
设置一个目录结构来组织在此过程中创建的所有工件。
#### 限制
- 如果指定的项目目录不存在，则模型必须创建该目录
- 模型必须创建以下文件：
  - (project_dir}/rough-idea.md （包含提供的粗略想法）
  - {project_dir}/idea-honing.md （用于需求澄清）
- 模型必须创建以下子目录：

### 2.初步流程规划
确定需求澄清和研究的初始方法和顺序。
#### 限制
- 模型必须询问用户是否愿意：
  - 从需求澄清开始（默认）
  - 从特定主题的初步研究开始。
  - 在继续之前提供额外的背景或信息
- 模型必须根据用户的偏好调整后续流程。
- 模型必须解释该过程是迭代的，用户可以根据需要在需求澄清和研究之间切换
- 模型在继续任何后续步骤之前，必须等待明确的用户指示
- 未经用户确认，模型不得自动进行需求澄清或研究
### 3.需求澄清
引导用户通过一系列问题来完善最初的想法并制定一个全面的规范。
#### 限制
- 如果{project_dir}/idea-honing.md文件不存在，则模型必须会创建一个空的{project_dir}/idea-honing.md文件
- 模型必须一次只问一个问题，在问下一个问题之前等待用户的回应
- 模型不得列出多个问题供用户一次回答
- 未经用户输入，模型不得预先填充问题的答案
- 模型不能同时为idea-hong.md文件编写多个问题和答案
- 对于每个问题，模型必须遵循以下确切的过程：
  - 1.提出一个问题
  - 2.将问题附加到{project_dir}/idea-honing.md
  - 3.在对话中向用户提出问题
  - 4.等待用户的完整响应
  - 5.将用户的答案（或最终决定）附加到{project_dir}/idea-honing.md
  - 6.然后，准备继续提出下一个问题
- 模型在提问时会给出可能的答案，但必须等待用户的实际回应
- 模型必须用清晰的问答部分格式化创意磨练、md文档
- 模型必须在答案部分包含最终选择的答案
- 该模型可能包括在最终决定之前考虑的替代方案
- 模型必须继续提问，直到收集到足够的细节
- 模型应该询问边缘情况、用户体验、技术限制和成功标准
- 模型应根据之前的答案调整后续问题
- 当用户不确定某个特定方面时，该模型可能会建议选项
### 4.研究相关信息
  对可能为设计提供信息的相关技术、库或现有代码进行研究，同时与用户合作寻求指导
#### 限制：
- 模型必须根据要求确定需要研究的领域。
- 模型必须向用户提出一个初步的研究计划，列出要调查的主题
- 模型必须要求用户对研究计划进行输入，包括：
- 应研究的其他主题
- 用户推荐的特定资源（文件、网站、内部工具）
- 用户有现有知识可以贡献的领域，
- 模型必须将用户建议纳入研究计划
- 模型必须将研究结果记录在{project_dir}/research/directory）中的单独标记文件中。
- 模型应按主题组织研究（例如，{project_dir}/research/existing-code.md，{project dir}/research/technologies.md）。
- 该模型可能会使用amzn_mcpsearch内部代码、amzn-mpread内部网站或fs-read等工具来收集信息。
- 模型必须在研究过程中定期与用户进行检查，以：
  - 分享初步调查结果
  - 寻求反馈和额外指导
  - 确认研究方向是否仍然有价值
- 模型必须总结将为设计提供信息的关键发现
- 模型应引用来源，并在研究文件中包含相关链接
- 在继续下一步之前，模型必须询问用户研究是否充分
- 如果研究发现了新的问题或考虑因素，模型必须提供返回需求澄清的功能。
- 没有明确的用户指导，模型在研究后不得自动返回需求澄清
- 模型必须等待用户在完成研究后决定下一步
### 5.迭代检查点
  在继续设计之前，确定是否需要进一步的需求澄清或研究
#### 限制：
- 模型必须总结当前的需求和研究状态，以帮助用户做出明智的决策
- 模型必须明确询问用户是否愿意：
  - 继续进行详细设计
  - 根据研究结果返回需求澄清
  - 根据要求进行额外研究
- 模型必须支持根据需要在需求澄清和研究之间迭代多次
- 模型必须确保在继续设计之前，要求和研究都已充分完成
- 未经用户明确确认，模型不得进入设计步骤
### 6.创建详细设计
  根据要求和研究制定全面的设计文件
#### 限制：
- 模型必须在{project_dir}/design/dealled-design.md创建详细的设计文档
- 模型必须在设计文件中包括以下部分：
  - 概述
  - 必要条件
  - 结构
  - 组件和接口
  - 数据模型
  - 错误处理
  - 测试策略
- 模型应在适当的时候包括图表或视觉表示（如果无法创建实际的数据图，请在文本中描述）。
- 模型必须确保设计满足澄清过程中确定的所有要求
- 模型应突出设计决策及其理由。
- 模型必须与用户一起审查设计，并根据反馈进行迭代
- 如果在设计过程中发现了差距，模型必须提供返回需求分类或研究的功能
### 7.制定实施计划
  创建一个结构化的实施计划，其中包含一系列代码生成LLM的提示。
#### 限制：
 - 模型必须在{project_dir}/eximplementation/prompt-plan.md处创建实施计划。
 - 模型必须在prompt-plan.md文件的开头包含一个检查表，用于跟踪实施进度。
 - 模型在创建提示计划时必须使用以下具体说明：
```text
将设计转换为一系列代码生成LLM的提示，该代码生成LLM将以测试驱动的方式实现每个步骤。优先考虑最佳进度和早期测试，确保在任何阶段复杂性都不会大幅增加。确保每个提示都建立在前一个提示的基础上，并一起结束，不应有未集成到前一步中的挂起或孤立代码。
```
- 模型必须将提示计划格式化为一系列可以直接给LlM的实际提示。
- 计划中的每个提示都必须以命令形式写成，作为对法学硕士的直接指示
- 每个提示必须以“Prompt N:”开头，其中N是序列号
- 模型必须确保每个提示包括：
  - 明确的目标
  - 一般实施指南
##### 示例格式(truncated)：
```markdown
# 实施快速计划
## 检查表
- Prompt 1：设置项目结构和核心接口
- Prompt 2：实现数据模型和验证
- Prompt 3：创建存储机制
- Prompt 4：实现核心业务逻辑
- Prompt 5：添加API端点
- Prompt 6：实现身份验证和授权
- Prompt 7：添加错误处理和日志记录 
- Prompt 8：创建集成测试
- Prompt 9：将所有内容连接在一起
  ## 提示
  ### Prompt 1：设置项目结构和核心接口
  根据设计文档中定义的架构创建初始项目结构。实施定义系统边界的核心接口
  1.为模型、服务、存储库和ApI组件设置目录结构
  2.根据需求为核心数据模型创建接口定义
  3.建立一个基本的测试框架，并添加一个简单的烟雾测试
  4.为我们将要实现的主要模块创建占位符文件
  专注于建立一个干净的架构，明确分离关注点，不要实现任何业务逻辑，只需定义接口和结构。
  
  ### Prompt 2：实施数据模型和验证
  使用适当的验证规则实现设计文档中定义的数据模型。
  1、创建用户模型，验证需求中指定的必填字段
  2.使用设计中描述的关系实现文档模型
  3.为模型验证添加单元测试，以验证是否满足要求
  4.确保所有模型都实现提示1中定义的接口
  专注于强类型和验证，以确保整个系统的数据完整性。确保处理在需求澄清过程中发现的边缘情况。
[继续其他Prompts]
```
### 8.总结并展示结果
提供创建的所有工件和下一步的摘要。
#### 限制：
- 模型必须在{project_dir}/summary.md处创建摘要文档
- 模型必须列出在此过程中创建的所有工件
- 模型必须提供设计和实施计划的简要概述
- 模型必须为用户建议下一步行动
- 模型应突出显示可能需要进一步改进的任何领域
- 模型必须在对话中向用户呈现此摘要
### 示例
#### 示例输入
```text
粗略的想法：我想为我们团队的内部工具构建一个功能，允许用户为我们生成的许多文档创建和管理模板。用户可以创建、编辑和共享模板，以及使用它们生成具有自定义字段的新文档。
项目目录：docs/template-feature
```
#### 示例输出
```text
# 项目概述
我已经完成了将你的粗略想法转化为具有实施计划的详细设计。以下是创建的内容：
## 目录结构
- docs/template-feature/
- rough-idea.md（你最初的概念）
- idea-honing.md（我们的Q&A需求澄清）
- research/
    - existing-templates.md
    - storage-options.md
- desiqn/
    - detailed-design.md
- implementation/
    - prompt-plan.md（包括实施清单）
    - summary.md（本文档）

## 关键设计元素
- 具有CRUD操作的模板管理系统
- 基于角色的共享访问控制
- 模板版本控制系统
- 带有验证的自定义字段
- 文档生成引擎

##实施方法
实施计划将工作分解为12个增量步骤，从核心数据模型开始，逐步构建完整的功能集。
##下一步
1.在docs/template feature/design/detailed-design.md查看详细设计文件
2.在docs/template feature/eximplementation/primppt-plan.md查看实施计划和检查表
3.按照快速计划中的检查表开始实施
您想让我更详细地解释设计或实施计划的任何具体部分吗？
```

### 故障排除
#### 需求澄清停滞
如果需求澄清过程似乎在原地踏步或没有取得进展：
- 模型应该建议转移到需求的不同方面
- 该模型可以提供示例或选项来帮助用户做出决策。
- 该模型应总结迄今为止所建立的内容，并确定具体的差距
- 该模型可能建议进行研究，为需求决策提供信息
#### 研究局限性
如果模型无法访问所需的信息。
- 模型应记录缺失的信息
- 该模型应根据可用信息提出替代方法
- 模型可能会要求用户提供额外的上下文或文档
- 模型应继续使用可用信息，而不是阻止进度
#### 设计复杂性
如果设计变得过于复杂或难以操作：
- 模型应该建议将其分解为更小、更易于管理的组件
- 模型应首先关注核心功能
- 该模型可能建议分阶段实施
- 如果需要，模型应返回需求澄清，以确定功能的优先级
