# 接口(方法)信息服务
**使用此接口之前,请仔细阅读接口通用说明 OpenAPI.md**
接口名称:com.jd.jsf.open.api.InterfaceService

## getByInterfaceName: 根据接口名称查询接口信息

```java
/** *根据接口名称,查询接口信息-用户信息等*
*@paramqueryInterfaceRequest*@return*/
Result<InterfaceInfo>getByInterfaceName(QueryInterfaceRequest queryInterfaceRequest);
```

参数说明: QueryInterfaceRequest

| 字段         | 类型    | 必填 | 默认值 | 说明        |
| ---------- | ----- | -- | ---- | --------- |
| interfaceName | string | Y  | null | 接口名称,必填 |

Data返回值:

```json
{
    "total": 0,
    "code": 1,
    "data": {
        "serviceType": 1, # 服务类型, 1: JSF; 2: containerMesh; 默认: 1
        "supportType": 1, # 服务支持类型
        "consumerTotal": 7813,
        "src": 1, # 来源, 1-saf1.0, 2-saf2.1
        "departmentCode": "00007721",
        "crossLang": 0,
        "remark": "fop",
        "important": 0,
        "valid": 1, # 接口信息状态, 是否有效【0: 无效; 1: 已审核; 2: 新建(待提交); 3:
        "ownerUser": "minyuan, lixiaohe7, zhaochunming7, lijingshuai3, yanglin15,c",
        "createdTime": 1409821380000,
        "providerTotal": 37165,
        "appInvoke": 0,
        "id": 2004,
        "interfaceName": "com.jd.eclp.btp.taskfunnel.handler.Handler",
        "department": "技术发展部",
        "providerLive": 37165,
        "hasJsfClient": 1, # 是否有jsf客户端
        "consumerLive": 7813
    },
    "success": true
}
```

## getMethodInfo 获取接口对应的方法入参与出参信息

此方法最终通过telnet到存活的Provider，获取方法信息进行返回; (测试环境可能导致超时，
result={"code":5,"msg":"connect timed out", "success":false,"total":0})

注: 如果接口继承，可能获取不到父接口的方法(A继承了B接口，B接口里面的信息获取不到)
```java
/** 获取方法的详细信息 @paramqueryMethodInfoRequest@return@throwsException*/ 
Result<String>getMethodInfo(QueryMethodInfoRequest queryMethodInfoRequest) throws Exception;
```
参数说明: QueryMethodInfoRequest

| 字段           | 类型   | 必填 | 说明                                                                                                                                                                    |
| :------------- | :----- | :--- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| interfaceName  | string | Y    | 接口名称,必填                                                                                                                                                              |
| alias          | string | N    | 指定别名                                                                                                                                                                  |
| ip             | string | N    | 如果未指定ip,默认自动查询一个存活provider,使用该 provider 的ip: port,如果指定了ip那么同时也需要指定port                                                                      |
| port           | int    | N    | 参考ip说明                                                                                                                                                                 |
| methodDName    | string | N    | 如果接口方法特别多,建议指定方法名进行掉用,否侧可能返回的信息不全                                                                                                                |

Data返回值: 返回一个Json 字符串,需自行解析,该字符串由Provider解析Java class得到,开放api直接透传

## getMethod List:获取方法列表
```java
/** *获取方法列表**@paramqueryMethodInfoRequest*@return*@throws Exception*/
Result<List<String>>getMethodList(QueryMethodInfoRequest queryMethodInfoRequest)throws Exception;
```
参数与Data返回值说明:参考获取方法详情入参
