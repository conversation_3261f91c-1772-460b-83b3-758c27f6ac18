# 别名服务接口

使用此接口之前,请仔细阅读 OpenAPI.md
接口名:com.jd.jsf.open.api.ProviderAliaService

查询别名+动态别名

```java
/***<p>根据接口名获取别名列表</p>**入参说明: *---------------------------------------------------*
* interfaceName |必填*---------------------------------------------------*@return*/
Result<List<String>>getAliasByInterfaceName(QueryInterfaceRequest request);
```

参数说明:

| 字段            | 类型   | 必填 | 说明     |
| ------------- | ---- | -- | ------ |
| interfaceName | string | Y  | 接口名称   |

返回数据

```json
{
    "total":0,
    "code":1,
    "data":[
        "jsf-liush-demo",
        "jsftest-gw",
        "jsf-liush-test",
        "CHANGE-IT",
        "zjx-jsf-provider-test"
    ],
    "success":true
}
```