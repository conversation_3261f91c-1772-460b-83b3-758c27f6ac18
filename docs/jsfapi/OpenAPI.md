# OpenAPI

设置调用方应用的token信息, JSF 开放API调用需要使用token进行签名当做授权信息

**token修改位置:** [添加token](token自己定义)![](https://i.imgur.com/111.png)
**appKey:** [应用名称](https://i.imgur.com/444.png)

step 1: JSF调用方式

1.引入依赖包

```xml
1   <dependency>
2       <groupId>com.jd</groupId>
3       <artifactId>jsf-api</artifactId>
4       <version>2.2.85</version>
5   </dependency>
```

2.配置JSF consumer

1 `#Note:com.jd.jsf.open.api.InterfaceService 可以替换成其他接口`
2 `<jsf:consumer id="interfaceService" interface="com.jd.jsf.open.api.In
terfaceService" alias="jsf-open-api"/>`
 
### 3.JSF调用实示例
```java
1  QueryMethodInfoRequest request = new QueryMethodInfoRequest();
2  //参数有两部分组成,一部分称之为授权通用参数设置,可考虑抽取成一个公共的方法,
  另一部分是业务参数与具体接口相关
3  String appKey = "testAppName";//Your appName in Jsf admin
4  String token = "testAppToken"; //Your appName token
  如果线上环境可以在管理端app管理页面中点击修改按钮进行手动添加
5  String operator = "zhangsan";
6  request.setAppKey (appKey);
7  request.setOperator(operator);
8  request.setClientIp(NetUtils.getLocalHost());
9  request.setTimeStamp(System.currentTimeMillis());
10 request.setSign(request.sign(token)); //设置前面信息,使用request
  sign方法
11 //业务参数设置
12 request.setInterfaceName("com.jd.xxxxService");
13 Result<String> result = interfaceService.getMethodInfo(request); //
  获取所有方法的详情信息
14 if(result.isSuccess()){
15   String data = result.getData();
16  //解析结果
17 }
```

参数,返回值,Code码说明
Request参数说明:请求参数分两部分,一部分是通用参数,包含授权,操作人,调用信息,这
部分参数必传,否则认证失败,第二部分属于业务参数,在具体接口说明

| 字段      | 类型     | 说明                                                                 |
| --------- | -------- | -------------------------------------------------------------------- |
| appKey    | string   | 参考上面appKey申请说明                                                 |
| operator  | string   | 操作人erp                                                              |
| clientip  | string   | 调用者ip地址                                                           |
| timeStamp | long     | 当前时间的时间戳                                                       |
| sign      | string   | 签名信息 sign=md5(utf8(appKey+timeStamp+token))                        |

Resutl 说明

| 字段  | 类型   | 说明                                                                                                                                                                          |
| ----- | ------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| code  | int    | 返回code码,如果使用jsf调用,直接使用result对应isSuccess进行 判断即可                                                                                                                                      |
| msg   | string | 调用的返回消息                                                                                                                                                              |
| data  | object | 数据域,需要具体参考不通接口                                                                                                                                                     |
| total | int    | 当需要分页查询,包含数据的总数据                                                                                                                                                     |

Code码说明

| Code码 | 说明                   |
| ------ | ---------------------- |
| 1      | 调用成功               |
| 0      | 失败情况               |
| 3      | 参数不符合要求,可以参考msg返回内容 |
| -4     | 存在权限校验的情况下,应用或者operator无权限 |
| -6     | 传入的条件下,查询不到对应的记录,无法进行后续操作|
| 5      | 服务端异常,联系研发进行排查    |